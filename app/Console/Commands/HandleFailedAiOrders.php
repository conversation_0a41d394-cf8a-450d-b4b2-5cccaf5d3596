<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Models\AiFaceTask;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HandleFailedAiOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:handle-failed-ai {--dry-run : 只显示会被处理的订单，不实际执行}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '处理AI任务失败的订单，将其状态从ai_processing改为failed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('=== 干运行模式 - 只显示会被处理的订单 ===');
        } else {
            $this->info('开始处理AI任务失败的订单...');
        }

        // 查找状态为ai_processing的订单
        $orders = Order::where('status', Order::STATUS_AI_PROCESSING)->get();

        if ($orders->isEmpty()) {
            $this->info('没有找到状态为ai_processing的订单');
            return;
        }

        $this->info("找到 {$orders->count()} 个ai_processing状态的订单");

        $processedCount = 0;
        
        foreach ($orders as $order) {
            // 检查该订单相关的AI任务状态
            $failedTasks = AiFaceTask::where('user_id', $order->user_id)
                ->where('status', 'failed')
                ->where('created_at', '>=', $order->created_at)
                ->count();
                
            $processingTasks = AiFaceTask::where('user_id', $order->user_id)
                ->where('status', 'processing')
                ->where('created_at', '>=', $order->created_at)
                ->count();
                
            $completedTasks = AiFaceTask::where('user_id', $order->user_id)
                ->where('status', 'completed')
                ->where('created_at', '>=', $order->created_at)
                ->count();

            $this->info("订单 {$order->order_number} (ID: {$order->id}):");
            $this->info("  - 用户: {$order->user_id}");
            $this->info("  - 创建时间: {$order->created_at}");
            $this->info("  - 失败任务: {$failedTasks}");
            $this->info("  - 处理中任务: {$processingTasks}");
            $this->info("  - 完成任务: {$completedTasks}");

            // 如果有失败的任务且没有处理中的任务，则标记订单为失败
            if ($failedTasks > 0 && $processingTasks == 0) {
                if ($dryRun) {
                    $this->warn("  -> [干运行] 会将此订单标记为失败");
                } else {
                    try {
                        $order->update([
                            'status' => 'failed',
                            'updated_at' => Carbon::now()
                        ]);
                        
                        $processedCount++;
                        $this->info("  -> 订单已标记为失败");
                        
                        Log::info('处理AI失败订单', [
                            'order_id' => $order->id,
                            'order_number' => $order->order_number,
                            'user_id' => $order->user_id,
                            'failed_tasks' => $failedTasks,
                            'processing_tasks' => $processingTasks,
                            'completed_tasks' => $completedTasks
                        ]);
                        
                    } catch (\Exception $e) {
                        $this->error("  -> 处理订单失败: " . $e->getMessage());
                        
                        Log::error('处理AI失败订单出错', [
                            'order_id' => $order->id,
                            'order_number' => $order->order_number,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            } else {
                $this->info("  -> 跳过（仍有处理中的任务或无失败任务）");
            }
            
            $this->info('');
        }

        if ($dryRun) {
            $this->info("=== 干运行完成 ===");
        } else {
            $this->info("处理完成，共处理 {$processedCount} 个订单");
            Log::info("处理AI失败订单完成: 共处理 {$processedCount} 个订单");
        }
    }
}