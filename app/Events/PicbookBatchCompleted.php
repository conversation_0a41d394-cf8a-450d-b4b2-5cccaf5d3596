<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PicbookBatchCompleted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $batchId;
    public $succeededTasks;
    public $totalTasks;

    /**
     * Create a new event instance.
     */
    public function __construct(string $batchId, int $succeededTasks, int $totalTasks)
    {
        $this->batchId = $batchId;
        $this->succeededTasks = $succeededTasks;
        $this->totalTasks = $totalTasks;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('picbook-batch.' . $this->batchId),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'batch.completed';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'batch_id' => $this->batchId,
            'succeeded_tasks' => $this->succeededTasks,
            'total_tasks' => $this->totalTasks,
            'success_rate' => $this->totalTasks > 0 ? round(($this->succeededTasks / $this->totalTasks) * 100, 2) : 0,
            'completed_at' => now()->toISOString(),
        ];
    }
}