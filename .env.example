APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel_backend
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# 使用Reverb进行广播
BROADCAST_CONNECTION=reverb
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

# Reverb广播配置
REVERB_APP_ID=dreamazebook
REVERB_APP_KEY=reverb_app_key
REVERB_APP_SECRET=reverb_app_secret
REVERB_HOST=localhost
REVERB_PORT=8080
REVERB_SCHEME=http

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

STRIPE_SECRET=your_stripe_secret_key

# 4PX物流配置
4PX_BASE_URL=https://open.4px.com/router/api/service
4PX_APP_KEY=your_4px_app_key
4PX_APP_SECRET=your_4px_app_secret

# 绘本物理参数配置
PICBOOK_WEIGHT=500
PICBOOK_LENGTH=20
PICBOOK_WIDTH=15
PICBOOK_HEIGHT=2
PICBOOK_PACKAGING_WEIGHT=50
PICBOOK_PACKAGING_LENGTH=2
PICBOOK_PACKAGING_WIDTH=2
PICBOOK_PACKAGING_HEIGHT=1

# 物流费用配置
SHIPPING_COST_TOLERANCE=0.01
SHIPPING_ABNORMAL_THRESHOLD=100.00
SHIPPING_DEFAULT_CURRENCY=USD