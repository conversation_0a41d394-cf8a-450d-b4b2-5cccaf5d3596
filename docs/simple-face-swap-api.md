# Simple Face Swap API 文档

## 概述
Simple Face Swap 功能提供了完整的AI换脸服务，支持预览和整本书处理，包括队列管理、WebSocket通知和优先级处理。

## 功能特性

### ✅ 已实现的核心功能

1. **预览页面获取** - 返回用户整个绘本支持预览的页面
2. **文字合并处理** - 处理需要合并文字的绘本页
3. **换脸处理** - 处理需要换脸的绘本页
4. **队列处理** - 换脸接口工作流一次只能实现一个，通过队列处理
5. **队列状态** - 用户端可以看到队列排队数量和预估时间
6. **WebSocket通知** - 预览功能完成或失败时更新对应的任务和WS通知
7. **批次管理** - 预览可能存在多个换脸处理，整个完成时通知和更新预览数据
8. **顺序执行** - 单个预览执行完成后执行下一个预览
9. **优先级队列** - 用户下单完成后有优先级更高的队列，优先处理整本书

## API 接口

### 1. 获取预览页面
```http
GET /api/simple-face-swap/preview-pages
```

**请求参数：**
```json
{
  "picbook_id": 1,
  "language": "en",
  "gender": 1,
  "skincolor": 1
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "获取预览页面成功",
  "data": {
    "total_pages": 5,
    "face_swap_pages": 3,
    "text_merge_pages": 2,
    "pages": [
      {
        "page_id": 1,
        "page_number": 1,
        "has_question": false,
        "has_choice": false,
        "choice_type": 0,
        "image_url": "https://example.com/page1.jpg",
        "content": "页面内容",
        "question": null,
        "choice_options": null,
        "has_text": true,
        "has_face_swap": true,
        "character_sequence": [1, 2]
      }
    ],
    "face_swap_page_list": [...],
    "text_merge_page_list": [...]
  }
}
```

### 2. 创建预览换脸批次
```http
POST /api/simple-face-swap/create-by-picbook
```

**请求参数：**
```json
{
  "picbook_id": 1,
  "face_image": "https://example.com/face.jpg",
  "full_name": "张三",
  "language": "en",
  "gender": 1,
  "skincolor": 1
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "预览成功",
  "data": {
    "face": {
      "status": "success",
      "batch_id": "face_uuid-here",
      "task_ids": [1, 2, 3],
      "total_tasks": 3
    }
  }
}
```

### 3. 创建整本书换脸批次（下单后）
```http
POST /api/simple-face-swap/create-full-book
```

**请求参数：**
```json
{
  "picbook_id": 1,
  "face_image": "https://example.com/face.jpg",
  "full_name": "张三",
  "language": "en",
  "gender": 1,
  "skincolor": 1,
  "order_id": 123
}
```

### 4. 获取批次状态
```http
GET /api/simple-face-swap/{batchId}/status
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "batch_id": "face_uuid-here",
    "status": "processing",
    "progress": 66.67,
    "total_tasks": 3,
    "completed_tasks": 2,
    "failed_tasks": 0,
    "processing_tasks": 1,
    "pending_tasks": 0,
    "completed_results": [
      {
        "task_id": 1,
        "page_id": 1,
        "variant_id": 1,
        "result_image_url": "https://example.com/result1.jpg",
        "result": {
          "standard_url": "https://example.com/result1.jpg",
          "high_res_url": "https://example.com/result1_hd.jpg",
          "low_res_url": "https://example.com/result1_low.jpg"
        }
      }
    ],
    "is_priority": false,
    "created_at": "2025-07-19T10:00:00.000000Z",
    "updated_at": "2025-07-19T10:05:00.000000Z"
  }
}
```

### 5. 获取队列状态
```http
GET /api/simple-face-swap/queue/status
```

**响应示例：**
```json
{
  "success": true,
  "message": "获取队列状态成功",
  "data": {
    "queue_info": {
      "total_pending": 15,
      "total_processing": 2,
      "high_priority_pending": 3,
      "estimated_wait_time": 2040,
      "estimated_wait_time_formatted": "34分钟"
    },
    "user_tasks": [
      {
        "batch_id": "face_uuid-here",
        "status": "pending",
        "progress": 0,
        "created_at": "2025-07-19T10:00:00.000000Z",
        "is_priority": false
      }
    ],
    "user_tasks_count": 1
  }
}
```

### 6. 获取队列统计
```http
GET /api/queue/stats
```

### 7. 获取用户任务列表
```http
GET /api/queue/tasks
```

## WebSocket 通知

### 任务完成通知
```javascript
// 频道: user.{userId}
// 事件: face-swap.task.completed
{
  "type": "face_swap_task_completed",
  "batch_id": "face_uuid-here",
  "task_id": 1,
  "page_id": 1,
  "variant_id": 1,
  "result_image_url": "https://example.com/result.jpg",
  "result": {
    "standard_url": "https://example.com/result.jpg",
    "high_res_url": "https://example.com/result_hd.jpg",
    "low_res_url": "https://example.com/result_low.jpg"
  },
  "progress": 33.33,
  "timestamp": "2025-07-19T10:05:00.000Z"
}
```

### 任务失败通知
```javascript
// 频道: user.{userId}
// 事件: face-swap.task.failed
{
  "type": "face_swap_task_failed",
  "batch_id": "face_uuid-here",
  "task_id": 1,
  "page_id": 1,
  "variant_id": 1,
  "error_message": "换脸处理失败",
  "timestamp": "2025-07-19T10:05:00.000Z"
}
```

### 批次完成通知
```javascript
// 频道: user.{userId}
// 事件: face-swap.batch.completed
{
  "type": "face_swap_batch_completed",
  "batch_id": "face_uuid-here",
  "status": "completed",
  "progress": 100,
  "total_tasks": 3,
  "completed_tasks": 3,
  "is_priority": false,
  "results": [
    {
      "task_id": 1,
      "page_id": 1,
      "result_image_url": "https://example.com/result1.jpg",
      "result": {...}
    }
  ],
  "timestamp": "2025-07-19T10:10:00.000Z"
}
```

## 队列系统

### 队列类型
1. **普通队列** (`face_swap`) - 预览任务
2. **高优先级队列** (`high_priority_face_swap`) - 整本书任务（下单后）

### 处理逻辑
1. 每个批次一次只处理一个任务
2. 任务完成后自动处理下一个任务
3. 高优先级任务会优先处理
4. 支持任务失败重试（最多3次）

### 预估时间计算
- 每个任务平均处理时间：2分钟
- 预估等待时间 = (队列中任务数 + 处理中任务数) × 平均处理时间
- 高优先级任务会影响普通任务的等待时间

## 数据库表结构

### ai_face_tasks 表
```sql
- id: 主键
- batch_id: 批次ID
- user_id: 用户ID
- task_id: 第三方API任务ID
- type: 类型 (batch/task)
- status: 状态 (pending/processing/completed/failed)
- is_priority: 是否高优先级
- page_id: 页面ID
- variant_id: 变体ID
- target_image_url: 目标图片URL
- face_image_url: 人脸图片URL
- mask_image: 蒙版图片URL
- result_image_url: 结果图片URL
- result: 结果数据 (JSON)
- error_message: 错误信息
- progress: 进度百分比
- total_tasks: 总任务数
- completed_tasks: 已完成任务数
- character_sequence: 角色序列 (JSON)
- config: 配置信息 (JSON)
- task_index: 任务索引
- completed_at: 完成时间
- created_at: 创建时间
- updated_at: 更新时间
```

### picbook_previews 表
```sql
- id: 主键
- user_id: 用户ID
- picbook_id: 绘本ID
- batch_id: 批次ID
- status: 状态 (pending/completed/failed)
- face_image: 人脸图片URL
- language: 语言
- gender: 性别
- skin_color: 肤色 (JSON数组)
- face_swap_batch: 换脸批次信息 (JSON)
- result_images: 结果图片 (JSON)
- preview_count: 预览次数
- last_preview_at: 最后预览时间
- md5_key: 请求参数MD5
- created_at: 创建时间
- updated_at: 更新时间
```

## 使用流程

### 预览流程
1. 用户选择绘本和参数
2. 调用 `GET /api/simple-face-swap/preview-pages` 获取预览页面
3. 调用 `POST /api/simple-face-swap/create-by-picbook` 创建预览批次
4. 通过 WebSocket 监听任务进度通知
5. 调用 `GET /api/simple-face-swap/{batchId}/status` 获取最终结果

### 整本书处理流程
1. 用户完成下单
2. 调用 `POST /api/simple-face-swap/create-full-book` 创建高优先级批次
3. 系统自动优先处理整本书任务
4. 通过 WebSocket 监听处理进度
5. 完成后更新订单状态

### 队列监控流程
1. 调用 `GET /api/queue/stats` 获取队列统计
2. 调用 `GET /api/queue/tasks` 获取用户任务列表
3. 通过 WebSocket 实时监听队列变化

## 错误处理

### 常见错误码
- `422`: 参数验证失败
- `404`: 资源不存在
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "success": false,
  "error": "错误信息",
  "message": "用户友好的错误描述"
}
```

## 性能优化

1. **批次处理** - 避免同时处理大量任务
2. **队列延迟** - 任务间添加2秒延迟避免API频繁调用
3. **缓存机制** - 批次信息缓存1小时
4. **数据库优化** - 添加必要索引
5. **WebSocket优化** - 只向相关用户推送通知

## 监控和日志

### 关键日志
- 批次创建和完成
- 任务处理进度
- API调用状态
- 错误和异常

### 监控指标
- 队列长度
- 任务处理时间
- 成功/失败率
- 用户活跃度