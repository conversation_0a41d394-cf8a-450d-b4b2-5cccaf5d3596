# 订单地址功能API文档

## 概述

订单系统现在支持两种地址处理方式：
1. **地址ID方式**: 使用用户已保存的地址ID
2. **直接地址方式**: 直接传入完整的地址信息
3. **混合方式**: 收货地址和账单地址可以分别使用不同的方式

## API接口

### 1. 创建订单 (支持地址ID)

**接口**: `POST /api/order/create`

**请求参数**:

#### 方式一：使用地址ID
```json
{
    "cart_item_ids": [1, 2, 3],
    "shipping_address_id": 1,
    "billing_address_id": 2,
    "payment_method": "stripe",
    "shipping_method": "standard",
    "coupon_code": "DISCOUNT10",
    "notes": "请小心包装"
}
```

#### 方式二：直接传入地址信息
```json
{
    "cart_item_ids": [1, 2, 3],
    "shipping_address": {
        "first_name": "张",
        "last_name": "三",
        "company": "测试公司",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "post_code": "100000",
        "country": "CN",
        "state": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "street": "测试街道123号",
        "house_number": "1单元101室"
    },
    "billing_address": {
        "first_name": "李",
        "last_name": "四",
        "phone": "13900139000",
        "country": "CN",
        "city": "上海市",
        "street": "账单街道456号"
    },
    "payment_method": "stripe",
    "shipping_method": "standard"
}
```

#### 方式三：混合使用
```json
{
    "cart_item_ids": [1, 2, 3],
    "shipping_address_id": 1,
    "billing_address": {
        "first_name": "王",
        "last_name": "五",
        "phone": "13700137000",
        "country": "CN",
        "city": "广州市",
        "street": "账单街道789号"
    },
    "payment_method": "stripe"
}
```

#### 方式四：使用收货地址作为账单地址
```json
{
    "cart_item_ids": [1, 2, 3],
    "shipping_address_id": 1,
    "use_shipping_as_billing": true,
    "payment_method": "stripe"
}
```

**参数说明**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| cart_item_ids | array | 是 | 购物车项ID数组 |
| shipping_address_id | integer | 否 | 收货地址ID（与shipping_address二选一） |
| shipping_address | object | 否 | 收货地址信息（与shipping_address_id二选一） |
| billing_address_id | integer | 否 | 账单地址ID |
| billing_address | object | 否 | 账单地址信息 |
| use_shipping_as_billing | boolean | 否 | 是否使用收货地址作为账单地址 |
| payment_method | string | 是 | 支付方式：stripe, paypal |
| shipping_method | string | 否 | 配送方式：standard, speed |
| coupon_code | string | 否 | 优惠券代码 |
| notes | string | 否 | 订单备注 |

**地址对象字段**:

| 字段 | 类型 | 必填 | 长度限制 | 说明 |
|------|------|------|----------|------|
| first_name | string | 是 | 32 | 名字 |
| last_name | string | 否 | 32 | 姓氏 |
| company | string | 否 | 64 | 公司名称 |
| phone | string | 是 | 32 | 电话号码 |
| phone2 | string | 否 | 32 | 备用电话 |
| email | string | 否 | 32 | 邮箱地址 |
| post_code | string | 否 | 32 | 邮政编码 |
| country | string | 是 | 2 | 国家代码（ISO 3166-1） |
| state | string | 否 | 64 | 省/州 |
| city | string | 是 | 64 | 城市 |
| district | string | 否 | 64 | 区/县 |
| street | string | 是 | 64 | 街道地址 |
| house_number | string | 否 | 32 | 门牌号/房间号 |
| second_name | string | 否 | 32 | 备用姓名 |

**响应示例**:
```json
{
    "success": true,
    "code": 200,
    "message": "订单创建成功",
    "data": {
        "order": {
            "id": 123,
            "order_number": "PB20250127ABC123",
            "total_amount": 89.99,
            "currency_code": "USD",
            "status": "pending",
            "payment_status": "pending",
            "shipping_address": {
                "address_id": 1,
                "first_name": "张",
                "last_name": "三",
                "full_name": "张三",
                "phone": "13800138000",
                "full_address": "1单元101室, 测试街道123号, 朝阳区, 北京市, 北京市, 100000, CN"
            },
            "billing_address": {
                "address_id": 2,
                "first_name": "李",
                "last_name": "四",
                "full_name": "李四",
                "phone": "13900139000"
            }
        },
        "payment_url": "/api/stripe/create-checkout-session"
    }
}
```

### 2. 更新订单地址

**接口**: `PUT /api/order/update-address/{order_id}`

**请求参数**:

#### 更新收货地址（使用地址ID）
```json
{
    "shipping_address_id": 3
}
```

#### 更新账单地址（直接传入）
```json
{
    "billing_address": {
        "first_name": "赵",
        "last_name": "六",
        "phone": "13600136000",
        "country": "CN",
        "city": "深圳市",
        "street": "新账单地址"
    }
}
```

#### 同时更新收货和账单地址
```json
{
    "shipping_address_id": 3,
    "billing_address_id": 4
}
```

#### 使用收货地址作为账单地址
```json
{
    "shipping_address_id": 3,
    "use_shipping_as_billing": true
}
```

**响应示例**:
```json
{
    "success": true,
    "code": 200,
    "message": "订单地址更新成功",
    "data": {
        "id": 123,
        "order_number": "PB20250127ABC123",
        "shipping_address": {
            "address_id": 3,
            "first_name": "新收货人",
            "phone": "13500135000",
            "full_address": "新收货地址"
        },
        "billing_address": {
            "first_name": "新收货人",
            "phone": "13500135000",
            "full_address": "新收货地址"
        }
    }
}
```

### 3. 获取可用地址列表

**接口**: `GET /api/order/available-addresses`

**响应示例**:
```json
{
    "success": true,
    "code": 200,
    "message": "获取可用地址列表成功",
    "data": [
        {
            "id": 1,
            "type": 1,
            "full_name": "张三",
            "full_address": "1单元101室, 测试街道123号, 朝阳区, 北京市, 北京市, 100000, CN",
            "phone": "13800138000",
            "is_default": true,
            "formatted": {
                "address_id": 1,
                "first_name": "张",
                "last_name": "三",
                "phone": "13800138000",
                "country": "CN",
                "city": "北京市",
                "street": "测试街道123号"
            }
        },
        {
            "id": 2,
            "type": 2,
            "full_name": "李四",
            "full_address": "账单街道456号, 上海市, CN",
            "phone": "13900139000",
            "is_default": false,
            "formatted": {
                "address_id": 2,
                "first_name": "李",
                "last_name": "四",
                "phone": "13900139000",
                "country": "CN",
                "city": "上海市",
                "street": "账单街道456号"
            }
        }
    ]
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 422 | 参数验证失败 | 检查请求参数格式和必填字段 |
| 404 | 地址不存在 | 确认地址ID是否正确且属于当前用户 |
| 403 | 无权访问 | 确认地址属于当前登录用户 |
| 400 | 订单状态不允许修改 | 只有pending和processing状态的订单可以修改地址 |

### 错误响应示例

```json
{
    "success": false,
    "code": 422,
    "message": "参数验证失败",
    "errors": {
        "shipping_address.first_name": [
            "收货地址的名字字段是必填的"
        ],
        "shipping_address.country": [
            "国家代码必须是2位字符"
        ]
    }
}
```

## 使用场景

### 1. 新用户首次下单
- 直接传入地址信息
- 系统会自动保存地址供下次使用

### 2. 老用户快速下单
- 使用已保存的地址ID
- 提高下单效率

### 3. 企业用户
- 收货地址使用公司地址ID
- 账单地址直接传入财务部门信息

### 4. 订单地址修改
- 支付前可以修改收货地址
- 支持部分字段更新

## 最佳实践

### 1. 地址验证
- 创建订单前先验证地址格式
- 使用标准的国家代码
- 确保必填字段完整

### 2. 用户体验
- 优先使用用户的默认地址
- 提供地址自动补全功能
- 支持地址模板保存

### 3. 错误处理
- 提供清晰的错误提示
- 支持字段级别的错误显示
- 提供地址格式示例

### 4. 性能优化
- 缓存用户常用地址
- 批量验证地址信息
- 异步保存地址历史

## 安全考虑

1. **权限验证**: 确保用户只能访问自己的地址
2. **数据验证**: 严格验证地址格式和内容
3. **日志记录**: 记录地址修改操作
4. **敏感信息**: 适当脱敏显示地址信息

## 测试建议

1. **功能测试**: 测试各种地址组合方式
2. **边界测试**: 测试字段长度限制
3. **错误测试**: 测试各种错误场景
4. **性能测试**: 测试大量地址的处理性能