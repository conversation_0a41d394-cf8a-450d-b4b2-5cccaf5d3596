<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\ApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Helpers\FileHelper;
use App\Models\Admin;

class FileController extends ApiController
{
    /**
     * 前台用户文件上传
     * 
     * @authenticated
     * @header Authorization Bearer your-token-here
     * 
     * @bodyParam file file required 要上传的文件，最大5MB
     * @bodyParam type string required 文件类型，可选值：avatar,aiface,document
     * @bodyParam folder string optional 子文件夹路径
     * 
     * @response 201 {
     *   "url": "http://your-domain.com/storage/user_uploads/1/avatar/xxx.jpg",
     *   "path": "user_uploads/1/avatar/xxx.jpg",
     *   "original_name": "photo.jpg",
     *   "file_name": "uuid.jpg",
     *   "mime_type": "image/jpeg",
     *   "size": 1024,
     *   "extension": "jpg"
     * }
     */
    public function userUpload(Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'file' => [
                'required',
                'file',
                'max:5120', // 前台限制5MB
                'mimes:jpeg,png,jpg,gif,mp3,mp4,pdf,doc,docx,webp'
            ],
            'type' => 'required|string|in:avatar,aiface,document', // 前台文件类型
            // 'folder' => 'nullable|string|max:100'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', $validator->errors(), 422);
        }

        try {
            $file = $request->file('file');
            $type = $request->input('type');
            // $folder = $request->input('folder', '');
            $userId = Auth::id();

            // 获取文件信息
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $mimeType = $file->getMimeType();
            $size = $file->getSize();

            // 生成唯一文件名
            $fileName = Str::uuid() . '.' . $extension;

            // 构建存储路径（用户文件存储在 user_uploads 目录下）
            $path = trim("user_uploads/{$userId}/{$type}", '/');
            
            // 根据文件类型选择不同的存储
            // $s3Disk = $type === 'aiface' ? 's3_faceswap' : 's3_picbook';
            $s3Disk = 's3_faceswap';
            
            // 临时上传到本地作为后备方案
            $localPath = Storage::disk('public')->putFileAs(
                $path,
                $file,
                $fileName
            );
            $localUrl = Storage::disk('public')->url($localPath);
            
            // 尝试上传到S3
            try {
                // 存储文件
                $filePath = Storage::disk($s3Disk)->putFileAs(
                    $path,
                    $file,
                    $fileName
                );
                
                if ($filePath === false) {
                    throw new \Exception('S3上传失败');
                }

                // 确保存储路径正确
                $s3Path = $filePath; 

                // 生成访问URL - 使用配置中的S3基础URL
                $baseUrl = rtrim(config("filesystems.disks.{$s3Disk}.url"), '/');
                $url = $baseUrl . '/' . ltrim($s3Path, '/');
                
                $storage = $s3Disk;
            } catch (\Exception $s3e) {
                // S3上传失败，使用本地存储作为备选
                $filePath = $localPath;
                $url = $localUrl;
                $storage = 'public';
            }

            return $this->success([
                'url' => $url,
                'path' => $filePath,
                'original_name' => $originalName,
                'file_name' => $fileName,
                'mime_type' => $mimeType,
                'size' => $size,
                'extension' => $extension,
                'storage' => $storage // 添加存储类型标识
            ], '文件上传成功', 201);

        } catch (\Exception $e) {
            return $this->error('文件上传失败', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * 后台管理文件上传
     * 
     * @authenticated
     * @header Authorization Bearer your-admin-token-here
     * 
     * @bodyParam file file required 要上传的文件，最大20MB
     * @bodyParam type string required 文件类型，可选值：picbook,cover,content,resource
     * @bodyParam folder string optional 子文件夹路径
     */
    public function adminUpload(Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'file' => [
                'required',
                'file',
                'max:20480', // 后台限制20MB
                'mimes:jpeg,png,jpg,gif,mp3,mp4,wav,pdf,doc,docx,xls,xlsx,webp'
            ],
            'type' => 'required|string|in:picbook,cover,content,resource', // 后台文件类型
            'folder' => 'nullable|string|max:100'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', $validator->errors(), 422);
        }

        try {
            $file = $request->file('file');
            $type = $request->input('type');
            $folder = $request->input('folder', '');

            // 获取文件信息
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $mimeType = $file->getMimeType();
            $size = $file->getSize();

            // 生成唯一文件名
            $fileName = Str::uuid() . '.' . $extension;

            // 构建存储路径（管理文件存储在 admin_uploads 目录下）
            $path = trim("admin_uploads/{$type}/{$folder}", '/');
            
            // 临时上传到本地作为后备方案
            $localPath = Storage::disk('public')->putFileAs(
                $path,
                $file,
                $fileName,
                'public'
            );
            $localUrl = Storage::disk('public')->url($localPath);
            
            try {
                // 上传到S3
                $filePath = Storage::disk('s3_picbook')->putFileAs(
                    $path,
                    $file,
                    $fileName
                );
                
                if ($filePath === false) {
                    throw new \Exception('S3上传失败');
                }
                
                // 生成访问URL
                $baseUrl = rtrim(config('filesystems.disks.s3_picbook.url'), '/');
                $url = $baseUrl . '/' . ltrim($filePath, '/');
                $storage = 's3_picbook';
                
                // 验证上传是否成功
                if (!Storage::disk('s3_picbook')->exists($filePath)) {
                    throw new \Exception('S3上传文件验证失败');
                }
            } catch (\Exception $e) {
                // S3上传失败，使用本地存储作为备选
                $filePath = $localPath;
                $url = $localUrl;
                $storage = 'public';
            }

            return $this->success([
                'url' => $url,
                'path' => $filePath,
                'original_name' => $originalName,
                'file_name' => $fileName,
                'mime_type' => $mimeType,
                'size' => $size,
                'extension' => $extension,
                'storage' => $storage // 添加存储类型标识
            ], '文件上传成功', 201);
        } catch (\Exception $e) {
            return $this->error('文件上传失败', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * 删除文件
     * 
     * @authenticated
     * @header Authorization Bearer your-token-here
     * 
     * @bodyParam path string required 要删除的文件路径
     * @bodyParam storage string nullable 文件存储类型，可选值：public,s3,s3_picbook,s3_faceswap
     */
    public function destroy(Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'path' => 'required|string|max:255',
            'storage' => 'nullable|string|in:public,s3,s3_picbook,s3_faceswap' // 添加存储类型参数
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', $validator->errors(), 422);
        }

        try {
            $path = $request->input('path');
            $userId = Auth::id();
            // 默认使用public存储，但如果指定了其他存储则使用指定的
            $storage = $request->input('storage', 'public');
            
            // 检查文件是否存在
            if (!Storage::disk($storage)->exists($path)) {
                return $this->error('文件不存在', null, 404);
            }

            // 检查权限
            $user = auth()->user();
            $isAdmin = $user instanceof Admin;
            if (!$isAdmin) {
                // 普通用户只能删除自己的文件
                if (!str_starts_with($path, "user_uploads/{$userId}/")) {
                    return $this->error('没有权限删除此文件', null, 403);
                }
            }

            // 删除文件
            Storage::disk($storage)->delete($path);

            return $this->success(null, '文件已删除', 204);
        } catch (\Exception $e) {
            return $this->error('文件删除失败', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * 获取文件的预签名URL
     * 
     * @authenticated
     * @header Authorization Bearer your-token-here
     * 
     * @bodyParam path string required 文件路径
     * @bodyParam storage string nullable 存储类型，可选值：public,s3,s3_picbook,s3_faceswap
     * @bodyParam expires int nullable URL有效期(秒)，默认300秒(5分钟)
     * @bodyParam disposition string nullable 内容处理方式，可选值：inline,attachment，默认inline
     * 
     * @response {
     *   "success": true,
     *   "url": "https://bucket.s3.region.amazonaws.com/path/to/file.jpg?X-Amz-Algorithm=...",
     *   "expires_at": "2025-04-23T14:43:42+00:00"
     * }
     */
    public function getPresignedUrl(Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'path' => 'required|string|max:255',
            'storage' => 'nullable|string|in:public,s3,s3_picbook,s3_faceswap',
            'expires' => 'nullable|integer|min:60|max:86400',
            'disposition' => 'nullable|string|in:inline,attachment'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', $validator->errors(), 422);
        }

        try {
            $path = $request->input('path');
            $storage = $request->input('storage', 's3_picbook'); // 默认使用s3_picbook
            $expires = $request->input('expires', 300); // 默认5分钟
            $disposition = $request->input('disposition', 'inline');
            
            // 检查文件是否存在
            if (!Storage::disk($storage)->exists($path)) {
                return $this->error('文件不存在', null, 404);
            }
            
            // 检查权限 - 仅管理员可以获取任意文件的URL
            $userId = Auth::id();
            $user = auth()->user();
            $isAdmin = $user instanceof Admin;
            if (!$isAdmin) {
                // 普通用户只能获取自己上传的文件的URL
                if (str_starts_with($path, 'user_uploads/') && !str_starts_with($path, "user_uploads/{$userId}/")) {
                    return $this->error('没有权限获取此文件的URL', null, 403);
                }
                
                // 普通用户不能获取admin_uploads目录下的文件
                if (str_starts_with($path, 'admin_uploads/')) {
                    return $this->error('没有权限获取此文件的URL', null, 403);
                }
            }
            
            // 对于public存储，直接返回URL
            if ($storage === 'public') {
                $url = Storage::disk($storage)->url($path);
                return $this->success([
                    'url' => $url,
                    'expires_at' => null,
                    'note' => '公共存储URL，无过期时间'
                ], '获取URL成功');
            }
            
            // 使用FileHelper生成预签名URL
            $options = [];
            if ($disposition) {
                $filename = basename($path);
                if ($disposition === 'inline') {
                    $options['ResponseContentDisposition'] = 'inline';
                } else {
                    $options['ResponseContentDisposition'] = 'attachment; filename="' . $filename . '"';
                }
            }
            
            $url = FileHelper::getPresignedUrl($path, $storage, $expires, $options);
            
            if (!$url) {
                return $this->error('生成预签名URL失败', null, 500);
            }
            
            $expiresAt = now()->addSeconds($expires);
            
            return $this->success([
                'url' => $url,
                'expires_at' => $expiresAt->toIso8601String(),
                'path' => $path,
                'storage' => $storage
            ], '获取预签名URL成功');
        } catch (\Exception $e) {
            return $this->error('生成预签名URL失败', ['error' => $e->getMessage()], 500);
        }
    }
}