<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\AiFaceTask;

class FaceSwapTaskCompleted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $task;
    public $userId;

    /**
     * Create a new event instance.
     */
    public function __construct(AiFaceTask $task)
    {
        $this->task = $task;
        $this->userId = $task->user_id;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->userId)
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'face-swap.task.completed';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'type' => 'face_swap_task_completed',
            'batch_id' => $this->task->batch_id,
            'task_id' => $this->task->id,
            'page_id' => $this->task->page_id,
            'variant_id' => $this->task->variant_id,
            'result_image_url' => $this->task->result_image_url,
            'result' => $this->task->result,
            'progress' => $this->task->progress,
            'timestamp' => now()->toISOString()
        ];
    }
}