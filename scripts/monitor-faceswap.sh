#!/bin/bash

# 换脸服务监控脚本 - 适配 Horizon
# 建议通过 cron 每5分钟运行一次

LOG_FILE="/var/log/faceswap-monitor.log"
PROJECT_PATH="/path/to/your/project"

cd $PROJECT_PATH

echo "$(date): 开始换脸服务监控检查..." >> $LOG_FILE

# 1. 检查 Horizon 进程（替代原来的队列工作进程检查）
HORIZON_STATUS=$(sudo supervisorctl status horizon | grep RUNNING | wc -l)
if [ $HORIZON_STATUS -lt 1 ]; then
    echo "$(date): 警告 - Horizon 进程未运行" >> $LOG_FILE
    sudo supervisorctl restart horizon
    # 发送告警邮件或通知
    # mail -s "Horizon 进程告警" <EMAIL> < /dev/null
fi

# 2. 检查调度器进程
SCHEDULER_STATUS=$(sudo supervisorctl status laravel-scheduler | grep RUNNING | wc -l)
if [ $SCHEDULER_STATUS -lt 1 ]; then
    echo "$(date): 警告 - 调度器进程未运行" >> $LOG_FILE
    sudo supervisorctl restart laravel-scheduler
fi

# 3. 检查 Horizon 队列健康状态
HORIZON_HEALTH=$(php artisan horizon:status | grep -c "active" || echo "0")
if [ $HORIZON_HEALTH -lt 1 ]; then
    echo "$(date): 警告 - Horizon 队列状态异常" >> $LOG_FILE
    php artisan horizon:terminate
fi

# 4. 检查换脸队列积压
PENDING_BATCHES=$(php artisan tinker --execute="echo App\Models\AiFaceTask::where('type', 'batch')->where('status', 'pending')->count();")
if [ $PENDING_BATCHES -gt 20 ]; then
    echo "$(date): 警告 - 换脸队列积压严重: $PENDING_BATCHES 个待处理批次" >> $LOG_FILE
fi

# 5. 检查高优先级队列积压
HIGH_PRIORITY_JOBS=$(php artisan tinker --execute="echo \Illuminate\Support\Facades\Redis::llen('queues:high_priority_face_swap');")
if [ $HIGH_PRIORITY_JOBS -gt 10 ]; then
    echo "$(date): 警告 - 高优先级队列积压: $HIGH_PRIORITY_JOBS 个任务" >> $LOG_FILE
fi

# 6. 检查超时任务
php artisan faceswap:cleanup-timeout --timeout=30 >> $LOG_FILE 2>&1

# 7. 生成统计报告（每小时一次）
MINUTE=$(date +%M)
if [ $MINUTE -eq 0 ]; then
    php artisan faceswap:queue-stats --save >> $LOG_FILE 2>&1
fi

echo "$(date): 换脸服务监控检查完成" >> $LOG_FILE