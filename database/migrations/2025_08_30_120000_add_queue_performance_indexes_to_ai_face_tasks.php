<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ai_face_tasks', function (Blueprint $table) {
            // 添加队列查询优化索引
            $table->index(['status', 'type', 'is_priority', 'created_at'], 'idx_queue_processing');
            $table->index(['user_id', 'status', 'type'], 'idx_user_tasks');
            $table->index(['batch_id', 'status'], 'idx_batch_status');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index(['is_priority', 'status', 'created_at'], 'idx_priority_queue');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ai_face_tasks', function (Blueprint $table) {
            $table->dropIndex('idx_queue_processing');
            $table->dropIndex('idx_user_tasks');
            $table->dropIndex('idx_batch_status');
            $table->dropIndex('idx_status_created');
            $table->dropIndex('idx_priority_queue');
        });
    }
};