<?php

namespace App\Services;

use Exception;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Image;
use Illuminate\Support\Facades\Log;

/**
 * 前端一致性图像处理器
 * 使用与前端 LayerComposer.tsx 完全一致的逐像素算法
 */
class FrontendConsistentProcessor
{
    protected $imageManager;
    
    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }
    
    /**
     * 应用滤镜 - 与前端算法完全一致
     * 
     * @param Image $image 图像对象
     * @param array $filters 滤镜数组
     * @return Image
     */
    public function applyFilters(Image $image, array $filters): Image
    {
        // 获取图像数据
        $core = $image->core();
        
        // 如果是 GD 驱动，使用逐像素处理
        if (method_exists($core, 'native')) {
            $gdImage = $core->native();
            $this->processPixels($gdImage, $filters);
        } else {
            // 回退到其他处理方式
            Log::warning('无法使用逐像素处理，使用备用方法');
            $image = $this->fallbackProcess($image, $filters);
        }
        
        return $image;
    }
    
    /**
     * 逐像素处理 - 与前端算法完全一致
     */
    protected function processPixels(&$gdImage, array $filters)
    {
        $width = imagesx($gdImage);
        $height = imagesy($gdImage);
        
        for ($y = 0; $y < $height; $y++) {
            for ($x = 0; $x < $width; $x++) {
                // 获取像素颜色和透明度
                $colorIndex = imagecolorat($gdImage, $x, $y);
                $alpha = ($colorIndex >> 24) & 0x7F;
                
                // 只处理非透明像素
                if ($alpha < 127) {
                    $r = ($colorIndex >> 16) & 0xFF;
                    $g = ($colorIndex >> 8) & 0xFF;
                    $b = $colorIndex & 0xFF;
                    
                    // 应用滤镜
                    [$r, $g, $b] = $this->applyPixelFilters($r, $g, $b, $filters);
                    
                    // 重新设置像素颜色
                    $newColor = imagecolorallocatealpha($gdImage, $r, $g, $b, $alpha);
                    imagesetpixel($gdImage, $x, $y, $newColor);
                }
            }
        }
    }
    
    /**
     * 对单个像素应用滤镜 - 与前端算法完全一致
     */
    protected function applyPixelFilters($r, $g, $b, array $filters): array
    {
        // 应用饱和度调整
        if (isset($filters['saturate'])) {
            $saturateValue = floatval($filters['saturate']);
            $gray = 0.299 * $r + 0.587 * $g + 0.114 * $b;
            $r = min(255, max(0, $gray + $saturateValue * ($r - $gray)));
            $g = min(255, max(0, $gray + $saturateValue * ($g - $gray)));
            $b = min(255, max(0, $gray + $saturateValue * ($b - $gray)));
        }
        
        // 应用色相调整
        if (isset($filters['hue'])) {
            $hueValue = floatval($filters['hue']);
            $hueRad = $hueValue * M_PI / 180;
            $newR = $r * cos($hueRad) - $g * sin($hueRad);
            $newG = $r * sin($hueRad) + $g * cos($hueRad);
            $r = min(255, max(0, $newR));
            $g = min(255, max(0, $newG));
        }
        
        // 应用亮度调整
        if (isset($filters['brightness'])) {
            $brightnessValue = floatval($filters['brightness']);
            $r = min(255, max(0, $r + $brightnessValue));
            $g = min(255, max(0, $g + $brightnessValue));
            $b = min(255, max(0, $b + $brightnessValue));
        }
        
        // 应用对比度调整
        if (isset($filters['contrast'])) {
            $contrastValue = floatval($filters['contrast']);
            $factor = (259 * ($contrastValue + 255)) / (255 * (259 - $contrastValue));
            $r = min(255, max(0, $factor * ($r - 128) + 128));
            $g = min(255, max(0, $factor * ($g - 128) + 128));
            $b = min(255, max(0, $factor * ($b - 128) + 128));
        }
        
        return [(int)$r, (int)$g, (int)$b];
    }
    
    /**
     * 备用处理方法（当无法逐像素处理时使用）
     */
    protected function fallbackProcess(Image $image, array $filters): Image
    {
        foreach ($filters as $filter => $value) {
            switch ($filter) {
                case 'brightness':
                    $image->brightness((int)$value);
                    break;
                case 'contrast':
                    $image->contrast((int)$value);
                    break;
                case 'saturate':
                    // 使用 colorize 作为饱和度的近似
                    $intValue = (int)$value;
                    $image->colorize($intValue, $intValue, $intValue);
                    break;
                case 'hue':
                    // 使用 colorize 作为色相的近似
                    $hueValue = (int)$value;
                    $angle = $hueValue * M_PI / 180;
                    $r = (int)(15 * cos($angle));
                    $g = (int)(15 * cos($angle - 2 * M_PI / 3));
                    $b = (int)(15 * cos($angle + 2 * M_PI / 3));
                    $image->colorize($r, $g, $b);
                    break;
            }
        }
        
        return $image;
    }
    
    /**
     * 测试处理器 - 处理测试图片
     */
    public function processTestImage($inputPath, $outputPath, array $filters)
    {
        try {
            if (!file_exists($inputPath)) {
                throw new Exception("输入文件不存在: {$inputPath}");
            }
            
            $image = $this->imageManager->read($inputPath);
            $processedImage = $this->applyFilters($image, $filters);
            
            // 确保输出目录存在
            $outputDir = dirname($outputPath);
            if (!is_dir($outputDir)) {
                mkdir($outputDir, 0755, true);
            }
            
            $processedImage->save($outputPath, 95);
            
            return [
                'success' => true,
                'output_path' => $outputPath,
                'message' => '处理完成'
            ];
            
        } catch (Exception $e) {
            Log::error('测试图片处理失败', [
                'error' => $e->getMessage(),
                'input_path' => $inputPath,
                'filters' => $filters
            ]);
            
            return [
                'success' => false,
                'message' => '处理失败: ' . $e->getMessage()
            ];
        }
    }
}