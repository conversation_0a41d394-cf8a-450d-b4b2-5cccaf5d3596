<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 执行迁移
     */
    public function up(): void
    {
        // 创建用户问答记录表
        Schema::create('picbook_user_answers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('picbook_id')->constrained()->onDelete('cascade');
            $table->foreignId('page_id')->nullable()->constrained('picbook_pages')->onDelete('cascade');
            $table->json('answers')->comment('用户回答JSON数据');
            $table->timestamps();
            
            // 索引提高查询性能
            $table->index(['user_id', 'picbook_id']);
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('picbook_user_answers');
    }
}; 