<?php

// 测试广播认证的脚本

$baseUrl = 'https://api.dreamazebook.com';

// 1. 先登录获取token
$loginData = [
    'email' => '<EMAIL>', // 使用你的测试用户邮箱
    'password' => '12345678' // 使用你的测试用户密码
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/auth/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$loginResponse = curl_exec($ch);
$loginData = json_decode($loginResponse, true);

if (!$loginData || !isset($loginData['data']['token'])) {
    echo "登录失败: " . $loginResponse . "\n";
    exit(1);
}

$token = $loginData['data']['token'];
$userId = $loginData['data']['user']['id'];
echo "登录成功，用户ID: $userId，获取到token: " . substr($token, 0, 20) . "...\n";

// 2. 测试广播认证
$broadcastData = [
    'channel_name' => "face-swap.$userId", // 使用正确的用户ID
    'socket_id' => '123.456'
];

curl_setopt($ch, CURLOPT_URL, $baseUrl . '/api/broadcasting/auth');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($broadcastData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: Bearer ' . $token
]);

$broadcastResponse = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

curl_close($ch);

echo "广播认证响应 (HTTP $httpCode): " . $broadcastResponse . "\n";

if ($httpCode === 200) {
    echo "✅ 广播认证成功！\n";
} else {
    echo "❌ 广播认证失败\n";
}