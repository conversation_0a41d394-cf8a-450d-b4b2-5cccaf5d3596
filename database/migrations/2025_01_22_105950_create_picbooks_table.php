<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('picbooks', function (Blueprint $table) {
            $table->id();
            $table->string('default_name');  // 默认名称（通常是英文）
            $table->string('default_cover'); // 默认封面（通常是英文男性版本）
            $table->string('pricesymbol');
            $table->decimal('price', 10, 2);
            $table->string('currencycode', 3);
            $table->integer('total_pages');
            $table->integer('preview_pages_count')->default(7);
            $table->integer('character_count')->default(1);
            $table->decimal('rating', 3, 2)->default(5.00);
            $table->json('supported_languages');  // ['en', 'zh']
            $table->json('supported_genders');    // [1, 2]
            $table->json('supported_skincolors'); // [1, 2, 3]
            $table->json('tags')->nullable();
            $table->boolean('has_choices')->default(false);  // 是否包含8选4
            $table->tinyInteger('choices_type')->default(0);  // 选择类型
            $table->boolean('has_question')->default(false);  // 是否包含问答
            $table->tinyInteger('status')->default(0);  // 状态：草稿、已发布、已归档等
            $table->integer('batch_processing_status')->default(0)->comment('批量处理状态 0:未处理 1:处理中 2:处理完成 3:处理失败');
            
            $table->timestamps();
            $table->softDeletes();  // 添加软删除
        });

        // 创建绘本变体表
        Schema::create('picbook_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('picbook_id')->constrained()->onDelete('cascade');
            $table->string('language', 2)->comment('语言代码，如：en, zh');
            $table->tinyInteger('gender')->comment('性别：1-男，2-女');
            $table->tinyInteger('skincolor')->comment('肤色：1-白，2-黄，3-黑');
            $table->string('bookname', 255);
            $table->string('character_url', 255)->comment('角色图片URL');
            $table->text('intro')->nullable()->comment('简介');
            $table->text('description')->nullable()->comment('详细描述');
            $table->string('cover')->comment('封面图片URL');
            $table->json('tags')->nullable()->comment('变体特定标签');
            $table->tinyInteger('status')->default(1)->comment('状态：0-草稿，1-已发布，2-已下架');
            $table->timestamps();
            $table->softDeletes();

            // 一个绘本的每种语言、性别和肤色组合必须唯一
            $table->unique(['picbook_id', 'language', 'gender', 'skincolor'], 'unique_variant');
            
            // 添加索引以提高查询性能
            $table->index(['language', 'gender', 'skincolor','deleted_at']);
            $table->index('status');
        });

        // 创建绘本封面变体表
        Schema::create('picbook_cover_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('picbook_id')->constrained()->onDelete('cascade');
            $table->string('language', 2)->comment('语言代码');
            $table->integer('gender')->nullable()->comment('性别：1男，2女');
            $table->integer('skincolor')->nullable()->comment('肤色：1白，2黄，3黑');
            $table->string('image_url')->nullable()->comment('图片URL');
            $table->string('skin_mask_url')->nullable();
            $table->boolean('has_text')->default(false);
            $table->json('text_config')->nullable();
            $table->boolean('has_face')->default(false);
            $table->json('face_config')->nullable();
            $table->boolean('is_published')->default(false);
            $table->integer('sort_order')->default(0);
            $table->decimal('price', 8, 2)->nullable();
            $table->string('pricesymbol', 10)->nullable();
            $table->string('currencycode', 3)->nullable();
            $table->boolean('is_default')->default(false);
            $table->timestamps();
            $table->softDeletes();
            
            // 联合唯一索引确保每本绘本每种语言、性别、肤色组合只有一个封面变体
            $table->unique(['picbook_id', 'language', 'gender', 'skincolor'], 'picbook_cover_variant_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('picbooks');
        Schema::dropIfExists('picbook_variants');
        Schema::dropIfExists('picbook_cover_variants');
    }
};
