<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserAddress;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class AddressController extends Controller
{
    /**
     * 获取用户地址列表
     */
    public function index(Request $request): JsonResponse
    {
        $addresses = $request->user()->addresses()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $addresses
        ]);
    }

    /**
     * 创建新地址
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => 'required|in:1,2',
            'first_name' => 'required|string|max:32',
            'last_name' => 'nullable|string|max:32',
            'company' => 'nullable|string|max:64',
            'phone' => 'required|string|max:32',
            'phone2' => 'nullable|string|max:32',
            'email' => 'nullable|email|max:32',
            'post_code' => 'nullable|string|max:32',
            'country' => 'required|string|max:10',
            'state' => 'nullable|string|max:64',
            'city' => 'required|string|max:64',
            'district' => 'nullable|string|max:64',
            'street' => 'required|string|max:64',
            'house_number' => 'nullable|string|max:32',
            'second_name' => 'nullable|string|max:32',
            'is_default' => 'boolean'
        ]);

        $validated['user_id'] = $request->user()->id;
        $user = $request->user();
        $isDefault = $validated['is_default'] ?? false;

        // 检查用户是否已有地址
        $hasExistingAddresses = $user->addresses()->exists();

        // 如果用户没有地址，自动设置为默认地址
        if (!$hasExistingAddresses) {
            $validated['is_default'] = true;
            $isDefault = true;
        }

        // 如果要设置为默认地址，先将其他地址设为非默认
        if ($isDefault) {
            UserAddress::where('user_id', $user->id)
                ->update(['is_default' => false]);
        }

        // 创建地址
        $address = UserAddress::create($validated);

        return response()->json([
            'success' => true,
            'message' => '地址创建成功',
            'data' => $address->fresh()
        ], 201);
    }

    /**
     * 获取单个地址详情
     */
    public function show(Request $request, $id): JsonResponse
    {
        $address = $request->user()->addresses()->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $address
        ]);
    }

    /**
     * 更新地址
     */
    public function update(Request $request, $id): JsonResponse
    {
        $address = $request->user()->addresses()->findOrFail($id);

        $validated = $request->validate([
            'first_name' => 'required|string|max:32',
            'last_name' => 'nullable|string|max:32',
            'company' => 'nullable|string|max:64',
            'phone' => 'required|string|max:32',
            'phone2' => 'nullable|string|max:32',
            'email' => 'nullable|email|max:32',
            'post_code' => 'nullable|string|max:32',
            'country' => 'required|string|max:10',
            'state' => 'nullable|string|max:64',
            'city' => 'required|string|max:64',
            'district' => 'nullable|string|max:64',
            'street' => 'required|string|max:64',
            'house_number' => 'nullable|string|max:32',
            'second_name' => 'nullable|string|max:32',
            'is_default' => 'boolean'
        ]);

        $isDefault = $validated['is_default'] ?? false;

        // 如果要设置为默认地址，先将其他地址设为非默认
        if ($isDefault && !$address->is_default) {
            UserAddress::where('user_id', $request->user()->id)
                ->where('id', '!=', $address->id)
                ->update(['is_default' => false]);
        }

        // 更新地址
        $address->update($validated);

        return response()->json([
            'success' => true,
            'message' => '地址更新成功',
            'data' => $address->fresh()
        ]);
    }

    /**
     * 删除地址
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        $address = $request->user()->addresses()->findOrFail($id);

        // 如果删除的是默认地址，需要处理
        $wasDefault = $address->is_default;

        $address->delete();

        // 如果删除的是默认地址，将第一个地址设为默认
        if ($wasDefault) {
            $firstAddress = $request->user()->addresses()->first();
            if ($firstAddress) {
                $firstAddress->setAsDefault();
            }
        }

        return response()->json([
            'success' => true,
            'message' => '地址删除成功'
        ]);
    }

    /**
     * 设置默认地址
     */
    public function setDefault(Request $request, $id): JsonResponse
    {
        $address = $request->user()->addresses()->findOrFail($id);

        // 如果已经是默认地址，直接返回
        if ($address->is_default) {
            return response()->json([
                'success' => true,
                'message' => '该地址已是默认地址',
                'data' => $address
            ]);
        }

        $address->setAsDefault();

        return response()->json([
            'success' => true,
            'message' => '默认地址设置成功',
            'data' => $address->fresh()
        ]);
    }

    /**
     * 获取默认地址
     */
    public function getDefault(Request $request): JsonResponse
    {
        $defaultAddress = $request->user()->defaultAddress;

        if (!$defaultAddress) {
            return response()->json([
                'success' => false,
                'message' => '未找到默认地址'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $defaultAddress
        ]);
    }
}
