<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('picbook_pages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('picbook_id')->constrained()->onDelete('cascade')->comment('绘本ID');
            $table->integer('page_number')->comment('页码');
            $table->string('image_url')->comment('页面图片URL');
            $table->tinyInteger('status')->default(1)->comment('状态: 0-禁用, 1-启用');
            $table->boolean('is_ai_face')->default(false)->comment('是否需要AI换脸');
            $table->string('mask_image_url')->nullable()->comment('AI换脸遮罩图片URL');
            $table->boolean('has_replaceable_text')->default(false)->comment('是否包含可替换文字');
            $table->json('text_elements')->nullable()->comment('可替换文字配置');
            $table->json('character_sequence')->nullable()->comment('角色序列，如[2,1]表示从左到右的角色顺序');
            $table->boolean('has_question')->default(false)->comment('是否包含问题');
            $table->boolean('has_choice')->default(false)->comment('是否包含选择');
            $table->integer('choice_type')->default(0)->comment('选择类型: 0-无选择 1-单选 2-多选');
            $table->boolean('is_preview')->default(false)->comment('是否为预览页');
            $table->timestamps();
            $table->softDeletes();

            // 索引优化
            $table->index(['picbook_id', 'page_number']); // 常用查询组合
            $table->index(['status', 'created_at']); // 状态筛选
        });

        // 创建绘本页面变体表
        Schema::create('picbook_page_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('page_id')->comment('关联的页面ID')->constrained('picbook_pages')->onDelete('cascade');
            $table->string('language', 5)->comment('语言代码(如: zh-CN, en-US)');
            $table->tinyInteger('gender')->default(1)->comment('性别: 1-男, 2-女');
            $table->tinyInteger('skincolor')->default(1)->comment('肤色：1白，2黄，3黑');
            $table->json('character_skincolors')->nullable()->comment('多角色肤色组合，如[1,2]表示第一个角色白肤色，第二个角色黄肤色');
            $table->string('character_skincolors_hash')->virtualAs(
                DB::raw('MD5(COALESCE(character_skincolors, "[]"))')
            )->comment('character_skincolors的哈希值，用于唯一性约束');
            $table->string('image_url')->comment('页面图片URL');
            $table->string('skin_mask_url')->nullable();
            $table->boolean('has_text')->default(false);
            $table->json('text_config')->nullable();
            $table->boolean('has_face')->default(false);
            $table->json('face_config')->nullable();
            $table->boolean('is_published')->default(true);
            $table->json('elements')->nullable()->comment('页面元素位置配置');
            $table->json('text_elements')->nullable()->comment('可替换文本元素配置');
            $table->integer('variant_type')->default(0)->comment('0:普通页 1:问答页 2:选择页');
            $table->boolean('is_preview_variant')->default(false)->comment('是否为预览变体');
            $table->text('content')->nullable()->comment('页面文本内容');
            $table->json('choice_options')->nullable()->comment('选择选项');
            $table->string('question')->nullable()->comment('问题');
            $table->json('character_masks')->nullable()->comment('每个角色位置对应的蒙版图片URL，用于换肤');
            $table->timestamps();
            $table->softDeletes();

            // 移除旧的唯一性约束
            // $table->unique(['page_id', 'language', 'gender'], 'unique_page_variant');
            // 添加新的唯一性约束，使用生成列
            $table->unique(['page_id', 'language', 'gender', 'character_skincolors_hash'], 'unique_page_variant');
            $table->index('language');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('picbook_page_variants');
        Schema::dropIfExists('picbook_pages');
    }
};
