<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */
    'paths' => ['api/*', 'sanctum/csrf-cookie'], // 限制跨域路径
    'allowed_methods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'], // 允许所有常用方法
    'allowed_origins' => ['*'], // 允许所有来源，生产环境应该限制为特定域名
    'allowed_origins_patterns' => [],
    'allowed_headers' => ['Content-Type', 'X-Requested-With', 'Authorization', 'X-CSRF-TOKEN', 'Accept', 'Origin'], // 更多请求头
    'exposed_headers' => ['Content-Disposition'], // 暴露的响应头
    'max_age' => 86400, // 缓存时间，单位秒（1天）
    'supports_credentials' => true, // 支持凭证（cookies, authorization headers等）



];
