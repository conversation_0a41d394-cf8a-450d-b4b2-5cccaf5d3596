<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AiFaceTask;
use App\Services\SimpleFaceSwapService;
use Illuminate\Support\Facades\Log;

class QueueController extends Controller
{
    protected $faceSwapService;

    public function __construct(SimpleFaceSwapService $faceSwapService)
    {
        $this->faceSwapService = $faceSwapService;
    }

    /**
     * 获取队列统计信息
     */
    public function getQueueStats(Request $request)
    {
        try {
            $userId = $request->user()->id;
            
            // 获取队列统计
            $stats = [
                'total_pending' => AiFaceTask::where('status', 'pending')->where('type', 'task')->count(),
                'total_processing' => AiFaceTask::where('status', 'processing')->where('type', 'task')->count(),
                'high_priority_pending' => AiFaceTask::where('status', 'pending')
                    ->where('type', 'task')
                    ->where('is_priority', true)
                    ->count(),
                'normal_priority_pending' => AiFaceTask::where('status', 'pending')
                    ->where('type', 'task')
                    ->where('is_priority', false)
                    ->count(),
                'user_pending' => AiFaceTask::where('user_id', $userId)
                    ->where('status', 'pending')
                    ->where('type', 'task')
                    ->count(),
                'user_processing' => AiFaceTask::where('user_id', $userId)
                    ->where('status', 'processing')
                    ->where('type', 'task')
                    ->count()
            ];

            // 计算预估等待时间
            $averageProcessingTime = 120; // 每个任务平均2分钟
            $userPosition = $this->calculateUserPosition($userId);
            $estimatedWaitTime = $userPosition * $averageProcessingTime;

            return response()->json([
                'success' => true,
                'data' => [
                    'queue_stats' => $stats,
                    'user_position' => $userPosition,
                    'estimated_wait_time' => $estimatedWaitTime,
                    'estimated_wait_time_formatted' => $this->formatTime($estimatedWaitTime)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取队列统计失败', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => '获取队列统计失败'
            ], 500);
        }
    }

    /**
     * 获取用户的任务列表
     */
    public function getUserTasks(Request $request)
    {
        try {
            $userId = $request->user()->id;
            
            $tasks = AiFaceTask::where('user_id', $userId)
                ->where('type', 'batch')
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get()
                ->map(function ($task) {
                    return [
                        'batch_id' => $task->batch_id,
                        'status' => $task->status,
                        'progress' => $task->progress ?? 0,
                        'total_tasks' => $task->total_tasks,
                        'completed_tasks' => $task->completed_tasks,
                        'is_priority' => $task->is_priority,
                        'created_at' => $task->created_at,
                        'updated_at' => $task->updated_at
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $tasks
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户任务列表失败', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => '获取任务列表失败'
            ], 500);
        }
    }

    /**
     * 计算用户在队列中的位置
     */
    private function calculateUserPosition(int $userId)
    {
        // 获取用户最早的待处理任务
        $userEarliestTask = AiFaceTask::where('user_id', $userId)
            ->where('status', 'pending')
            ->where('type', 'task')
            ->orderBy('created_at')
            ->first();

        if (!$userEarliestTask) {
            return 0;
        }

        // 计算在该任务之前有多少个任务
        $position = AiFaceTask::where('status', 'pending')
            ->where('type', 'task')
            ->where('created_at', '<', $userEarliestTask->created_at)
            ->count();

        // 高优先级任务会被优先处理
        $highPriorityBefore = AiFaceTask::where('status', 'pending')
            ->where('type', 'task')
            ->where('is_priority', true)
            ->where('created_at', '>', $userEarliestTask->created_at)
            ->count();

        return $position + $highPriorityBefore + 1;
    }

    /**
     * 格式化时间
     */
    private function formatTime(int $seconds)
    {
        if ($seconds < 60) {
            return $seconds . '秒';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            return $minutes . '分钟';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return $hours . '小时' . ($minutes > 0 ? $minutes . '分钟' : '');
        }
    }
}