<?php

namespace App\Listeners;

use App\Events\OrderPaid;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use App\Services\BookService;
use App\Jobs\GenerateOrderBooks;

class OrderPaidListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 绘本生成服务
     */
    protected $bookService;
    
    /**
     * 创建监听器实例
     *
     * @param BookService $bookService
     * @return void
     */
    public function __construct(BookService $bookService)
    {
        $this->bookService = $bookService;
    }

    /**
     * 处理事件
     *
     * @param  \App\Events\OrderPaid  $event
     * @return void
     */
    public function handle(OrderPaid $event)
    {
        try {
            $order = $event->order;
            
            Log::info('订单支付完成，准备生成绘本', [
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'payment_id' => $order->payment_id
            ]);
            
            // 将绘本生成任务分派到队列中
            GenerateOrderBooks::dispatch($order);
            
            Log::info('已分派绘本生成任务', [
                'order_id' => $order->id
            ]);
        } catch (\Exception $e) {
            Log::error('处理订单支付完成事件异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $event->order->id ?? 'unknown'
            ]);
        }
    }
} 