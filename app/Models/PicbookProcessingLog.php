<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PicbookProcessingLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'picbook_id',
        'process_type',
        'status',
        'message',
        'details'
    ];

    protected $casts = [
        'details' => 'array',
        'status' => 'integer'
    ];

    /**
     * 获取关联的绘本
     */
    public function picbook()
    {
        return $this->belongsTo(Picbook::class);
    }
} 