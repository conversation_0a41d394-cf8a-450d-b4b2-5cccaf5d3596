# 生产环境 Cron 配置示例 - Horizon + Reverb + FaceSwap
# 使用命令添加到 crontab: crontab -e

# Laravel 调度器（每分钟运行）
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1

# 换脸服务专项监控（每5分钟）
*/5 * * * * cd /path/to/your/project && ./scripts/monitor-faceswap.sh

# Horizon 健康检查（每10分钟）
*/10 * * * * cd /path/to/your/project && ./scripts/monitor-horizon.sh >> /var/log/horizon-monitor.log 2>&1

# Reverb 连接检查（每15分钟）
*/15 * * * * cd /path/to/your/project && ./scripts/monitor-reverb.sh >> /var/log/reverb-monitor.log 2>&1

# 完整系统监控报告（每小时）
0 * * * * cd /path/to/your/project && ./scripts/monitor-all.sh >> /var/log/system-monitor.log 2>&1

# 日志清理（每天凌晨2点）
0 2 * * * find /var/log -name "*monitor*.log" -mtime +7 -delete

# 注意：
# 1. 将 /path/to/your/project 替换为你的实际项目路径
# 2. 确保 www 用户有执行权限
# 3. 根据需要调整监控频率