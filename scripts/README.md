# 生产环境脚本操作说明

本目录包含了生产环境部署和监控的所有脚本，适配 Laravel 11 + Horizon + Reverb 架构。

## 📁 脚本文件说明

### 部署脚本
- **`deploy-production.sh`** - 生产环境自动化部署脚本

### 监控脚本
- **`monitor-services.sh`** - 基础服务监控（Supervisor + Redis）
- **`monitor-horizon.sh`** - Horizon 队列专项监控
- **`monitor-reverb.sh`** - Reverb WebSocket 专项监控
- **`monitor-faceswap.sh`** - 换脸服务专项监控
- **`monitor-all.sh`** - 完整系统监控（调用所有监控脚本）

### 配置文件
- **`crontab-example.txt`** - Cron 定时任务配置示例

## 🚀 部署操作

### 首次部署

1. **准备环境**
```bash
# 确保脚本有执行权限
chmod +x scripts/*.sh

# 复制环境配置
cp .env.production.example .env
# 编辑 .env 文件，填入实际配置
```

2. **配置 Supervisor**
```bash
# 复制配置文件
sudo cp supervisor/horizon.conf /etc/supervisor/conf.d/
sudo cp supervisor/reverb.conf /etc/supervisor/conf.d/
sudo cp supervisor/laravel-scheduler.conf /etc/supervisor/conf.d/

# 更新路径（将 /path/to/your/project 替换为实际路径）
sudo sed -i 's|/path/to/your/project|'$(pwd)'|g' /etc/supervisor/conf.d/*.conf

# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update
```

3. **启动服务**
```bash
sudo supervisorctl start laravel-scheduler
sudo supervisorctl start horizon
sudo supervisorctl start reverb
```

4. **运行部署脚本**
```bash
./scripts/deploy-production.sh
```

### 日常部署

```bash
# 直接运行部署脚本即可
./scripts/deploy-production.sh
```

## 📊 监控操作

### 单项监控

```bash
# 基础服务状态
./scripts/monitor-services.sh

# Horizon 队列状态
./scripts/monitor-horizon.sh

# Reverb WebSocket 状态
./scripts/monitor-reverb.sh

# 换脸服务状态
./scripts/monitor-faceswap.sh
```

### 完整监控

```bash
# 监控所有服务
./scripts/monitor-all.sh
```

### 实时监控

```bash
# 实时查看 Horizon 状态
watch -n 5 './scripts/monitor-horizon.sh'

# 实时查看换脸队列
watch -n 10 'php artisan faceswap:queue-stats'
```

## ⏰ 自动化监控设置

### 配置 Cron 定时任务

```bash
# 编辑 crontab
crontab -e

# 复制 scripts/crontab-example.txt 中的内容
# 记得将 /path/to/your/project 替换为实际项目路径
```

### 推荐的监控频率

- **Laravel 调度器**: 每分钟
- **换脸服务监控**: 每5分钟
- **Horizon 健康检查**: 每10分钟
- **Reverb 连接检查**: 每15分钟
- **完整系统报告**: 每小时

## 🔧 故障排除

### Horizon 相关问题

```bash
# 检查 Horizon 状态
php artisan horizon:status

# 重启 Horizon
php artisan horizon:terminate
sudo supervisorctl restart horizon

# 查看 Horizon 日志
tail -f storage/logs/horizon.log
```

### Reverb 相关问题

```bash
# 测试 Reverb 连接
php artisan reverb:ping

# 重启 Reverb
php artisan reverb:restart
sudo supervisorctl restart reverb

# 查看 Reverb 日志
tail -f storage/logs/reverb.log
```

### 队列相关问题

```bash
# 查看队列统计
php artisan faceswap:queue-stats

# 清理超时任务
php artisan faceswap:cleanup-timeout

# 处理待处理批次
php artisan faceswap:process-pending
```

### 服务重启顺序

```bash
# 1. 停止所有服务
sudo supervisorctl stop all

# 2. 按顺序启动
sudo supervisorctl start laravel-scheduler
sudo supervisorctl start horizon
sudo supervisorctl start reverb

# 3. 检查状态
sudo supervisorctl status
```

## 📝 日志文件位置

- **Horizon 日志**: `storage/logs/horizon.log`
- **Reverb 日志**: `storage/logs/reverb.log`
- **调度器日志**: `storage/logs/scheduler.log`
- **换脸服务日志**: `storage/logs/faceswap-*.log`
- **监控日志**: `/var/log/*monitor*.log`

## ⚠️ 注意事项

1. **权限问题**: 确保 `www` 用户有执行脚本的权限
2. **路径配置**: 所有配置文件中的路径都需要替换为实际项目路径
3. **环境变量**: 生产环境的 `.env` 文件必须正确配置
4. **防火墙**: 确保 Reverb 的端口（默认8080）已开放
5. **资源监控**: 定期检查服务器资源使用情况，必要时调整 Horizon 的进程数量

## 🆘 紧急处理

### 服务完全停止时

```bash
# 快速重启所有服务
sudo supervisorctl restart all

# 检查服务状态
./scripts/monitor-all.sh
```

### 队列积压严重时

```bash
# 增加 Horizon 进程数（临时）
# 编辑 config/horizon.php，增加 maxProcesses

# 重启 Horizon 应用新配置
php artisan horizon:terminate

# 监控队列恢复情况
watch -n 5 'php artisan faceswap:queue-stats'
```

### 数据库连接问题

```bash
# 检查数据库连接
php artisan tinker --execute="DB::connection()->getPdo();"

# 清理连接池
php artisan cache:clear
php artisan config:clear
```

---

如有问题，请查看相应的日志文件或联系系统管理员。