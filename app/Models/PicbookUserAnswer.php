<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PicbookUserAnswer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'picbook_id',
        'page_id',
        'answers'
    ];

    protected $casts = [
        'answers' => 'array'
    ];

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取关联的绘本
     */
    public function picbook()
    {
        return $this->belongsTo(Picbook::class);
    }

    /**
     * 获取关联的页面
     */
    public function page()
    {
        return $this->belongsTo(PicbookPage::class, 'page_id');
    }
    
    /**
     * 根据用户和绘本查询答案的范围
     */
    public function scopeOfUserAndPicbook($query, $userId, $picbookId)
    {
        return $query->where('user_id', $userId)
                     ->where('picbook_id', $picbookId);
    }
    
    /**
     * 处理问题文本，替换变量
     */
    public function processQuestionText($text)
    {
        if (empty($this->answers) || empty($text)) {
            return $text;
        }
        
        foreach ($this->answers as $key => $value) {
            $text = str_replace("{{$key}}", $value, $text);
        }
        
        return $text;
    }
} 