<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductOption extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'option_type',
        'option_key',
        'name',
        'description',
        'price',
        'currency_code',
        'image_url',
        'is_default',
        'status',
        'sort_order'
    ];

    protected $casts = [
        'price' => 'float',
        'is_default' => 'boolean',
        'status' => 'integer',
        'sort_order' => 'integer',
        'name' => 'array',         // 将name字段转换为数组，以支持多语言
        'description' => 'array'   // 将description字段转换为数组，以支持多语言
    ];

    /**
     * 选项类型常量
     */
    const TYPE_COVER = 'cover';         // 封面
    const TYPE_BINDING = 'binding';     // 装帧方式
    const TYPE_GIFT_BOX = 'gift_box';   // 礼盒

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 1;    // 启用
    const STATUS_INACTIVE = 0;  // 禁用

    /**
     * 获取指定类型的所有可用选项
     *
     * @param string $type 选项类型
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getOptions($type)
    {
        return self::where('option_type', $type)
            ->where('status', self::STATUS_ACTIVE)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * 获取指定类型的默认选项
     *
     * @param string $type 选项类型
     * @return ProductOption|null
     */
    public static function getDefaultOption($type)
    {
        return self::where('option_type', $type)
            ->where('is_default', true)
            ->where('status', self::STATUS_ACTIVE)
            ->first();
    }

    /**
     * 根据键和类型获取特定选项
     *
     * @param string $key 选项键
     * @param string $type 选项类型
     * @return ProductOption|null
     */
    public static function getOption($key, $type)
    {
        return self::where('option_key', $key)
            ->where('option_type', $type)
            ->where('status', self::STATUS_ACTIVE)
            ->first();
    }
} 