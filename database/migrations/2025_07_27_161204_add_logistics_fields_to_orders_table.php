<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->timestamp('confirmed_at')->nullable()->after('cancelled_at');
            $table->string('logistics_request_no')->nullable()->after('confirmed_at');
            $table->string('logistics_status')->nullable()->after('logistics_request_no');
            $table->json('logistics_data')->nullable()->after('logistics_status');
            $table->string('tracking_number')->nullable()->after('logistics_data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'confirmed_at',
                'logistics_request_no',
                'logistics_status',
                'logistics_data',
                'tracking_number'
            ]);
        });
    }
};
