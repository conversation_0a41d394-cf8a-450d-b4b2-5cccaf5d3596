<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

$redis = Illuminate\Support\Facades\Redis::connection();

// 清除普通队列 - 适配 Horizon
$normalQueue = 'queues:face_swap';
$highPriorityQueue = 'queues:high_priority_face_swap';
$defaultQueue = 'queues:default';
$pollingQueue = 'queues:polling';

echo "开始清除换脸任务队列 (Horizon 管理)...\n";

// 清除普通队列
$normalCount = $redis->llen($normalQueue);
if ($normalCount > 0) {
    $redis->del($normalQueue);
    echo "清除普通优先级队列: {$normalQueue}, 共 {$normalCount} 个任务\n";
} else {
    echo "普通优先级队列为空\n";
}

// 清除高优先级队列
$highCount = $redis->llen($highPriorityQueue);
if ($highCount > 0) {
    $redis->del($highPriorityQueue);
    echo "清除高优先级队列: {$highPriorityQueue}, 共 {$highCount} 个任务\n";
} else {
    echo "高优先级队列为空\n";
}

// 清除默认队列
$defaultCount = $redis->llen($defaultQueue);
if ($defaultCount > 0) {
    $redis->del($defaultQueue);
    echo "清除默认队列: {$defaultQueue}, 共 {$defaultCount} 个任务\n";
} else {
    echo "默认队列为空\n";
}

// 清除轮询队列
$pollingCount = $redis->llen($pollingQueue);
if ($pollingCount > 0) {
    $redis->del($pollingQueue);
    echo "清除轮询队列: {$pollingQueue}, 共 {$pollingCount} 个任务\n";
} else {
    echo "轮询队列为空\n";
}

// 清除延迟队列
$delayedQueues = [
    'queues:face_swap:delayed',
    'queues:high_priority_face_swap:delayed'
];

foreach ($delayedQueues as $queue) {
    $count = $redis->zcard($queue);
    if ($count > 0) {
        $redis->del($queue);
        echo "清除延迟队列: {$queue}, 共 {$count} 个任务\n";
    } else {
        echo "延迟队列 {$queue} 为空\n";
    }
}

// 清除保留队列
$reservedQueues = [
    'queues:face_swap:reserved',
    'queues:high_priority_face_swap:reserved'
];

foreach ($reservedQueues as $queue) {
    $count = $redis->zcard($queue);
    if ($count > 0) {
        $redis->del($queue);
        echo "清除保留队列: {$queue}, 共 {$count} 个任务\n";
    } else {
        echo "保留队列 {$queue} 为空\n";
    }
}

echo "队列清除完成!\n"; 