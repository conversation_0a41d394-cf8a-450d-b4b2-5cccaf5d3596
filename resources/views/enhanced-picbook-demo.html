<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版绘本处理演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        select, input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .preview-image {
            max-width: 100%;
            margin-top: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>增强版绘本处理演示</h1>
        <p>这个演示展示了新的多图合并和文字处理功能</p>
        
        <form id="picbookForm">
            <div class="form-group">
                <label for="picbook_id">绘本ID:</label>
                <select id="picbook_id" name="picbook_id">
                    <option value="test">测试绘本 (test)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="skin_tone">肤色选择:</label>
                <select id="skin_tone" name="skin_tone">
                    <option value="brown">棕色</option>
                    <option value="dark">深色</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="hair_style">发型选择:</label>
                <select id="hair_style" name="hair_style">
                    <option value="1">发型1</option>
                    <option value="2">发型2</option>
                    <option value="3">发型3</option>
                    <option value="4">发型4</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="hair_color">发色选择:</label>
                <select id="hair_color" name="hair_color">
                    <option value="brown">棕色</option>
                    <option value="dark">深色</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="dedication_text">寄语文字 (可选):</label>
                <textarea id="dedication_text" name="dedication_text" rows="3" placeholder="输入您的寄语文字..."></textarea>
            </div>
            
            <button type="button" onclick="getConfig()">获取配置</button>
            <button type="button" onclick="processPage()">处理页面</button>
            <button type="button" onclick="batchProcess()">批量处理</button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>处理中，请稍候...</p>
        </div>
        
        <div class="result" id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://huiben.com/api';
        
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
        }
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        function showResult(success, message, data = null) {
            hideLoading();
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + (success ? 'success' : 'error');
            resultDiv.style.display = 'block';
            
            let html = '<strong>' + (success ? '成功' : '失败') + ':</strong> ' + message;
            
            if (data && data.image_path) {
                html += '<br><img src="http://huiben.com/' + data.image_path + '" class="preview-image" alt="处理结果">';
            }
            
            if (data && data.config) {
                html += '<br><br><strong>配置信息:</strong><pre>' + JSON.stringify(data.config, null, 2) + '</pre>';
            }
            
            resultDiv.innerHTML = html;
        }
        
        async function apiRequest(url, method = 'GET', data = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(API_BASE + url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                return {
                    success: false,
                    message: '网络请求失败: ' + error.message
                };
            }
        }
        
        async function getConfig() {
            showLoading();
            const picbookId = document.getElementById('picbook_id').value;
            
            const result = await apiRequest(`/enhanced-picbook/${picbookId}/config`);
            
            if (result.success) {
                showResult(true, '配置获取成功', result);
            } else {
                showResult(false, result.message);
            }
        }
        
        async function processPage() {
            showLoading();
            
            const formData = {
                picbook_id: document.getElementById('picbook_id').value,
                user_options: {
                    skin_tone: document.getElementById('skin_tone').value,
                    hair_style: document.getElementById('hair_style').value,
                    hair_color: document.getElementById('hair_color').value
                },
                dedication_text: document.getElementById('dedication_text').value || null
            };
            console.log(formData);
            
            const result = await apiRequest('/enhanced-picbook/process-page', 'POST', formData);
            
            if (result.success) {
                showResult(true, '页面处理成功', result);
            } else {
                showResult(false, result.message);
            }
        }
        
        async function batchProcess() {
            showLoading();
            
            const formData = {
                picbook_id: document.getElementById('picbook_id').value,
                pages: [
                    { id: 1, page_number: 1 },
                    { id: 2, page_number: 2 }
                ],
                user_options: {
                    skin_tone: document.getElementById('skin_tone').value,
                    hair_style: document.getElementById('hair_style').value,
                    hair_color: document.getElementById('hair_color').value
                },
                dedication_text: document.getElementById('dedication_text').value || null
            };
            
            const result = await apiRequest('/enhanced-picbook/batch-process', 'POST', formData);
            
            if (result.success) {
                showResult(true, `批量处理完成 (成功: ${result.success_count}, 失败: ${result.fail_count})`, result);
            } else {
                showResult(false, result.message);
            }
        }
    </script>
</body>
</html>