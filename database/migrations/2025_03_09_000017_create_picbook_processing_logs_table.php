<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 执行迁移
     */
    public function up(): void
    {
        // 创建绘本处理日志表
        Schema::create('picbook_processing_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('picbook_id')->constrained()->onDelete('cascade');
            $table->string('process_type')->comment('处理类型');
            $table->integer('status')->default(0)->comment('0:处理中 1:成功 2:失败');
            $table->text('message')->nullable()->comment('处理消息');
            $table->json('details')->nullable()->comment('处理详情');
            $table->timestamps();
            
            // 索引提高查询性能
            $table->index(['picbook_id', 'process_type']);
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('picbook_processing_logs');
    }
}; 