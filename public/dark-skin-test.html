<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深色肤色测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .comparison {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .image-container {
            flex: 1;
            min-width: 300px;
            text-align: center;
        }

        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }

        button:hover {
            background: #0056b3;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>深色肤色处理测试</h1>

        <div class="test-section">
            <h2>肤色对比测试</h2>
            <p>点击按钮生成不同肤色的图片进行对比</p>

            <button onclick="generateImage('brown')">生成棕色肤色</button>
            <button onclick="generateImage('dark')">生成深色肤色</button>
            <button onclick="clearResults()">清除结果</button>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>处理中，请稍候...</p>
            </div>

            <div class="result" id="result"></div>

            <div class="comparison" id="comparison">
                <!-- 对比图片将在这里显示 -->
            </div>
        </div>

        <div class="test-section">
            <h2>配置信息</h2>
            <button onclick="getConfig()">获取当前配置</button>
            <div id="config-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        let generatedImages = {};

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showResult(success, message) {
            hideLoading();
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + (success ? 'success' : 'error');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<strong>' + (success ? '成功' : '失败') + ':</strong> ' + message;
        }

        async function generateImage(skinTone) {
            showLoading();

            const formData = {
                picbook_id: 'test',
                user_options: {
                    skin_tone: skinTone,
                    hair_style: '2',
                    hair_color: skinTone
                },
                dedication_text: `${skinTone === 'brown' ? '棕色' : '深色'}肤色测试`
            };

            try {
                const response = await fetch(API_BASE + '/enhanced-picbook/process-page', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    showResult(true, `${skinTone === 'brown' ? '棕色' : '深色'}肤色图片生成成功`);

                    // 保存生成的图片信息
                    generatedImages[skinTone] = {
                        path: result.image_path,
                        timestamp: new Date().toLocaleString()
                    };

                    // 更新对比显示
                    updateComparison();
                } else {
                    showResult(false, result.message || '处理失败');
                }
            } catch (error) {
                showResult(false, '网络请求失败: ' + error.message);
            }
        }

        function updateComparison() {
            const comparisonDiv = document.getElementById('comparison');
            comparisonDiv.innerHTML = '';

            Object.keys(generatedImages).forEach(skinTone => {
                const imageInfo = generatedImages[skinTone];
                const container = document.createElement('div');
                container.className = 'image-container';

                container.innerHTML = `
                    <h3>${skinTone === 'brown' ? '棕色肤色' : '深色肤色'}</h3>
                    <img src="http://127.0.0.1:8000/${imageInfo.path}" alt="${skinTone}肤色" 
                         onerror="this.alt='图片加载失败'; this.style.display='none';">
                    <p>生成时间: ${imageInfo.timestamp}</p>
                    <p>路径: ${imageInfo.path}</p>
                `;

                comparisonDiv.appendChild(container);
            });
        }

        function clearResults() {
            generatedImages = {};
            document.getElementById('comparison').innerHTML = '';
            document.getElementById('result').style.display = 'none';
        }

        async function getConfig() {
            try {
                const response = await fetch(API_BASE + '/enhanced-picbook/test/config');
                const result = await response.json();

                const configDiv = document.getElementById('config-result');
                if (result.success) {
                    configDiv.innerHTML = `
                        <h4>配置信息:</h4>
                        <pre>${JSON.stringify(result.config, null, 2)}</pre>
                        <h4>可用选项:</h4>
                        <pre>${JSON.stringify(result.available_options, null, 2)}</pre>
                    `;
                } else {
                    configDiv.innerHTML = '<p style="color: red;">配置获取失败: ' + result.message + '</p>';
                }
            } catch (error) {
                document.getElementById('config-result').innerHTML =
                    '<p style="color: red;">网络错误: ' + error.message + '</p>';
            }
        }

        // 页面加载时自动获取配置
        window.onload = function () {
            getConfig();
        };
    </script>
</body>

</html>