<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\SimpleFaceSwapService;

class ProcessSimpleFaceSwapBatch implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 批次ID
     */
    protected $batchId;

    /**
     * 任务尝试次数
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 300;

    /**
     * 创建任务实例
     * 
     * @param string $batchId 批次ID
     */
    public function __construct(string $batchId)
    {
        $this->batchId = $batchId;
    }

    /**
     * 执行任务
     * 
     * @param SimpleFaceSwapService $faceSwapService 换脸服务
     */
    public function handle(SimpleFaceSwapService $faceSwapService)
    {
        try {
            Log::info('开始处理简化版AI换脸批次', [
                'batch_id' => $this->batchId
            ]);

            // 处理批次中的任务
            $result = $faceSwapService->processBatch($this->batchId);

            Log::info('处理批次任务完成', [
                'batch_id' => $this->batchId,
                'success' => $result['success']
            ]);
            
        } catch (\Exception $e) {
            Log::error('处理简化版AI换脸批次失败', [
                'batch_id' => $this->batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
} 