<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Order Confirmation - DreamAzeBook</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .button {
            display: inline-block;
            background-color: #4a90e2;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 12px;
            color: #777;
        }
        .order-info {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .order-items {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .order-items th,
        .order-items td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        .order-items th {
            background-color: #f5f5f5;
        }
        .price-summary {
            margin: 15px 0;
            padding: 15px;
            border-top: 2px solid #ddd;
        }
        .price-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        .total-row {
            font-weight: bold;
            font-size: 1.1em;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            margin-top: 10px;
        }
        .shipping-info {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Order Confirmation</h1>
    </div>
    
    <div class="content">
        <p>Dear {{ $order->user->name }},</p>
        
        <p>Thank you for your order! We're pleased to confirm that your order has been received and is being processed.</p>
        
        <div class="order-info">
            <p><strong>Order Number:</strong> #{{ $order->order_number }}</p>
            <p><strong>Order Date:</strong> {{ $order->created_at->format('F j, Y') }}</p>
        </div>

        <h2>Order Details</h2>
        <table class="order-items">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($order->items as $item)
                <tr>
                    <td>{{ $item->product->name }}</td>
                    <td>{{ $item->quantity }}</td>
                    <td>${{ number_format($item->price, 2) }}</td>
                    <td>${{ number_format($item->quantity * $item->price, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <div class="price-summary">
            <div class="price-row">
                <span>Subtotal:</span>
                <span>${{ number_format($order->subtotal, 2) }}</span>
            </div>
            @if($order->tax > 0)
            <div class="price-row">
                <span>Tax:</span>
                <span>${{ number_format($order->tax, 2) }}</span>
            </div>
            @endif
            @if($order->shipping_fee > 0)
            <div class="price-row">
                <span>Shipping:</span>
                <span>${{ number_format($order->shipping_fee, 2) }}</span>
            </div>
            @endif
            <div class="price-row total-row">
                <span>Total:</span>
                <span>${{ number_format($order->total, 2) }}</span>
            </div>
        </div>

        <div class="shipping-info">
            <h3>Shipping Address</h3>
            <p>
                {{ $order->shipping_address->full_name }}<br>
                {{ $order->shipping_address->address_line1 }}<br>
                @if($order->shipping_address->address_line2)
                    {{ $order->shipping_address->address_line2 }}<br>
                @endif
                {{ $order->shipping_address->city }}, {{ $order->shipping_address->state }} {{ $order->shipping_address->postal_code }}<br>
                {{ $order->shipping_address->country }}
            </p>
        </div>

        <p>You can track your order status by clicking the button below:</p>
        
        <a href="{{ url('/orders/' . $order->id) }}" class="button">View Order Details</a>

        <p>If you have any questions about your order, please contact our customer service team.</p>
    </div>
    
    <div class="footer">
        <p>&copy; {{ date('Y') }} DreamAzeBook. All rights reserved.</p>
        <p>This email was sent to {{ $order->user->email }}</p>
    </div>
</body>
</html>