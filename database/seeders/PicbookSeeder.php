<?php

namespace Database\Seeders;

use App\Models\Picbook;
use App\Models\PicbookPage;
use App\Models\PicbookVariant;
use App\Models\PicbookPageVariant;
use Illuminate\Database\Seeder;

class PicbookSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建绘本数据
        $picbook = Picbook::create([
            'id' => 1,
            'default_name' => 'Good Night(SingleCharacter)',
            'default_cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
            'pricesymbol' => '$',
            'price' => 29.99,
            'currencycode' => 'USD',
            'total_pages' => 19,
            'preview_pages_count' => 7,
            'character_count' => 1,
            'rating' => 5.50,
            'supported_languages' => ["zh", "en"],
            'supported_genders' => [1, 2],
            'supported_skincolors' => [1, 2, 3],
            'tags' => ["fantasy", "adventure"],
            'has_choices' => 1,
            'choices_type' => 1,
            'has_question' => 1,
            'status' => 1,
            'batch_processing_status' => 0
        ]);

        // 创建绘本变体
        $this->createPicbookVariants($picbook->id);

        // 创建绘本页面
        $this->createPicbookPages($picbook->id);

        // 创建绘本页面变体
        $this->createPicbookPageVariants();
        
        // 输出成功信息
        if (isset($this->command)) {
            $this->command->info('绘本数据填充完成');
        }
    }

    /**
     * 创建绘本变体数据
     */
    private function createPicbookVariants($picbookId): void
    {
        $variants = [
            [
                'id' => 1,
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 1,
                'skincolor' => 1,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-男性-白色',
                'description' => '书籍描述',
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 2,
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 1,
                'skincolor' => 2,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-男性-棕色',
                'description' => '书籍描述',
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 3,
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 1,
                'skincolor' => 3,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-男性-黑色',
                'description' => '书籍描述',
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 4,
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 2,
                'skincolor' => 1,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-女性-白色',
                'description' => '书籍描述',
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 5,
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 2,
                'skincolor' => 2,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-女性-棕色',
                'description' => '书籍描述',
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 6,
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 2,
                'skincolor' => 3,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-女性-黑色',
                'description' => '书籍描述',
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 7,
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 1,
                'skincolor' => 1,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 8,
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 1,
                'skincolor' => 2,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 9,
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 1,
                'skincolor' => 3,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 10,
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 2,
                'skincolor' => 1,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 11,
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 2,
                'skincolor' => 2,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'id' => 12,
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 2,
                'skincolor' => 3,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'public/imgs/picbook/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
        ];

        foreach ($variants as $variant) {
            PicbookVariant::create($variant);
        }
    }

    /**
     * 创建绘本页面数据
     */
    private function createPicbookPages($picbookId): void
    {
        $pages = [
            [
                'id' => 1,
                'picbook_id' => $picbookId,
                'page_number' => 1,
                'image_url' => 'public/imgs/picbook/goodnight/white/1-2.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 1,
                'text_elements' => [['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 2,
                'picbook_id' => $picbookId,
                'page_number' => 2,
                'image_url' => 'public/imgs/picbook/goodnight/white/3-4.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 3,
                'picbook_id' => $picbookId,
                'page_number' => 3,
                'image_url' => 'public/imgs/picbook/goodnight/white/5-6.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 4,
                'picbook_id' => $picbookId,
                'page_number' => 4,
                'image_url' => 'public/imgs/picbook/goodnight/white/7-8.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/7-8mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => [['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 5,
                'picbook_id' => $picbookId,
                'page_number' => 5,
                'image_url' => 'public/imgs/picbook/goodnight/white/9-10.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/9-10mask.jpg',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 6,
                'picbook_id' => $picbookId,
                'page_number' => 6,
                'image_url' => 'public/imgs/picbook/goodnight/white/11-12.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 7,
                'picbook_id' => $picbookId,
                'page_number' => 7,
                'image_url' => 'public/imgs/picbook/goodnight/white/13-14.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 8,
                'picbook_id' => $picbookId,
                'page_number' => 8,
                'image_url' => 'public/imgs/picbook/goodnight/white/15-16.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/15-16mask.jpg',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 9,
                'picbook_id' => $picbookId,
                'page_number' => 9,
                'image_url' => 'public/imgs/picbook/goodnight/white/17-18.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/17-18mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => [['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 10,
                'picbook_id' => $picbookId,
                'page_number' => 10,
                'image_url' => 'public/imgs/picbook/goodnight/white/19-20.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 11,
                'picbook_id' => $picbookId,
                'page_number' => 11,
                'image_url' => 'public/imgs/picbook/goodnight/white/21-22.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/21-22mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => [['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 12,
                'picbook_id' => $picbookId,
                'page_number' => 12,
                'image_url' => 'public/imgs/picbook/goodnight/white/23-24.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 13,
                'picbook_id' => $picbookId,
                'page_number' => 13,
                'image_url' => 'public/imgs/picbook/goodnight/white/25-26.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/25-26mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => [['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 14,
                'picbook_id' => $picbookId,
                'page_number' => 14,
                'image_url' => 'public/imgs/picbook/goodnight/white/27-28.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 15,
                'picbook_id' => $picbookId,
                'page_number' => 15,
                'image_url' => 'public/imgs/picbook/goodnight/white/29-30.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/29-30mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => [['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 16,
                'picbook_id' => $picbookId,
                'page_number' => 16,
                'image_url' => 'public/imgs/picbook/goodnight/white/31-32.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/31-32mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => [['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 17,
                'picbook_id' => $picbookId,
                'page_number' => 17,
                'image_url' => 'public/imgs/picbook/goodnight/white/33-34.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/33-34mask.jpg',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 18,
                'picbook_id' => $picbookId,
                'page_number' => 18,
                'image_url' => 'public/imgs/picbook/goodnight/white/35-36.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 1,
                'text_elements' => [['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'id' => 19,
                'picbook_id' => $picbookId,
                'page_number' => 19,
                'image_url' => 'public/imgs/picbook/goodnight/white/37-38.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => [],
                'character_sequence' => [1],
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
        ];

        // 批量创建页面
        foreach ($pages as $page) {
            PicbookPage::create($page);
        }
    }

    /**
     * 创建绘本页面变体数据
     */
    private function createPicbookPageVariants(): void
    {
        // 使用SQL文件数据构建前7页的变体（每页12个变体，总共84条记录）
        $languages = ['zh', 'en'];
        $genders = [1, 2];
        $skincolors = [1, 2, 3];
        
        // 页面基本信息
        $pagesInfo = [
            1 => [
                'image_url' => 'public/imgs/picbook/goodnight/white/1-2.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 1
            ],
            2 => [
                'image_url' => 'public/imgs/picbook/goodnight/white/3-4.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0, 
                'has_text' => 0
            ],
            3 => [
                'image_url' => 'public/imgs/picbook/goodnight/white/5-6.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0
            ],
            4 => [
                'image_url' => 'public/imgs/picbook/goodnight/white/7-8.jpg',
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/7-8mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 1
            ],
            5 => [
                'image_url' => 'public/imgs/picbook/goodnight/white/9-10.jpg',
                'mask_image_url' => 'public/imgs/picbook/goodnight/mask/9-10mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 0
            ],
            6 => [
                'image_url' => 'public/imgs/picbook/goodnight/white/11-12.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0
            ],
            7 => [
                'image_url' => 'public/imgs/picbook/goodnight/white/13-14.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0
            ]
        ];
        
        // 开始构建变体数据
        $variants = [];
        $id = 1;
        
        for ($pageId = 1; $pageId <= 7; $pageId++) {
            $pageInfo = $pagesInfo[$pageId];
            
            foreach ($languages as $language) {
                foreach ($genders as $gender) {
                    foreach ($skincolors as $skincolor) {
                        $variants[] = [
                            'id' => $id++,
                            'page_id' => $pageId,
                            'language' => $language,
                            'gender' => $gender,
                            'skincolor' => $skincolor,
                            'character_skincolors' => [$skincolor],
                            'image_url' => $pageInfo['image_url'],
                            'skin_mask_url' => $pageInfo['mask_image_url'],
                            'has_text' => $pageInfo['has_text'],
                            'text_config' => null,
                            'has_face' => $pageInfo['is_ai_face'],
                            'face_config' => [
                                'mask_url' => $pageInfo['mask_image_url'], 
                                'character_sequence' => [1]
                            ],
                            'is_published' => 1,
                            'elements' => null,
                            'text_elements' => $pageInfo['has_text'] ? 
                            [['x' => 50, 'y' => 500, 'color' => '#000000', 'fontSize' => 50]] : 
                                [],
                            'variant_type' => 0,
                            'is_preview_variant' => 0,
                            'content' => null,
                            'choice_options' => null,
                            'question' => null,
                            'character_masks' => null,
                            'processing_status' => 0,
                            'processed_image_url' => null,
                            'created_at' => now(),
                            'updated_at' => now()
                        ];
                    }
                }
            }
        }

        // 批量创建页面变体
        foreach ($variants as $variant) {
            PicbookPageVariant::create($variant);
        }
        
        // 在命令行输出信息
        if (isset($this->command)) {
            $this->command->info('创建了前7页的页面变体数据，共' . count($variants) . '条记录（每页12个变体）');
        }
    }
} 