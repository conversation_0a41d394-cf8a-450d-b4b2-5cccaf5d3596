<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\PicbookPreview;
use App\Models\Picbook;
use App\Models\AiFaceTask;
use App\Services\SimpleFaceSwapService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;

class OrderPreviewTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $user;
    protected $order;
    protected $orderItem;
    protected $preview;
    protected $picbook;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建管理员用户
        $this->admin = User::factory()->create([
            'user_type' => 'admin'
        ]);
        
        // 创建普通用户
        $this->user = User::factory()->create();
        
        // 创建绘本
        $this->picbook = Picbook::factory()->create([
            'default_name' => 'Test Picture Book',
            'price' => 29.99
        ]);
        
        // 创建预览
        $this->preview = PicbookPreview::factory()->create([
            'user_id' => $this->user->id,
            'picbook_id' => $this->picbook->id,
            'status' => 'completed',
            'preview_data' => [
                'full_name' => 'Test Child',
                'language' => 'en',
                'gender' => 1,
                'skincolor' => 1,
                'face_images' => ['test_face.jpg']
            ]
        ]);
        
        // 创建订单
        $this->order = Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => Order::STATUS_PROCESSING
        ]);
        
        // 创建订单项
        $this->orderItem = OrderItem::factory()->create([
            'order_id' => $this->order->id,
            'preview_id' => $this->preview->id,
            'picbook_id' => $this->picbook->id,
            'status' => OrderItem::STATUS_PROCESSING
        ]);
    }

    /**
     * 测试普通预览的订单预览接口
     */
    public function test_admin_can_get_order_preview_with_normal_preview()
    {
        // 设置预览数据，模拟普通预览
        $this->preview->update([
            'preview_data' => [
                'full_name' => 'Test Child',
                'language' => 'en',
                'gender' => 1,
                'skincolor' => 1,
                'face_images' => ['test_face.jpg'],
                'pages' => [
                    [
                        'page_number' => 1,
                        'page_id' => 1,
                        'image_url' => 'original_page1.jpg',
                        'result_image_url' => 'result_page1.jpg',
                        'text' => 'Page 1 text'
                    ],
                    [
                        'page_number' => 2,
                        'page_id' => 2,
                        'image_url' => 'original_page2.jpg',
                        'result_image_url' => 'result_page2.jpg',
                        'text' => 'Page 2 text'
                    ]
                ]
            ],
            'result_images' => [
                [
                    'page_id' => 1,
                    'result_image_url' => 'result_page1.jpg'
                ],
                [
                    'page_id' => 2,
                    'result_image_url' => 'result_page2.jpg'
                ]
            ]
        ]);

        // 更新订单状态为允许预览
        $this->order->update(['status' => Order::STATUS_AI_COMPLETED]);

        // 认证为管理员
        $this->actingAs($this->admin);

        // 发送请求
        $response = $this->getJson("/api/admin/orders/{$this->order->id}/preview");

        // 验证响应
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'order_info',
                'user_info',
                'items' => [
                    '*' => [
                        'item_id',
                        'picbook',
                        'character_info',
                        'personalization',
                        'face_images',
                        'result_images',
                        'pages' => [
                            '*' => [
                                'page_number',
                                'page_id',
                                'image_url',
                                'result_image_url'
                            ]
                        ],
                        'processing_info'
                    ]
                ]
            ]
        ]);

        $responseData = $response->json('data');
        
        // 验证页面数据
        $this->assertCount(2, $responseData['items'][0]['pages']);
        $this->assertEquals(1, $responseData['items'][0]['pages'][0]['page_number']);
        $this->assertEquals('result_page1.jpg', $responseData['items'][0]['pages'][0]['result_image_url']);
    }

    /**
     * 测试全书预览的订单预览接口
     */
    public function test_admin_can_get_order_preview_with_full_book_preview()
    {
        // 创建批次ID，模拟全书预览
        $batchId = 'face_test_batch_123';
        
        // 更新订单项，添加批次ID
        $this->orderItem->update([
            'face_swap_batch_id' => $batchId,
            'result_images' => [
                [
                    'page_id' => 1,
                    'page_number' => 1,
                    'result_image_url' => 'full_book_result_page1.jpg',
                    'task_index' => 0
                ],
                [
                    'page_id' => 2,
                    'page_number' => 2,
                    'result_image_url' => 'full_book_result_page2.jpg',
                    'task_index' => 1
                ]
            ]
        ]);

        // 创建AI任务记录，模拟全书换脸完成
        AiFaceTask::create([
            'batch_id' => $batchId,
            'type' => 'task',
            'status' => 'completed',
            'page_id' => 1,
            'task_index' => 0,
            'image_url' => 'original_page1.jpg',
            'result_image_url' => 'full_book_result_page1.jpg',
            'result' => ['standard_url' => 'full_book_result_page1.jpg']
        ]);

        AiFaceTask::create([
            'batch_id' => $batchId,
            'type' => 'task',
            'status' => 'completed',
            'page_id' => 2,
            'task_index' => 1,
            'image_url' => 'original_page2.jpg',
            'result_image_url' => 'full_book_result_page2.jpg',
            'result' => ['standard_url' => 'full_book_result_page2.jpg']
        ]);

        // 创建页面记录
        \App\Models\PicbookPage::create([
            'id' => 1,
            'picbook_id' => $this->picbook->id,
            'page_number' => 1,
            'image_url' => 'original_page1.jpg'
        ]);

        \App\Models\PicbookPage::create([
            'id' => 2,
            'picbook_id' => $this->picbook->id,
            'page_number' => 2,
            'image_url' => 'original_page2.jpg'
        ]);

        // 更新订单状态为允许预览
        $this->order->update(['status' => Order::STATUS_AI_COMPLETED]);

        // 认证为管理员
        $this->actingAs($this->admin);

        // 发送请求
        $response = $this->getJson("/api/admin/orders/{$this->order->id}/preview");

        // 验证响应
        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        
        // 验证页面数据 - 应该从AI任务中获取
        $this->assertCount(2, $responseData['items'][0]['pages']);
        $this->assertEquals(1, $responseData['items'][0]['pages'][0]['page_number']);
        $this->assertEquals('full_book_result_page1.jpg', $responseData['items'][0]['pages'][0]['result_image_url']);
        $this->assertEquals('original_page1.jpg', $responseData['items'][0]['pages'][0]['original_image_url']);
    }

    /**
     * 测试订单状态不允许预览的情况
     */
    public function test_admin_cannot_get_preview_for_invalid_order_status()
    {
        // 设置订单状态为不允许预览
        $this->order->update(['status' => Order::STATUS_PENDING]);

        // 认证为管理员
        $this->actingAs($this->admin);

        // 发送请求
        $response = $this->getJson("/api/admin/orders/{$this->order->id}/preview");

        // 验证响应
        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => '订单状态不允许预览'
        ]);
    }
}
