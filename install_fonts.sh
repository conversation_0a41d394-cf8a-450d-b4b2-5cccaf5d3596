#!/bin/bash

# 设置字体目录
FONT_DIR="public/fonts"
mkdir -p "$FONT_DIR"

echo "开始下载字体..."

# 下载Noto Sans字体（支持多种语言）
echo "下载 Noto Sans 字体..."
wget -q -O "$FONT_DIR/NotoSans-Regular.ttf" "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSans/NotoSans-Regular.ttf"
wget -q -O "$FONT_DIR/NotoSans-Bold.ttf" "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSans/NotoSans-Bold.ttf"

# 下载思源黑体（中文）
echo "下载思源黑体（中文）..."
wget -q -O "$FONT_DIR/SourceHanSansSC-Regular.otf" "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf"
wget -q -O "$FONT_DIR/SourceHanSansSC-Bold.otf" "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Bold.otf"

# 如果下载失败，使用备用命令
if [ ! -f "$FONT_DIR/NotoSans-Regular.ttf" ]; then
    echo "使用备用方法下载字体..."
    curl -s -o "$FONT_DIR/NotoSans-Regular.ttf" "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSans/NotoSans-Regular.ttf"
    curl -s -o "$FONT_DIR/NotoSans-Bold.ttf" "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSans/NotoSans-Bold.ttf"
    
    curl -s -o "$FONT_DIR/SourceHanSansSC-Regular.otf" "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf"
    curl -s -o "$FONT_DIR/SourceHanSansSC-Bold.otf" "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Bold.otf"
fi

# 创建基本字体文件（如果下载失败）
if [ ! -f "$FONT_DIR/NotoSans-Regular.ttf" ]; then
    echo "下载失败，创建简单的字体链接..."
    
    # 查找系统字体
    if [ -f "/Library/Fonts/Arial.ttf" ]; then
        cp "/Library/Fonts/Arial.ttf" "$FONT_DIR/Arial.ttf"
        ln -sf "$FONT_DIR/Arial.ttf" "$FONT_DIR/NotoSans-Regular.ttf"
    elif [ -f "/System/Library/Fonts/AppleSDGothicNeo.ttc" ]; then
        cp "/System/Library/Fonts/AppleSDGothicNeo.ttc" "$FONT_DIR/AppleSDGothicNeo.ttc"
        ln -sf "$FONT_DIR/AppleSDGothicNeo.ttc" "$FONT_DIR/NotoSans-Regular.ttf"
    elif [ -f "/System/Library/Fonts/STHeiti Light.ttc" ]; then
        cp "/System/Library/Fonts/STHeiti Light.ttc" "$FONT_DIR/STHeiti-Light.ttc"
        ln -sf "$FONT_DIR/STHeiti-Light.ttc" "$FONT_DIR/NotoSans-Regular.ttf"
    fi
fi

# 检查字体文件是否存在
echo "检查字体文件..."
if [ -f "$FONT_DIR/NotoSans-Regular.ttf" ]; then
    echo "✅ Noto Sans Regular 字体已安装"
else
    echo "❌ Noto Sans Regular 字体安装失败"
fi

if [ -f "$FONT_DIR/SourceHanSansSC-Regular.otf" ]; then
    echo "✅ 思源黑体 Regular 字体已安装"
else
    echo "❌ 思源黑体 Regular 字体安装失败"
fi

echo "字体安装完成！" 