<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

## About Laravel

Laravel is a web application framework with expressive, elegant syntax. We believe development must be an enjoyable and creative experience to be truly fulfilling. Laravel takes the pain out of development by easing common tasks used in many web projects, such as:

- [Simple, fast routing engine](https://laravel.com/docs/routing).
- [Powerful dependency injection container](https://laravel.com/docs/container).
- Multiple back-ends for [session](https://laravel.com/docs/session) and [cache](https://laravel.com/docs/cache) storage.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).
- Database agnostic [schema migrations](https://laravel.com/docs/migrations).
- [Robust background job processing](https://laravel.com/docs/queues).
- [Real-time event broadcasting](https://laravel.com/docs/broadcasting).

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com/)**
- **[Tighten Co.](https://tighten.co)**
- **[WebReinvent](https://webreinvent.com/)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel/)**
- **[Cyber-Duck](https://cyber-duck.co.uk)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Jump24](https://jump24.co.uk)**
- **[Redberry](https://redberry.international/laravel/)**
- **[Active Logic](https://activelogic.com)**
- **[byte5](https://byte5.de)**
- **[OP.GG](https://op.gg)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).


## Run project
composer update
### if not key
php artisan key:generatecom
### if not migrate db
php artisan migrate
### start project
php artisan serve

# AI换脸实时进度功能

本项目实现了AI换脸处理的实时进度显示功能，基于Laravel Reverb实现WebSocket通信。

## 功能特点

- 异步处理多个AI换脸任务
- 实时显示处理进度
- 支持队列状态显示
- 显示处理完成的图片
- 错误处理与重试机制

## 技术实现

- Laravel队列系统处理异步任务
- Laravel Reverb实现WebSocket通信
- React前端实时更新进度
- 使用事件驱动架构

## 使用方法

### 1. 配置环境变量

在`.env`文件中添加以下配置：

```
BROADCAST_CONNECTION=reverb
REVERB_APP_ID=dreamazebook
REVERB_APP_KEY=reverb_app_key
REVERB_APP_SECRET=reverb_app_secret
REVERB_HOST=localhost
REVERB_PORT=8080
REVERB_SCHEME=http
```

### 2. 启动WebSocket服务器

```
php artisan reverb:start
```

可选参数：
- `--host`: 指定主机名（默认：localhost）
- `--port`: 指定端口（默认：8080）
- `--debug`: 启用调试模式

### 3. 启动队列处理器

```
php artisan queue:work --queue=face_swap
```

### 4. 前端集成

在前端页面中使用`FaceSwapProgress`组件：

```jsx
<FaceSwapProgress 
  batchId={batchId} 
  userId={userId} 
/>
```

## 开发指南

### 发送进度更新事件

```php
Event::dispatch(new FaceSwapProgressEvent(
    $batchId,
    $userId,
    $completedTasks,
    $totalTasks,
    'processing'
));
```

### 发送单个任务完成事件

```php
Event::dispatch(new FaceSwapSingleCompleteEvent(
    $batchId,
    $userId,
    $processedImageUrl,
    $pageId,
    $variantId,
    $pageNumber
));
```

## 故障排除

### 常见问题

1. **WebSocket 连接问题**
   - 确保WebSocket服务器正在运行：`php artisan reverb:start`
   - 检查防火墙设置，确保端口8080开放

2. **队列处理问题**
   - 检查队列处理器是否活跃：`php artisan queue:work`
   - 查看 Horizon 状态：`php artisan horizon:status`

3. **权限问题**
   ```bash
   # 诊断权限问题
   ./scripts/diagnose-permissions.sh
   
   # 修复权限问题
   ./scripts/fix-permissions.sh
   ```

4. **缓存问题**
   ```bash
   # 清理所有缓存
   php artisan config:clear
   php artisan route:clear
   php artisan view:clear
   php artisan cache:clear
   
   # 重新生成缓存
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

5. **日志查看**
   - Laravel 日志：`tail -f storage/logs/laravel.log`
   - 队列日志：`php artisan pail`
   - Horizon 监控：访问 `/horizon`

### 生产环境部署

```bash
# 完整部署流程
./scripts/deploy-production.sh

# 仅修复权限
./scripts/fix-permissions.sh

# 权限诊断
./scripts/diagnose-permissions.sh
```