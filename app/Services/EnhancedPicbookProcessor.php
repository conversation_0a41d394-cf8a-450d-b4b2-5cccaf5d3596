<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Imagick\Driver;
use Exception;

/**
 * 增强版绘本处理器
 * 支持多图合并、文字处理、AI换脸等功能
 */
class EnhancedPicbookProcessor
{
    protected $imageManager;
    protected $fontPath;
    
    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
        $this->fontPath = public_path('fonts');
    }

    /**
     * 处理绘本页面 - 多图合并 + 文字处理
     */
    public function processPage($picbookPath, $pageConfig, $userOptions = [], $dedicationText = null)
    {
        try {
            // 1. 合并多层图片
            $mergedImage = $this->mergeLayeredImages($picbookPath, $pageConfig, $userOptions);
            
            // 2. 添加文字
            if (!empty($pageConfig['text']) || $dedicationText) {
                $mergedImage = $this->addTextToImage($mergedImage, $pageConfig['text'] ?? [], $dedicationText);
            }
            
            // 3. 保存处理后的图片
            $outputPath = $this->saveProcessedImage($mergedImage, $picbookPath);
            
            return [
                'success' => true,
                'image_path' => $outputPath,
                'message' => '页面处理成功'
            ];
            
        } catch (Exception $e) {
            Log::error('绘本页面处理失败', [
                'error' => $e->getMessage(),
                'picbook_path' => $picbookPath,
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '页面处理失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 合并多层图片
     */
    protected function mergeLayeredImages($picbookPath, $pageConfig, $userOptions)
    {
        // 加载底图
        $basePath = public_path($picbookPath);
        $baseImagePath = $basePath . '/layer_bg.jpg';
        if (!file_exists($baseImagePath)) {
            throw new Exception("底图不存在: {$baseImagePath}");
        }
        $image = $this->imageManager->read($baseImagePath);

        // 合并肤色层（每次都基于原始肤色图做滤镜）
        if (isset($userOptions['skin_tone'])) {
            $skinLayerPath = $basePath . '/layer_skin.png';
            if (file_exists($skinLayerPath)) {
                $skinLayer = $this->imageManager->read($skinLayerPath);
                if (isset($pageConfig['skinToneFilter'][$userOptions['skin_tone']])) {
                    $filters = $pageConfig['skinToneFilter'][$userOptions['skin_tone']];
                    $skinLayer = $this->applyImageFilters($skinLayer, $filters); // 只对原始肤色层做
                }
                $image->place($skinLayer, 'top-left', 0, 0);
            } else {
                Log::warning("肤色层文件不存在: {$skinLayerPath}");
            }
        }

        // 合并发型层（每次都基于原始发型图做滤镜）
        if (isset($userOptions['hair_style'])) {
            $hairLayerPath = $basePath . "/layer_hair_{$userOptions['hair_style']}.png";
            if (file_exists($hairLayerPath)) {
                $hairLayer = $this->imageManager->read($hairLayerPath);
                if (isset($pageConfig['hairColorFilter'][$userOptions['hair_color'] ?? 'default'])) {
                    $filters = $pageConfig['hairColorFilter'][$userOptions['hair_color'] ?? 'default'];
                    $hairLayer = $this->applyImageFilters($hairLayer, $filters); // 只对原始发型层做
                }
                $image->place($hairLayer, 'top-left', 0, 0);
            } else {
                Log::warning("发型层文件不存在: {$hairLayerPath}");
            }
        }

        return $image;
    }

    /**
     * 应用肤色层
     */
    protected function applySkinLayer($image, $basePath, $skinTone, $pageConfig)
    {
        $skinLayerPath = $basePath . '/layer_skin.png';
        
        if (!file_exists($skinLayerPath)) {
            Log::warning("肤色层文件不存在: {$skinLayerPath}");
            return $image;
        }
        
        $skinLayer = $this->imageManager->read($skinLayerPath);
        
        // 应用肤色滤镜
        if (isset($pageConfig['skinToneFilter'][$skinTone])) {
            $filters = $pageConfig['skinToneFilter'][$skinTone];
            $skinLayer = $this->applyImageFilters($skinLayer, $filters);
        }
        
        // 合并到主图
        $image->place($skinLayer, 'top-left', 0, 0);
        
        return $image;
    }

    /**
     * 应用发型层
     */
    protected function applyHairLayer($image, $basePath, $hairStyle, $hairColor, $pageConfig)
    {
        $hairLayerPath = $basePath . "/layer_hair_{$hairStyle}.png";
        
        if (!file_exists($hairLayerPath)) {
            Log::warning("发型层文件不存在: {$hairLayerPath}");
            return $image;
        }
        
        $hairLayer = $this->imageManager->read($hairLayerPath);
        
        // 应用发色滤镜
        if (isset($pageConfig['hairColorFilter'][$hairColor])) {
            $filters = $pageConfig['hairColorFilter'][$hairColor];
            $hairLayer = $this->applyImageFilters($hairLayer, $filters);
        }
        
        // 合并到主图
        $image->place($hairLayer, 'top-left', 0, 0);
        
        return $image;
    }

    /**
     * 将色相角度转换为 RGB 调整值
     * 
     * @param int $hue 色相角度 (-180 到 180)
     * @return array RGB 调整值
     */
    protected function hueToRgb($hue)
    {
        // 将角度标准化到 0-360 范围
        $hue = ($hue + 360) % 360;
        
        // 根据色相角度计算 RGB 调整值
        if ($hue < 60) {
            // 红色到黄色
            $r = 30; $g = (int)($hue * 0.5); $b = -30;
        } elseif ($hue < 120) {
            // 黄色到绿色
            $r = (int)((120 - $hue) * 0.5); $g = 30; $b = -30;
        } elseif ($hue < 180) {
            // 绿色到青色
            $r = -30; $g = 30; $b = (int)(($hue - 120) * 0.5);
        } elseif ($hue < 240) {
            // 青色到蓝色
            $r = -30; $g = (int)((240 - $hue) * 0.5); $b = 30;
        } elseif ($hue < 300) {
            // 蓝色到紫色
            $r = (int)(($hue - 240) * 0.5); $g = -30; $b = 30;
        } else {
            // 紫色到红色
            $r = 30; $g = -30; $b = (int)((360 - $hue) * 0.5);
        }
        
        return ['r' => $r, 'g' => $g, 'b' => $b];
    }

    /**
     * 验证并调整滤镜参数到推荐范围 (Intervention Image 3.x 无严格限制)
     */
    protected function validateFilterParameter($value, $min, $max, $filterName)
    {
        $numericValue = (int) str_replace('+', '', $value);
        
        // Intervention Image 3.x 没有严格的参数限制，但我们仍然推荐合理的范围
        if ($numericValue < $min || $numericValue > $max) {
            Log::info("滤镜参数超出推荐范围，但仍会处理", [
                'filter' => $filterName,
                'value' => $numericValue,
                'recommended_range' => "{$min} ~ {$max}",
                'note' => 'Intervention Image 3.x 支持更广泛的参数范围'
            ]);
        }
        
        // 3.x 版本可以直接使用原始值，不需要强制调整
        return $numericValue;
    }

    /**
     * 应用图片滤镜（仅支持 Imagick 驱动，modulateImage 只调用一次）
     * @param Image $image Intervention Image 对象
     * @param array $filters 滤镜数组
     * @return Image
     */
    protected function applyImageFilters($image, $filters)
    {
        $imagick = $image->core()->native();
        $brightness = 100;
        $saturation = 100;
        $hue = 100;
        foreach ($filters as $filter => $value) {
            switch ($filter) {
                case 'saturate':
                    $saturation = 100 + (int)$value;
                    break;
                case 'hue':
                    $hue = 100 + (int)$value;
                    break;
                case 'brightness':
                    $brightness = 100 + (int)$value;
                    break;
                case 'contrast':
                    for ($i = 0; $i < abs($value); $i++) {
                        $imagick->contrastImage($value > 0);
                    }
                    break;
                case 'blur':
                    $imagick->blurImage(0, $value);
                    break;
                case 'sharpen':
                    $imagick->sharpenImage(0, $value);
                    break;
                case 'invert':
                    $imagick->negateImage(false);
                    break;
                case 'greyscale':
                    $imagick->setImageType(\Imagick::IMGTYPE_GRAYSCALE);
                    break;
                case 'opacity':
                    $imagick->evaluateImage(\Imagick::EVALUATE_MULTIPLY, $value / 100, \Imagick::CHANNEL_ALPHA);
                    break;
            }
        }
        // 只对单层原图做 modulateImage
        $imagick->modulateImage($brightness, $saturation, $hue);
        return $image;
    }

    /**
     * 添加文字到图片
     */
    protected function addTextToImage($image, $textElements, $dedicationText = null)
    {
        // 处理配置中的文字元素
        foreach ($textElements as $element) {
            $this->addSingleTextElement($image, $element);
        }
        
        // 处理寄语文字
        if ($dedicationText) {
            $this->addDedicationText($image, $dedicationText);
        }
        
        return $image;
    }

    /**
     * 添加单个文字元素
     */
    protected function addSingleTextElement($image, $element)
    {
        $text = $element['type'] === 'dynamic' ? '' : ($element['text'] ?? '');
        $fontSize = $this->convertFontSize($element['fontSize'] ?? 24, $element['fontUnit'] ?? 'px');
        $color = $element['color'] ?? '#000000';
        $x = $element['position']['x'] ?? 0;
        $y = $element['position']['y'] ?? 0;
        $fontFamily = $element['font'] ?? 'Arial';
        $fontWeight = $element['fontWeight'] ?? 'regular';
        $alignment = $element['alignment'] ?? 'left';
        
        // 获取字体文件路径
        $fontPath = $this->getFontPath($fontFamily, $fontWeight);
        
        if ($text) {
            $image->text($text, $x, $y, function ($font) use ($fontSize, $color, $fontPath, $alignment) {
                $font->size($fontSize);
                $font->color($color);
                $font->align($alignment);
                if ($fontPath && file_exists($fontPath)) {
                    $font->file($fontPath);
                }
            });
        }
    }

    /**
     * 添加寄语文字
     */
    protected function addDedicationText($image, $dedicationText)
    {
        // 寄语文字的默认样式 (基于300 DPI)
        $fontSize = $this->convertFontSize(10, 'pt'); // 10pt ≈ 42px at 300 DPI
        $color = '#333333';
        $fontFamily = 'Arial';
        
        // 计算文字位置 (居中底部)
        $imageWidth = $image->width();
        $imageHeight = $image->height();
        $x = $imageWidth / 2;
        $y = $imageHeight - 100; // 距离底部100像素
        
        $fontPath = $this->getFontPath($fontFamily, 'regular');
        
        $image->text($dedicationText, $x, $y, function ($font) use ($fontSize, $color, $fontPath) {
            $font->size($fontSize);
            $font->color($color);
            $font->align('center');
            if ($fontPath && file_exists($fontPath)) {
                $font->file($fontPath);
            }
        });
    }

    /**
     * 转换字体大小到像素单位 (基于300 DPI)
     */
    protected function convertFontSize($size, $unit = 'px')
    {
        switch (strtolower($unit)) {
            case 'pt':
                // 在 300 DPI 下: 1 pt = 300/72 px = 4.17 px
                return $size * (300 / 72);
            case 'em':
                // 1 em = 16 pt = 16 * (300/72) px ≈ 66.7 px (在300 DPI下)
                return $size * 16 * (300 / 72);
            case 'rem':
                // 1 rem = 16 pt = 16 * (300/72) px ≈ 66.7 px (在300 DPI下)
                return $size * 16 * (300 / 72);
            case 'px':
            default:
                return $size;
        }
    }

    /**
     * 获取字体文件路径
     */
    protected function getFontPath($fontFamily, $fontWeight = 'regular')
    {
        $fontMap = [
            'Arial' => [
                'regular' => 'Arial-Regular.ttf',
                'bold' => 'Arial-Bold.ttf'
            ],
            'NotoSans' => [
                'regular' => 'NotoSans-Regular.ttf',
                'bold' => 'Philosopher-Bold.ttf' // 使用可用的粗体字体
            ],
            'Philosopher' => [
                'regular' => 'Philosopher-Regular.ttf',
                'bold' => 'Philosopher-Bold.ttf'
            ],
            'SourceHanSans' => [
                'regular' => 'SourceHanSansSC-Regular.otf',
                'bold' => 'SourceHanSansSC-Regular.otf' // 粗体文件损坏，使用常规字体
            ],
            'SimHei' => [
                'regular' => 'msyh.ttc',
                'bold' => 'msyh.ttc'
            ]
        ];
        
        if (isset($fontMap[$fontFamily][$fontWeight])) {
            $fontFile = $fontMap[$fontFamily][$fontWeight];
            $fontPath = $this->fontPath . '/' . $fontFile;
            
            if (file_exists($fontPath) && filesize($fontPath) > 0) {
                return $fontPath;
            }
        }
        
        return null;
    }

    /**
     * 保存处理后的图片
     */
    protected function saveProcessedImage($image, $picbookPath)
    {
        $timestamp = time();
        $filename = "processed_{$timestamp}.jpg";
        $outputDir = storage_path('app/public/processed');
        
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }
        
        $outputPath = $outputDir . '/' . $filename;
        $image->save($outputPath, 90); // 90% 质量
        
        return 'storage/processed/' . $filename;
    }

    /**
     * 批量处理绘本页面
     */
    public function batchProcessPages($picbookId, $pages, $userOptions = [], $dedicationText = null)
    {
        $results = [];
        $successCount = 0;
        $failCount = 0;
        
        foreach ($pages as $pageIndex => $page) {
            try {
                $picbookPath = "picbooks/{$picbookId}";
                $pageConfigPath = public_path($picbookPath . '/page_properties.json');
                
                if (!file_exists($pageConfigPath)) {
                    throw new Exception("页面配置文件不存在: {$pageConfigPath}");
                }
                
                $pageConfig = json_decode(file_get_contents($pageConfigPath), true);
                
                $result = $this->processPage($picbookPath, $pageConfig, $userOptions, $dedicationText);
                
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
                
                $results[] = array_merge($result, [
                    'page_index' => $pageIndex,
                    'page_id' => $page['id'] ?? null
                ]);
                
            } catch (Exception $e) {
                $failCount++;
                $results[] = [
                    'success' => false,
                    'page_index' => $pageIndex,
                    'page_id' => $page['id'] ?? null,
                    'message' => '页面处理失败: ' . $e->getMessage()
                ];
                
                Log::error('批量处理页面失败', [
                    'picbook_id' => $picbookId,
                    'page_index' => $pageIndex,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return [
            'success' => $failCount === 0,
            'total' => count($pages),
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'results' => $results
        ];
    }

    /**
     * 处理订单付款后的图片合并和文字处理
     */
    public function processOrderImages($orderId, $dedicationText = null)
    {
        try {
            // 示例数据，实际使用时需要从数据库获取
            $picbookId = 'test'; // 临时使用test目录
            $userOptions = [
                'skin_tone' => 'brown',
                'hair_style' => '1',
                'hair_color' => 'brown'
            ];
            
            // 获取绘本页面信息
            $pages = $this->getOrderPages($orderId);
            
            // 批量处理页面
            $result = $this->batchProcessPages($picbookId, $pages, $userOptions, $dedicationText);
            
            if ($result['success']) {
                // 更新订单状态
                $this->updateOrderProcessingStatus($orderId, 'completed', $result);
                
                Log::info('订单图片处理完成', [
                    'order_id' => $orderId,
                    'processed_pages' => $result['success_count']
                ]);
            } else {
                $this->updateOrderProcessingStatus($orderId, 'failed', $result);
                
                Log::error('订单图片处理失败', [
                    'order_id' => $orderId,
                    'fail_count' => $result['fail_count']
                ]);
            }
            
            return $result;
            
        } catch (Exception $e) {
            Log::error('处理订单图片失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '处理订单图片失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取订单页面信息
     */
    protected function getOrderPages($orderId)
    {
        // 临时返回示例数据
        return [
            ['id' => 1, 'page_number' => 1],
            ['id' => 2, 'page_number' => 2],
        ];
    }

    /**
     * 更新订单处理状态
     */
    protected function updateOrderProcessingStatus($orderId, $status, $result)
    {
        Log::info('订单处理状态已更新', [
            'order_id' => $orderId,
            'status' => $status
        ]);
    }

    /**
     * 更新订单寄语文字（付款后4小时内可修改）
     */
    public function updateOrderDedication($orderId, $dedicationText)
    {
        try {
            // 重新处理订单图片，包含新的寄语文字
            $result = $this->processOrderImages($orderId, $dedicationText);
            
            if ($result['success']) {
                Log::info('订单寄语文字已更新', [
                    'order_id' => $orderId,
                    'dedication_text' => $dedicationText
                ]);
            }
            
            return $result;
            
        } catch (Exception $e) {
            Log::error('更新订单寄语失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '更新寄语失败: ' . $e->getMessage()
            ];
        }
    }
}