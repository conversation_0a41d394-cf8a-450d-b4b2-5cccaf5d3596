<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\Order;
use App\Services\BookService;

class GenerateOrderBooks implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 订单对象
     */
    protected $order;
    
    /**
     * 任务超时时间（秒）
     */
    public $timeout = 600;
    
    /**
     * 任务重试次数
     */
    public $tries = 3;

    /**
     * 创建新的任务实例
     *
     * @param Order $order 订单对象
     * @return void
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
        $this->onQueue('book_generation');
    }

    /**
     * 执行任务
     *
     * @param BookService $bookService
     * @return void
     */
    public function handle(BookService $bookService)
    {
        try {
            Log::info('开始生成订单绘本', [
                'order_id' => $this->order->id,
                'user_id' => $this->order->user_id
            ]);
            
            // 调用绘本服务生成绘本
            $result = $bookService->generateBookFromOrder($this->order);
            
            if ($result['success']) {
                Log::info('订单绘本生成完成', [
                    'order_id' => $this->order->id,
                    'books_count' => count($result['books'] ?? [])
                ]);
                
                // 可以在这里添加发送通知给用户的逻辑
                // notification(new BookGenerationCompleted($this->order));
            } else {
                Log::error('订单绘本生成失败', [
                    'order_id' => $this->order->id,
                    'error' => $result['message'] ?? '未知错误'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('订单绘本生成异常', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 标记订单项为失败
            foreach ($this->order->items as $item) {
                $item->status = 'failed';
                $item->error_message = '绘本生成失败: ' . $e->getMessage();
                $item->save();
            }
            
            throw $e; // 重新抛出异常以便Laravel队列系统可以重试或记录失败
        }
    }
    
    /**
     * 任务失败的处理
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('订单绘本生成任务失败', [
            'order_id' => $this->order->id,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        // 标记订单项为失败（再次确保即使队列系统标记任务为失败也能更新订单状态）
        foreach ($this->order->items as $item) {
            $item->status = 'failed';
            $item->error_message = '绘本生成失败: ' . $exception->getMessage();
            $item->save();
        }
    }
} 