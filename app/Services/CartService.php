<?php

namespace App\Services;

use App\Models\CartItem;
use App\Models\PicbookPreview;
use App\Models\Picbook;
use App\Models\ProductOption;
use Illuminate\Support\Facades\Log;

class CartService
{
    /**
     * 将预览添加到购物车
     *
     * @param int $userId 用户ID
     * @param int $previewId 预览ID
     * @param int $quantity 数量
     * @return CartItem
     */
    public function addToCart(int $userId, int $previewId, int $quantity = 1)
    {
        // 获取预览信息
        $preview = PicbookPreview::findOrFail($previewId);

        // 检查预览状态是否已完成
        // if (!$preview->isCompleted()) {
        //     throw new \Exception(__('picbook.preview_not_completed'));
        // }

        // 检查是否属于当前用户
        if ($preview->user_id !== $userId) {
            throw new \Exception(__('picbook.preview_not_found'));
        }

        // 查找绘本信息
        $picbook = Picbook::findOrFail($preview->picbook_id);

        // 计算选项价格
        $previewService = app(PreviewService::class);
        $priceData = $previewService->getOptionsPrice(
            $preview->cover_type,
            $preview->binding_type,
            $preview->gift_box,
            $preview->picbook_id
        );

        // 检查是否已在购物车中
        $cartItem = CartItem::where('user_id', $userId)
            ->where('preview_id', $previewId)
            ->first();

        if ($cartItem) {
            // 更新数量
            $cartItem->quantity = $quantity;
            $cartItem->save();

            // 计算总价
            $cartItem->calculateTotal();

            return $cartItem;
        }

        // 创建新的购物车项
        $cartItem = new CartItem([
            'user_id' => $userId,
            'preview_id' => $previewId,
            'quantity' => $quantity,
            'price' => $picbook->price,
            'cover_price' => $priceData['cover_price'],
            'binding_price' => $priceData['binding_price'],
            'gift_box_price' => $priceData['gift_box_price'],
            'currency_code' => $priceData['currency_code']
        ]);

        $cartItem->save();

        // 计算总价
        $cartItem->calculateTotal();

        return $cartItem;
    }

    /**
     * 更新购物车项数量
     *
     * @param int $userId 用户ID
     * @param int $cartItemId 购物车项ID
     * @param int $quantity 数量
     * @return CartItem
     */
    public function updateCartItemQuantity(int $userId, int $cartItemId, int $quantity)
    {
        $cartItem = CartItem::where('id', $cartItemId)
            ->where('user_id', $userId)
            ->firstOrFail();

        $cartItem->quantity = max(1, $quantity);
        $cartItem->save();

        // 计算总价
        $cartItem->calculateTotal();

        return $cartItem;
    }

    /**
     * 删除购物车项
     *
     * @param int $userId 用户ID
     * @param int $cartItemId 购物车项ID
     * @return bool
     */
    public function removeCartItem(int $userId, int $cartItemId)
    {
        return CartItem::where('id', $cartItemId)
            ->where('user_id', $userId)
            ->delete();
    }

    /**
     * 清空用户购物车
     *
     * @param int $userId 用户ID
     * @return bool
     */
    public function clearCart(int $userId, array $cartItems)
    {
        return CartItem::where('user_id', $userId)->whereIn('id', $cartItems)->delete();
    }

    /**
     * 获取用户购物车列表
     *
     * @param int $userId 用户ID
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCartItems(int $userId)
    {
        return CartItem::with(['preview', 'preview.picbook'])
            ->where('user_id', $userId)
            ->get();
    }

    /**
     * 获取指定ID的购物车项
     *
     * @param int $userId 用户ID
     * @param array $cartItemIds 购物车项ID数组
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCartItemsByIds(int $userId, array $cartItemIds)
    {
        return CartItem::with(['preview', 'preview.picbook'])
            ->where('user_id', $userId)
            ->whereIn('id', $cartItemIds)
            ->get();
    }

    /**
     * 获取购物车摘要信息
     *
     * @param int $userId 用户ID
     * @return array
     */
    public function getCartSummary(int $userId)
    {
        $items = $this->getCartItems($userId);

        $totalQuantity = $items->sum('quantity');
        $totalAmount = $items->sum('total_price');
        $currencyCode = $items->isNotEmpty() ? $items->first()->currency_code : 'USD';

        return [
            'items_count' => $items->count(),
            'total_quantity' => $totalQuantity,
            'total_amount' => $totalAmount,
            'currency_code' => $currencyCode
        ];
    }

    /**
     * 验证购物车项是否有效
     *
     * @param int $userId 用户ID
     * @param array|null $cartItemIds 指定的购物车项ID数组，如果为null则验证所有购物车项
     * @return array
     */
    public function validateCartItems(int $userId, ?array $cartItemIds = null)
    {
        // 获取购物车项
        $query = CartItem::with(['preview', 'preview.picbook'])
            ->where('user_id', $userId);

        // 如果指定了购物车项ID，则只验证这些项
        if ($cartItemIds !== null) {
            $query->whereIn('id', $cartItemIds);
        }

        $items = $query->get();
        $invalidItems = [];

        foreach ($items as $item) {
            // 检查预览是否仍然有效
            if (!$item->preview || !$item->preview->isCompleted()) {
                $invalidItems[] = [
                    'id' => $item->id,
                    'reason' => __('picbook.preview_not_completed')
                ];
                continue;
            }

            // 检查封面类型是否为整数ID
            if (empty($item->preview->cover) || !is_numeric($item->preview->cover_type)) {
                $invalidItems[] = [
                    'id' => $item->id,
                    'reason' => __('picbook.cover_is_required')
                ];
                continue;
            }

            // 检查封面变体是否存在
            $coverVariant = \App\Models\PicbookCoverVariant::where('id', $item->preview->cover_type)
                ->where('is_published', true)
                ->first();

            if (!$coverVariant) {
                $invalidItems[] = [
                    'id' => $item->id,
                    'reason' => __('picbook.cover_not_found')
                ];
                continue;
            }

            // 检查绘本是否仍然可用
            $picbook = $item->preview->picbook;
            if (!$picbook || $picbook->status != 1) {
                $invalidItems[] = [
                    'id' => $item->id,
                    'reason' => __('picbook.picbook_not_found')
                ];
                continue;
            }

            // 更新价格（如果有变化）
            $previewService = app(PreviewService::class);
            $priceData = $previewService->getOptionsPrice(
                $item->preview->cover_type,
                $item->preview->binding_type,
                $item->preview->gift_box,
                $item->preview->picbook_id
            );

            $item->updatePrices(
                $picbook->price,
                $priceData['cover_price'],
                $priceData['binding_price'],
                $priceData['gift_box_price'],
                $priceData['currency_code']
            );
        }

        return [
            'valid' => count($invalidItems) === 0,
            'invalid_items' => $invalidItems
        ];
    }
}
