<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CurrencyService
{
    protected $apiKey;
    protected $cachePrefix = 'exchange_rate_';
    protected $cacheTtl;
    protected $apiTimeout;

    public function __construct()
    {
        $this->apiKey = config('services.exchange.api_key', env('EXCHANGE_API_KEY'));
        $this->cacheTtl = config('services.exchange.cache_ttl', env('EXCHANGE_RATE_CACHE_TTL', 3600));
        $this->apiTimeout = config('services.exchange.timeout', env('EXCHANGE_API_TIMEOUT', 10));
    }

    /**
     * 获取汇率
     * 
     * @param string $fromCurrency 源币种
     * @param string $toCurrency 目标币种
     * @return float
     */
    public function getExchangeRate($fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        $cacheKey = $this->cachePrefix . strtoupper($fromCurrency) . '_' . strtoupper($toCurrency);
        
        // 尝试从缓存获取
        $rate = Cache::get($cacheKey);
        if ($rate !== null) {
            return $rate;
        }

        // 从API获取汇率
        try {
            $rate = $this->fetchExchangeRateFromApi($fromCurrency, $toCurrency);
            
            // 缓存汇率
            Cache::put($cacheKey, $rate, $this->cacheTtl);
            
            return $rate;
        } catch (\Exception $e) {
            Log::warning('获取实时汇率失败，使用备用汇率', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'error' => $e->getMessage()
            ]);
            
            // 使用备用汇率
            return $this->getFallbackRate($fromCurrency, $toCurrency);
        }
    }

    /**
     * 币种转换
     * 
     * @param float $amount 金额
     * @param string $fromCurrency 源币种
     * @param string $toCurrency 目标币种
     * @return float
     */
    public function convertCurrency($amount, $fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $rate = $this->getExchangeRate($fromCurrency, $toCurrency);
        return round($amount * $rate, 2);
    }

    /**
     * 从API获取汇率
     * 
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float
     */
    private function fetchExchangeRateFromApi($fromCurrency, $toCurrency)
    {
        if (empty($this->apiKey)) {
            throw new \Exception('汇率API密钥未配置');
        }

        // 使用 ExchangeRate-API
        $url = "https://v6.exchangerate-api.com/v6/{$this->apiKey}/pair/{$fromCurrency}/{$toCurrency}";
        
        $response = Http::timeout($this->apiTimeout)->get($url);
        
        if (!$response->successful()) {
            throw new \Exception('汇率API请求失败: ' . $response->status());
        }
        
        $data = $response->json();
        
        if ($data['result'] !== 'success') {
            throw new \Exception('汇率API返回错误: ' . ($data['error-type'] ?? 'Unknown error'));
        }
        
        return $data['conversion_rate'];
    }

    /**
     * 获取备用汇率（固定汇率）
     * 
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float
     */
    private function getFallbackRate($fromCurrency, $toCurrency)
    {
        // 预设的备用汇率表
        $fallbackRates = [
            'CNY_USD' => env('RMB_TO_USD_RATE', 0.14),
            'CNY_EUR' => 0.13,
            'CNY_GBP' => 0.11,
            'USD_EUR' => 0.92,
            'USD_GBP' => 0.79,
            'EUR_GBP' => 0.86,
        ];

        $rateKey = strtoupper($fromCurrency) . '_' . strtoupper($toCurrency);
        
        if (isset($fallbackRates[$rateKey])) {
            return $fallbackRates[$rateKey];
        }
        
        // 尝试反向汇率
        $reverseKey = strtoupper($toCurrency) . '_' . strtoupper($fromCurrency);
        if (isset($fallbackRates[$reverseKey])) {
            return 1 / $fallbackRates[$reverseKey];
        }
        
        // 通过USD作为中间货币
        if ($fromCurrency !== 'USD' && $toCurrency !== 'USD') {
            $fromToUsd = $this->getFallbackRate($fromCurrency, 'USD');
            $usdToTarget = $this->getFallbackRate('USD', $toCurrency);
            return $fromToUsd * $usdToTarget;
        }
        
        // 默认汇率
        Log::warning('未找到备用汇率，使用默认值', [
            'from' => $fromCurrency,
            'to' => $toCurrency
        ]);
        
        return 1.0;
    }

    /**
     * 批量获取汇率
     * 
     * @param string $baseCurrency 基础币种
     * @param array $targetCurrencies 目标币种数组
     * @return array
     */
    public function getBatchExchangeRates($baseCurrency, $targetCurrencies)
    {
        $rates = [];
        
        foreach ($targetCurrencies as $currency) {
            try {
                $rates[$currency] = $this->getExchangeRate($baseCurrency, $currency);
            } catch (\Exception $e) {
                Log::warning('获取汇率失败', [
                    'from' => $baseCurrency,
                    'to' => $currency,
                    'error' => $e->getMessage()
                ]);
                $rates[$currency] = $this->getFallbackRate($baseCurrency, $currency);
            }
        }
        
        return $rates;
    }

    /**
     * 清除汇率缓存
     * 
     * @param string|null $fromCurrency
     * @param string|null $toCurrency
     * @return void
     */
    public function clearCache($fromCurrency = null, $toCurrency = null)
    {
        if ($fromCurrency && $toCurrency) {
            $cacheKey = $this->cachePrefix . strtoupper($fromCurrency) . '_' . strtoupper($toCurrency);
            Cache::forget($cacheKey);
        } else {
            // 清除所有汇率缓存
            Cache::flush(); // 注意：这会清除所有缓存，生产环境中应该更精确
        }
    }

    /**
     * 获取支持的币种列表
     * 
     * @return array
     */
    public function getSupportedCurrencies()
    {
        return [
            'USD' => 'US Dollar',
            'EUR' => 'Euro',
            'GBP' => 'British Pound',
            'CNY' => 'Chinese Yuan',
            'JPY' => 'Japanese Yen',
            'CAD' => 'Canadian Dollar',
            'AUD' => 'Australian Dollar',
            'CHF' => 'Swiss Franc',
            'HKD' => 'Hong Kong Dollar',
            'SGD' => 'Singapore Dollar',
        ];
    }
}