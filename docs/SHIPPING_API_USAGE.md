# 物流费用API使用指南

## 概述

本文档介绍如何使用物流费用相关的API接口，包括获取物流方式、计算费用等功能。

## 配置参数

### 环境变量配置

在 `.env` 文件中配置以下参数：

```env
# 绘本物理参数配置
PICBOOK_WEIGHT=500                    # 单本绘本重量（克）
PICBOOK_LENGTH=20                     # 长度（厘米）
PICBOOK_WIDTH=15                      # 宽度（厘米）
PICBOOK_HEIGHT=2                      # 高度（厘米）

# 包装参数
PICBOOK_PACKAGING_WEIGHT=50           # 包装重量（克）
PICBOOK_PACKAGING_LENGTH=2            # 包装额外长度（厘米）
PICBOOK_PACKAGING_WIDTH=2             # 包装额外宽度（厘米）
PICBOOK_PACKAGING_HEIGHT=1            # 包装额外高度（厘米）

# 物流费用配置
SHIPPING_COST_TOLERANCE=0.01          # 费用校验容错（美元）
SHIPPING_ABNORMAL_THRESHOLD=100.00    # 异常费用阈值（美元）
SHIPPING_DEFAULT_CURRENCY=USD         # 默认货币
```

## API接口

### 3. 计算物流费用

计算指定物流方式的具体费用。

```http
POST /api/order/calculate-shipping
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**

#### 方式1：基于购物车商品计算（推荐）
```json
{
    "shipping_method": "YANWEN_STANDARD",
    "country_code": "US",
    "cart_item_ids": [1, 2, 3]
}
```

#### 方式2：使用自定义参数计算
```json
{
    "shipping_method": "YANWEN_STANDARD",
    "country_code": "US",
    "weight": 1000,
    "dimensions": [25, 20, 4]
}
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "cost": 15.00,
        "currency": "USD",
        "tax": 0,
        "total": 15.00
    },
    "message": "计算物流费用成功"
}
```

## 前端使用示例

### JavaScript/TypeScript 实现

```javascript
class ShippingService {
    constructor(apiBaseUrl, authToken) {
        this.apiBaseUrl = apiBaseUrl;
        this.authToken = authToken;
    }

    // 获取物理参数配置
    // async getPhysicalConfig() {
    //     const response = await fetch(`${this.apiBaseUrl}/api/order/physical-config`, {
    //         headers: {
    //             'Authorization': `Bearer ${this.authToken}`,
    //             'Accept': 'application/json'
    //         }
    //     });
        
    //     return await response.json();
    // }

    // 获取可用物流方式
    async getShippingMethods(countryCode, options = {}) {
        const params = new URLSearchParams({
            country_code: countryCode,
            ...options
        });

        const response = await fetch(`${this.apiBaseUrl}/api/order/shipping-methods?${params}`, {
            headers: {
                'Authorization': `Bearer ${this.authToken}`,
                'Accept': 'application/json'
            }
        });
        
        return await response.json();
    }

    // 基于购物车计算物流费用
    async calculateShippingForCart(cartItemIds, shippingMethod, countryCode) {
        const response = await fetch(`${this.apiBaseUrl}/api/order/calculate-shipping`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.authToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                cart_item_ids: cartItemIds,
                shipping_method: shippingMethod,
                country_code: countryCode
            })
        });
        
        return await response.json();
    }

    // 使用自定义参数计算物流费用
    async calculateShippingCustom(shippingMethod, countryCode, weight, dimensions) {
        const response = await fetch(`${this.apiBaseUrl}/api/order/calculate-shipping`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.authToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                shipping_method: shippingMethod,
                country_code: countryCode,
                weight: weight,
                dimensions: dimensions
            })
        });
        
        return await response.json();
    }
}

// 使用示例
const shippingService = new ShippingService('https://api.example.com', 'your-auth-token');

// 获取配置
const config = await shippingService.getPhysicalConfig();
console.log('物理参数配置:', config.data);

// 获取美国的物流方式
const methods = await shippingService.getShippingMethods('US');
console.log('可用物流方式:', methods.data.shipping_methods);

// 基于购物车计算费用
const cartCost = await shippingService.calculateShippingForCart(
    [1, 2, 3], 
    'YANWEN_STANDARD', 
    'US'
);
console.log('购物车物流费用:', cartCost.data);

// 自定义参数计算费用
const customCost = await shippingService.calculateShippingCustom(
    'YANWEN_EXPRESS', 
    'CA', 
    1000, 
    [25, 20, 4]
);
console.log('自定义物流费用:', customCost.data);
```

### React Hook 示例

```jsx
import { useState, useEffect } from 'react';

function useShipping(authToken) {
    const [config, setConfig] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const shippingService = new ShippingService('/api', authToken);

    // 获取配置
    useEffect(() => {
        const fetchConfig = async () => {
            try {
                setLoading(true);
                const result = await shippingService.getPhysicalConfig();
                if (result.success) {
                    setConfig(result.data);
                } else {
                    setError(result.message);
                }
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        if (authToken) {
            fetchConfig();
        }
    }, [authToken]);

    // 获取物流方式
    const getShippingMethods = async (countryCode) => {
        try {
            setLoading(true);
            setError(null);
            const result = await shippingService.getShippingMethods(countryCode);
            return result;
        } catch (err) {
            setError(err.message);
            return null;
        } finally {
            setLoading(false);
        }
    };

    // 计算物流费用
    const calculateShipping = async (cartItemIds, shippingMethod, countryCode) => {
        try {
            setLoading(true);
            setError(null);
            const result = await shippingService.calculateShippingForCart(
                cartItemIds, 
                shippingMethod, 
                countryCode
            );
            return result;
        } catch (err) {
            setError(err.message);
            return null;
        } finally {
            setLoading(false);
        }
    };

    return {
        config,
        loading,
        error,
        getShippingMethods,
        calculateShipping
    };
}

// 组件使用示例
function ShippingCalculator({ cartItems, selectedCountry, authToken }) {
    const { config, loading, error, getShippingMethods, calculateShipping } = useShipping(authToken);
    const [shippingMethods, setShippingMethods] = useState([]);
    const [selectedMethod, setSelectedMethod] = useState(null);
    const [shippingCost, setShippingCost] = useState(null);

    // 获取物流方式
    useEffect(() => {
        if (selectedCountry) {
            getShippingMethods(selectedCountry).then(result => {
                if (result?.success) {
                    setShippingMethods(result.data.shipping_methods);
                    // 默认选择推荐的物流方式
                    const recommended = result.data.shipping_methods.find(m => m.recommended);
                    if (recommended) {
                        setSelectedMethod(recommended);
                    }
                }
            });
        }
    }, [selectedCountry]);

    // 计算物流费用
    useEffect(() => {
        if (cartItems.length > 0 && selectedMethod && selectedCountry) {
            const cartItemIds = cartItems.map(item => item.id);
            calculateShipping(cartItemIds, selectedMethod.code, selectedCountry).then(result => {
                if (result?.success) {
                    setShippingCost(result.data);
                }
            });
        }
    }, [cartItems, selectedMethod, selectedCountry]);

    if (loading) return <div>加载中...</div>;
    if (error) return <div>错误: {error}</div>;

    return (
        <div className="shipping-calculator">
            <h3>选择物流方式</h3>
            
            {/* 显示包裹信息 */}
            {config && (
                <div className="package-info">
                    <p>单本重量: {config.book.weight}{config.book.weight_unit}</p>
                    <p>单本尺寸: {config.book.dimensions.length}×{config.book.dimensions.width}×{config.book.dimensions.height}{config.book.dimension_unit}</p>
                </div>
            )}

            {/* 物流方式选择 */}
            <div className="shipping-methods">
                {shippingMethods.map(method => (
                    <div 
                        key={method.code}
                        className={`method-option ${selectedMethod?.code === method.code ? 'selected' : ''}`}
                        onClick={() => setSelectedMethod(method)}
                    >
                        <div className="method-info">
                            <h4>{method.name} {method.recommended && <span className="recommended">推荐</span>}</h4>
                            <p>{method.description}</p>
                            <p>预计 {method.estimated_days} 个工作日</p>
                        </div>
                        <div className="method-cost">
                            ${method.cost.toFixed(2)}
                        </div>
                    </div>
                ))}
            </div>

            {/* 显示计算的费用 */}
            {shippingCost && (
                <div className="shipping-cost-summary">
                    <h4>物流费用明细</h4>
                    <div className="cost-breakdown">
                        <div>运费: ${shippingCost.cost.toFixed(2)}</div>
                        <div>税费: ${shippingCost.tax.toFixed(2)}</div>
                        <div className="total">总计: ${shippingCost.total.toFixed(2)}</div>
                    </div>
                </div>
            )}
        </div>
    );
}
```

## 错误处理

### 常见错误码

- `422` - 参数验证失败
- `403` - 购物车商品权限不足
- `500` - 服务器内部错误（通常是物流API调用失败）

### 错误响应示例

```json
{
    "success": false,
    "message": "部分购物车商品不存在或无权访问",
    "data": null
}
```

## 最佳实践

### 1. 缓存策略
- 物理参数配置可以缓存较长时间（如1小时）
- 物流方式列表可以缓存中等时间（如30分钟）
- 具体费用计算结果缓存较短时间（如5分钟）

### 2. 用户体验优化
- 在用户选择国家后立即获取物流方式
- 默认选择推荐的物流方式
- 实时计算和显示物流费用
- 提供费用明细展示

### 3. 错误处理
- 网络错误时提供重试机制
- 显示友好的错误提示
- 物流API失败时使用默认费用

### 4. 性能优化
- 使用防抖处理频繁的费用计算请求
- 批量处理多个商品的费用计算
- 合理使用加载状态提示

## 总结

通过这些API接口，前端可以：

1. **灵活配置** - 通过环境变量调整绘本物理参数
2. **准确计算** - 基于实际商品计算物流费用
3. **用户友好** - 提供多种物流选项和实时费用计算
4. **安全可靠** - 后端校验确保费用准确性

这样的设计既保证了系统的灵活性，又确保了费用计算的准确性和安全性。