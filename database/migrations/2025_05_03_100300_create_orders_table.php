<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('order_number')->unique()->comment('订单号');
            $table->decimal('total_amount', 10, 2)->default(0)->comment('订单总金额');
            $table->string('currency_code', 3)->default('USD')->comment('货币代码');
            $table->string('status')->default('pending')->comment('订单状态');
            $table->string('payment_status')->default('pending')->comment('支付状态');
            $table->string('payment_method')->nullable()->comment('支付方式');
            $table->string('payment_id')->nullable()->comment('支付ID/交易号');
            $table->json('shipping_address')->nullable()->comment('收货地址');
            $table->json('billing_address')->nullable()->comment('账单地址');
            $table->string('shipping_method')->nullable()->comment('配送方式');
            $table->decimal('shipping_cost', 10, 2)->default(0)->comment('运费');
            $table->decimal('tax_amount', 10, 2)->default(0)->comment('税费');
            $table->decimal('discount_amount', 10, 2)->default(0)->comment('折扣金额');
            $table->string('coupon_code')->nullable()->comment('优惠券代码');
            $table->text('notes')->nullable()->comment('订单备注');
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->timestamp('cancelled_at')->nullable()->comment('取消时间');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index(['user_id', 'status']);
            $table->index('order_number');
            $table->index('payment_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
}; 