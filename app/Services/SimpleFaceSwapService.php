<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\Events\AiFaceTaskCompleted;
use App\Models\AiFaceTask;
use App\Models\PicbookPage;
use App\Models\PicbookPageVariant;
use App\Models\PicbookPreview;
use App\Traits\ApiResponse;
use Illuminate\Support\Facades\DB;
use Intervention\Image\Facades\Image;
use App\Services\ImageProcessor;

class SimpleFaceSwapService
{
    use ApiResponse;

    /**
     * 缓存相关常量
     */
    const CACHE_PREFIX = 'simple_face_swap:';
    const CACHE_TTL = 3600; // 1小时

    protected $imageProcessor;

    public function __construct(ImageProcessor $imageProcessor)
    {
        $this->imageProcessor = $imageProcessor;
    }

    /**
     * 处理页面变体（文字合并）
     */
    public function processPageVariant($variant, $full_name)
    {
        $result = [
            'success' => true,
            'message' => '处理成功',
            'image_url' => $variant->image_url
        ];

        try {
            // 处理文本元素
            if ($variant->has_text && !empty($variant->text_elements)) {
                $textElements = is_array($variant->text_elements) ? $variant->text_elements : $variant->text_elements;

                if (!is_array($textElements)) {
                    Log::error('文本元素格式错误', [
                        'variant_id' => $variant->id,
                        'text_elements' => $variant->text_elements
                    ]);
                    throw new \Exception('文本元素格式错误');
                }

                foreach ($textElements as &$element) {
                    $element['text'] = $full_name;
                }

                // 应用文本到图像
                $result['image_url'] = $this->imageProcessor->addTextToImage(
                    $result['image_url'],
                    $textElements
                );
            }
        } catch (\Exception $e) {
            Log::error('处理页面变体多角色失败: ' . $e->getMessage(), [
                'variant_id' => $variant->id,
                'trace' => $e->getTraceAsString()
            ]);

            $result['success'] = false;
            $result['message'] = '处理失败: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 创建换脸批次任务
     */
    public function createBatch(array $images, array $faceImages = [], ?int $userId = null, bool $isPriority = false, ?int $totalTasks = null)
    {
        try {
            // 生成批次ID
            $batchId = 'face_' . Str::uuid();

            // 开启数据库事务
            DB::beginTransaction();

            // 创建批次记录
            $batchRecord = AiFaceTask::create([
                'batch_id' => $batchId,
                'user_id' => $userId,
                'face_image_url' => $faceImages, // 直接存储数组，不手动编码
                'status' => 'pending',
                'type' => 'batch',
                'is_priority' => $isPriority,
                'total_tasks' => $totalTasks ?? count($images),
                'completed_tasks' => 0,
                'progress' => 0,
                'created_at' => now()
            ]);

            $taskIds = [];
            $taskIndex = 0;

            // 为每个图片创建任务记录
            foreach ($images as $image) {
                $imageUrl = is_array($image) ? ($image['image_url'] ?? $image['target_image_url'] ?? $image['url'] ?? '') : $image;
                $maskImageUrl = is_array($image) ? ($image['mask_image_url'] ?? null) : null;

                // 根据角色序列选择对应的face_image
                $characterSequence = $image['character_sequence'] ?? [];
                $selectedFaceImage = $this->selectFaceImageForCharacter($faceImages, $characterSequence);

                // 创建任务配置
                $config = [
                    'batch_id' => $batchId,
                    'task_index' => $taskIndex,
                    'character_sequence' => $characterSequence,
                    'selected_face_image_index' => $selectedFaceImage['index'] ?? 0
                ];

                // 如果是数组，提取页面ID和变体ID
                if (is_array($image)) {
                    $config['page_id'] = $image['page_id'] ?? null;
                    $config['variant_id'] = $image['variant_id'] ?? null;
                }

                // 创建任务记录
                $taskRecord = AiFaceTask::create([
                    'batch_id' => $batchId,
                    'user_id' => $userId,
                    'face_image_url' => [$selectedFaceImage['image']], // 直接存储数组
                    'target_image_url' => $imageUrl,
                    'mask_image' => $maskImageUrl,
                    'task_index' => $taskIndex,
                    'status' => 'pending',
                    'type' => 'task',
                    'is_priority' => $isPriority,
                    'page_id' => $config['page_id'] ?? null,
                    'variant_id' => $config['variant_id'] ?? null,
                    'character_sequence' => $characterSequence,
                    'total_tasks' => $totalTasks ?? count($images),
                    'completed_tasks' => 0,
                    'config' => $config
                ]);

                $taskIds[] = $taskRecord->id;
                $taskIndex++;

                Log::info('创建简化换脸任务', [
                    'batch_id' => $batchId,
                    'task_index' => $taskIndex - 1,
                    'task_id' => $taskRecord->id,
                    'mask_image' => $maskImageUrl,
                    'character_sequence' => $characterSequence,
                    'selected_face_image_index' => $selectedFaceImage['index'] ?? 0
                ]);
            }

            // 提交事务
            DB::commit();

            // 缓存批次信息
            Cache::put(self::CACHE_PREFIX . "batch:{$batchId}", [
                'total_tasks' => count($images),
                'user_id' => $userId,
                'face_images' => $faceImages, // 缓存多个face_image
                'status' => 'pending',
                'is_priority' => $isPriority,
                'created_at' => now()->toDateTimeString()
            ], now()->addHours(1));

            // 批次创建完成，等待调度器处理
            Log::info('批次创建成功，等待调度器处理', [
                'batch_id' => $batchId,
                'total_tasks' => count($images),
                'is_priority' => $isPriority,
                'face_images_count' => count($faceImages),
                'message' => '批次已创建，将由调度器按优先级和顺序处理'
            ]);

            return [
                'success' => true,
                'batch_id' => $batchId,
                'task_ids' => $taskIds,
                'total_tasks' => count($images),
                'message' => '批次创建成功'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建换脸批次失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '创建批次失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理批次中的任务
     */
    public function processBatch(string $batchId)
    {
        try {
            // 使用数据库锁机制防止并发处理
            $lockAcquired = DB::transaction(function () use ($batchId) {
                // 检查当前批次状态，避免重复处理
                $currentBatch = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'batch')
                    ->lockForUpdate()
                    ->first();

                if (!$currentBatch) {
                    throw new \Exception("批次不存在: {$batchId}");
                }

                if ($currentBatch->status !== 'processing') {
                    Log::info('批次状态不是processing，跳过处理', [
                        'batch_id' => $batchId,
                        'status' => $currentBatch->status
                    ]);
                    return false;
                }

                return true;
            });

            if (!$lockAcquired) {
                return [
                    'success' => true,
                    'message' => '批次已被处理或状态不正确'
                ];
            }

            // 更新批次状态为处理中
//            AiFaceTask::where('batch_id', $batchId)
//                ->where('type', 'batch')
//                ->update(['status' => 'processing']);

            // 获取批次中的第一个待处理任务
            $nextTask = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'task')
                ->where('status', 'pending')
                ->orderBy('task_index')
                ->first();

            if ($nextTask) {
                Log::info('开始处理批次任务', [
                    'batch_id' => $batchId,
                    'task_id' => $nextTask->id,
                    'task_index' => $nextTask->task_index
                ]);

                // 处理该任务
                $result = $this->processTask($nextTask->id);

                // 检查是否有更多待处理任务
                $pendingCount = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'task')
                    ->where('status', 'pending')
                    ->count();

                // 如果还有待处理任务，再次分派批次处理器
                if ($pendingCount > 0) {
                    // 检查是否是高优先级任务
                    $batchRecord = AiFaceTask::where('batch_id', $batchId)
                        ->where('type', 'batch')
                        ->first();

                    if ($batchRecord && $batchRecord->is_priority) {
                        \App\Jobs\ProcessHighPriorityFaceSwapBatch::dispatch($batchId)
                            ->delay(now()->addSeconds(3));
                    } else {
                        \App\Jobs\ProcessSimpleFaceSwapBatch::dispatch($batchId)
                            ->delay(now()->addSeconds(3));
                    }

                    Log::info('批次中还有待处理任务，已重新分派处理器', [
                        'batch_id' => $batchId,
                        'pending_tasks' => $pendingCount
                    ]);
                } else {
                    Log::info('批次所有任务已处理完成', [
                        'batch_id' => $batchId
                    ]);

                    // 更新批次状态为已完成
                    $this->updateBatchProgress($batchId);
                }

                return $result;
            } else {
                // 没有待处理任务，检查批次是否已完成
                $this->updateBatchProgress($batchId);

                return [
                    'success' => true,
                    'message' => '批次中没有待处理任务'
                ];
            }
        } catch (\Exception $e) {
            Log::error('处理批次任务失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => '处理批次任务失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理单个换脸任务
     */
    public function processTask(int $taskId)
    {
        try {
            // 获取任务记录
            $task = AiFaceTask::find($taskId);
            if (!$task) {
                throw new \Exception('任务不存在');
            }

            // 更新任务状态为处理中
            $task->status = 'processing';
            $task->save();
            Log::info('开始调用换脸API', [
                'task_id' => $taskId,
                'image_url' => $task->target_image_url,
                'face_image_url' => $task->face_image_url
            ]);
            // 调用甲方换脸API
            $result = $this->callFaceSwapApi($task->target_image_url, $task->face_image_url, $taskId);

            if ($result['success']) {
                // 处理成功，生成两种分辨率的图片
                $resultUrls = $this->generateResolutions($result['result_image_url']);

                // 更新任务状态和结果
                $task->status = 'completed';
                $task->result_image_url = $resultUrls['standard'];
                $task->result = [
                    'standard_url' => $resultUrls['standard'],
                    'high_res_url' => $resultUrls['high_res'],
                    'low_res_url' => $resultUrls['low_res']
                ];
                $task->completed_at = now();
                $task->save();

                // 更新批次进度
                $this->updateBatchProgress($task->batch_id);

                // 发送WebSocket通知
                $this->sendTaskCompletedNotification($task);

                // 更新picbook_previews表
                $this->updatePicbookPreview($task->batch_id, $task->status);

                return [
                    'success' => true,
                    'task_id' => $taskId,
                    'result_images' => $resultUrls,
                    'message' => '换脸任务处理成功'
                ];
            } else {
                // 处理失败
                $task->status = 'failed';
                $task->error_message = $result['error'] ?? '未知错误';
                $task->result = ['error' => $result['error']];
                $task->completed_at = now();
                $task->save();

                // 更新批次进度
                $this->updateBatchProgress($task->batch_id);

                // 发送失败WebSocket通知
                $this->sendTaskFailedNotification($task);

                // 更新picbook_previews表
                $this->updatePicbookPreview($task->batch_id, $task->status, $task->error_message);

                return [
                    'success' => false,
                    'task_id' => $taskId,
                    'error' => $result['error'],
                    'message' => '换脸任务处理失败'
                ];
            }

        } catch (\Exception $e) {
            Log::error('处理换脸任务失败', [
                'task_id' => $taskId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 更新任务状态为失败
            try {
                $task = AiFaceTask::find($taskId);
                if ($task) {
                    $task->status = 'failed';
                    $task->error_message = $e->getMessage();
                    $task->result = ['error' => $e->getMessage()];
                    $task->completed_at = now();
                    $task->save();

                    // 发送失败WebSocket通知
                    $this->sendTaskFailedNotification($task);

                    // 更新picbook_previews表
                    $this->updatePicbookPreview($task->batch_id, $task->status, $task->error_message);
                }
            } catch (\Exception $updateEx) {
                Log::error('更新任务状态失败', [
                    'task_id' => $taskId,
                    'error' => $updateEx->getMessage()
                ]);
            }

            return [
                'success' => false,
                'error' => '处理换脸任务失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取队列状态和预估时间
     */
    public function getQueueStatus(int $userId)
    {
        try {
            // 获取用户当前的任务
            $userTasks = AiFaceTask::where('user_id', $userId)
                ->whereIn('status', ['pending', 'processing'])
                ->orderBy('created_at', 'desc')
                ->get();

            // 获取队列中的总任务数
            $totalPendingTasks = AiFaceTask::where('status', 'pending')
                ->where('type', 'task')
                ->count();

            $totalProcessingTasks = AiFaceTask::where('status', 'processing')
                ->where('type', 'task')
                ->count();

            // 获取高优先级队列任务数
            $highPriorityTasks = AiFaceTask::where('status', 'pending')
                ->where('type', 'task')
                ->where('is_priority', true)
                ->count();

            // 计算预估等待时间（每个任务平均处理时间约2分钟）
            $averageProcessingTime = 120; // 秒
            $estimatedWaitTime = ($totalPendingTasks + $totalProcessingTasks) * $averageProcessingTime;

            // 用户任务状态
            $userTasksStatus = $userTasks->map(function ($task) {
                return [
                    'batch_id' => $task->batch_id,
                    'status' => $task->status,
                    'progress' => $task->progress ?? 0,
                    'created_at' => $task->created_at,
                    'is_priority' => $task->is_priority
                ];
            });

            return [
                'success' => true,
                'queue_info' => [
                    'total_pending' => $totalPendingTasks,
                    'total_processing' => $totalProcessingTasks,
                    'high_priority_pending' => $highPriorityTasks,
                    'estimated_wait_time' => $estimatedWaitTime,
                    'estimated_wait_time_formatted' => $this->formatTime($estimatedWaitTime)
                ],
                'user_tasks' => $userTasksStatus,
                'user_tasks_count' => $userTasks->count()
            ];

        } catch (\Exception $e) {
            Log::error('获取队列状态失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取预览页面
     */
    public function getPreviewPages(int $picbookId, string $language, int $gender, int $skincolor)
    {
        try {
            // 获取绘本的预览页
            $previewPages = PicbookPage::where('picbook_id', $picbookId)
                ->where('is_preview', true)
                ->orderBy('page_number')
                ->get();

            if ($previewPages->isEmpty()) {
                throw new \Exception('未找到预览页面');
            }

            $pages = [];
            $faceSwapPages = [];
            $textMergePages = [];

            foreach ($previewPages as $page) {
                // 获取页面变体
                $variant = PicbookPageVariant::where('page_id', $page->id)
                    ->where('language', $language)
                    ->where('gender', $gender)
                    ->whereJsonContains('character_skincolors', [$skincolor])
                    ->first();

                if ($variant && !empty($variant->image_url)) {
                    $pageData = [
                        'page_id' => $page->id,
                        'page_number' => $page->page_number,
                        'has_question' => $page->has_question ?? false,
                        'has_choice' => $page->is_choices ?? false,
                        'choice_type' => $page->choice_type ?? 0,
                        'image_url' => $variant->image_url,
                        'content' => $variant->content,
                        'question' => $variant->question,
                        'choice_options' => $variant->choice_options,
                        'has_text' => $variant->has_text ?? false,
                        'has_face_swap' => !empty($variant->face_config) && !empty($variant->face_config['mask_url']),
                        'character_sequence' => $page->character_sequence ?? []
                    ];

                    $pages[] = $pageData;

                    // 分类页面类型
                    if ($pageData['has_face_swap']) {
                        $faceSwapPages[] = $pageData;
                    }
                    if ($pageData['has_text']) {
                        $textMergePages[] = $pageData;
                    }
                }
            }

            return [
                'success' => true,
                'total_pages' => count($pages),
                'face_swap_pages' => count($faceSwapPages),
                'text_merge_pages' => count($textMergePages),
                'pages' => $pages,
                'face_swap_page_list' => $faceSwapPages,
                'text_merge_page_list' => $textMergePages
            ];

        } catch (\Exception $e) {
            Log::error('获取预览页面失败', [
                'picbook_id' => $picbookId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 创建整本书的换脸任务
     */
    public function createFullBookBatch(int $picbookId, array $faceImages, string $fullName, string $language, int $gender, int $skincolor, int $orderId, int $userId)
    {
        try {
            // 获取绘本的所有页面（不仅仅是预览页）
            $allPages = PicbookPage::where('picbook_id', $picbookId)
                ->orderBy('page_number')
                ->get();

            if ($allPages->isEmpty()) {
                throw new \Exception('未找到绘本页面');
            }

            // 收集需要换脸的图片
            $images = [];
            $totalTasks = 0;

            foreach ($allPages as $page) {
                // 获取页面变体
                $variant = PicbookPageVariant::where('page_id', $page->id)
                    ->where('language', $language)
                    ->where('gender', $gender)
                    ->whereJsonContains('character_skincolors', [$skincolor])
                    ->first();

                if ($variant && !empty($variant->image_url)) {
                    // 检查是否需要换脸
                    if (!empty($variant->face_config) && !empty($variant->face_config['mask_url'])) {
                        $images[] = [
                            'page_id' => $page->id,
                            'variant_id' => $variant->id,
                            'target_image_url' => $variant->image_url,
                            'mask_image_url' => $variant->face_config['mask_url'],
                            'character_sequence' => $page->character_sequence ?? [],
                            'has_text' => $variant->has_text ?? false,
                            'text_elements' => $variant->text_elements ?? []
                        ];
                        $totalTasks++;
                    }
                }
            }

            if (empty($images)) {
                throw new \Exception('未找到需要换脸的页面');
            }

            // 创建高优先级换脸批次（整本书任务优先级更高）
            $result = $this->createBatch($images, $faceImages, $userId, true, $totalTasks);

            if ($result['success']) {
                // 记录订单关联信息
                AiFaceTask::where('batch_id', $result['batch_id'])
                    ->where('type', 'batch')
                    ->update([
                        'config' => [
                            'order_id' => $orderId,
                            'picbook_id' => $picbookId,
                            'full_name' => $fullName,
                            'language' => $language,
                            'gender' => $gender,
                            'skincolor' => $skincolor,
                            'type' => 'full_book'
                        ]
                    ]);

                Log::info('整本书换脸任务创建成功', [
                    'batch_id' => $result['batch_id'],
                    'order_id' => $orderId,
                    'total_tasks' => $totalTasks,
                    'face_images_count' => count($faceImages)
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('创建整本书换脸任务失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取批次状态
     */
    public function getBatchStatus(string $batchId)
    {
        try {
            // 获取批次记录
            $batchRecord = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'batch')
                ->first();

            if (!$batchRecord) {
                throw new \Exception('批次不存在');
            }

            // 获取批次中的所有任务
            $tasks = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'task')
                ->orderBy('task_index')
                ->get();

            $totalTasks = $tasks->count();
            $completedTasks = $tasks->where('status', 'completed')->count();
            $failedTasks = $tasks->where('status', 'failed')->count();
            $processingTasks = $tasks->where('status', 'processing')->count();
            $pendingTasks = $tasks->where('status', 'pending')->count();

            // 计算进度
            $progress = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;

            // 确定批次状态
            $batchStatus = 'pending';
            if ($completedTasks == $totalTasks) {
                $batchStatus = 'completed';
            } elseif ($failedTasks > 0 && ($completedTasks + $failedTasks) == $totalTasks) {
                $batchStatus = 'failed';
            } elseif ($processingTasks > 0 || $completedTasks > 0) {
                $batchStatus = 'processing';
            }

            // 获取已完成任务的结果
            $completedResults = $tasks->where('status', 'completed')->map(function ($task) {
                return [
                    'task_id' => $task->id,
                    'page_id' => $task->page_id,
                    'variant_id' => $task->variant_id,
                    'result_image_url' => $task->result_image_url,
                    'result' => $task->result
                ];
            })->values();

            return [
                'success' => true,
                'batch_id' => $batchId,
                'status' => $batchStatus,
                'progress' => $progress,
                'total_tasks' => $totalTasks,
                'completed_tasks' => $completedTasks,
                'failed_tasks' => $failedTasks,
                'processing_tasks' => $processingTasks,
                'pending_tasks' => $pendingTasks,
                'completed_results' => $completedResults,
                'is_priority' => $batchRecord->is_priority,
                'created_at' => $batchRecord->created_at,
                'updated_at' => $batchRecord->updated_at
            ];

        } catch (\Exception $e) {
            Log::error('获取批次状态失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    /**

     * 调用甲方换脸API
     */
    private function callFaceSwapApi(string $imageUrl, string $faceImageUrl, int $id)
    {
        try {
            Log::info('开始调用换脸API', [
                'image_url' => $imageUrl,
                'face_image_url' => $faceImageUrl
            ]);

            // 验证图片URL
            if (empty($imageUrl) || empty($faceImageUrl)) {
                throw new \Exception('图片URL不能为空');
            }

            // 获取API密钥
            $keyModel = \App\Models\AiApiKey::getAvailableKey();
            $apiKey = $keyModel->api_key;

            // 增加当前任务计数
            $keyModel->incrementTasks();

            // 获取任务记录，检查是否有蒙版图片
            $task = AiFaceTask::find($id);
            $maskImageUrl = $task ? $task->mask_image : null;

            try {
                // 下载目标图片
                $inputImagePath = $this->downloadImage($imageUrl);

                // 下载人脸图片
                $faceImagePath = [];
                foreach ($faceImageUrl as $item) {
                    $temp = $this->downloadImage($item);
                    if (!file_exists($temp) || !filesize($temp)) {
                        throw new \Exception('人脸图片文件无效');
                    }
                    $faceImagePath[] = $temp;
                }


                // 下载蒙版图片（如果有）
                $maskImagePath = null;
                if (!empty($maskImageUrl)) {
                    $maskImagePath = $this->downloadImage($maskImageUrl);
                    Log::info('成功下载蒙版图片', ['mask_image_url' => $maskImageUrl]);
                }

                Log::info('解析后的文件路径', [
                    'input_image_path' => $inputImagePath,
                    'face_image_path' => $faceImagePath,
                    'mask_image_path' => $maskImagePath
                ]);

                // 检查文件是否存在且有效
                if (!file_exists($inputImagePath) || !filesize($inputImagePath)) {
                    throw new \Exception('目标图片文件无效');
                }



                if ($maskImagePath && (!file_exists($maskImagePath) || !filesize($maskImagePath))) {
                    Log::warning('蒙版图片文件无效', ['mask_image_url' => $maskImageUrl]);
                    throw new \Exception('蒙版图片文件无效');
                }
            } catch (\Exception $e) {
                Log::error('下载图片失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw new \Exception('下载图片失败: ' . $e->getMessage());
            }

            // 上传目标图片到甲方服务器
            $inputResponse = Http::attach(
                'file',
                file_get_contents($inputImagePath),
                basename($inputImagePath)
            )->post('https://www.runninghub.cn/task/openapi/upload', [
                'apiKey' => $apiKey,
                'fileType' => 'image'
            ]);

            if (!$inputResponse->successful()) {
                throw new \Exception('上传目标图片失败：' . $inputResponse->json('msg'));
            }
            // 构建请求数据
            $inputFileName = $inputResponse->json('data.fileName');
            $nodeInfoList = [
                [
                    'nodeId' => "238",
                    'fieldName' => "image",
                    'fieldValue' => $inputFileName
                ]
            ];
            // 上传人脸图片到甲方服务器
            foreach ($faceImagePath as $value) {
                $faceResponse = Http::attach(
                    'file',
                    file_get_contents($value),
                    basename($value)
                )->post('https://www.runninghub.cn/task/openapi/upload', [
                    'apiKey' => $apiKey,
                    'fileType' => 'image'
                ]);

                if (!$faceResponse->successful()) {
                    throw new \Exception('上传人脸图片失败：' . $faceResponse->json('msg'));
                }
                $faceFileName = $faceResponse->json('data.fileName');
                $nodeInfoList[] = [
                    [
                        'nodeId' => "240",
                        'fieldName' => "image",
                        'fieldValue' => $faceFileName
                    ]
                ];
            }


            // 上传蒙版图片（如果有）
            $maskFileName = null;
            if ($maskImagePath) {
                $maskResponse = Http::attach(
                    'file',
                    file_get_contents($maskImagePath),
                    basename($maskImagePath)
                )->post('https://www.runninghub.cn/task/openapi/upload', [
                    'apiKey' => $apiKey,
                    'fileType' => 'image'
                ]);

                if (!$maskResponse->successful()) {
                    Log::warning('上传蒙版图片失败，将使用默认蒙版', [
                        'error' => $maskResponse->json('msg')
                    ]);
                } else {
                    $maskFileName = $maskResponse->json('data.fileName');
                    Log::info('成功上传蒙版图片', ['mask_file_name' => $maskFileName]);
                }
            }

            // 如果有蒙版，使用蒙版，否则使用输入图像
            if ($maskFileName) {
                $nodeInfoList[] = [
                    'nodeId' => "239",
                    'fieldName' => "image",
                    'fieldValue' => $maskFileName
                ];
            } else {
                $nodeInfoList[] = [
                    'nodeId' => "239",
                    'fieldName' => "image",
                    'fieldValue' => $inputFileName // 如果没有蒙版，使用输入图像
                ];
            }

            $requestData = [
                'workflowId' => config('services.ai_face.workflow_id'),
                'apiKey' => $apiKey,
                'nodeInfoList' => $nodeInfoList
            ];

            // 发送创建任务请求
            $response = Http::post('https://www.runninghub.cn/task/openapi/create', $requestData);

            if (!$response->successful()) {
                throw new \Exception('创建换脸任务失败：' . $response->json('msg'));
            }

            // 获取任务ID
            $taskId = $response->json('data.taskId');
            //更新ai_face_tasks表的task_id
            AiFaceTask::where('id', $id)->update(['task_id' => $taskId]);

            // 轮询任务结果
            $maxAttempts = 45; // 最大尝试次数
            $attempt = 0;
            $resultImageUrl = null;

            Log::info('开始轮询任务结果', [
                'task_id' => $taskId,
                'max_attempts' => $maxAttempts
            ]);

            while ($attempt < $maxAttempts) {
                $attempt++;

                // 查询任务状态
                try {
                    $apiUrl = config('services.ai_face.api_url', 'https://www.runninghub.cn') . '/task/openapi/outputs';

                    $statusResponse = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $apiKey,
                        'Content-Type' => 'application/json'
                    ])->timeout(30)->post($apiUrl, [
                        'taskId' => $taskId,
                        'apiKey' => $apiKey,
                    ]);

                    if (!$statusResponse->successful()) {
                        Log::warning('查询任务状态请求失败', [
                            'task_id' => $taskId,
                            'attempt' => $attempt,
                            'status_code' => $statusResponse->status()
                        ]);

                        if ($attempt >= $maxAttempts) {
                            throw new \Exception('查询任务状态失败：' . $statusResponse->json('msg'));
                        }

                        sleep(10);
                        continue;
                    }

                    $mainCode = $statusResponse->json('code');

                    // 如果任务完成
                    if ($mainCode === 0) {
                        $resultData = $statusResponse->json('data');

                        // 处理不同格式的结果
                        if (is_array($resultData)) {
                            // 检查是否直接返回数组
                            if (!empty($resultData) && isset($resultData[0]['fileUrl'])) {
                                $resultImageUrl = $resultData[0]['fileUrl'];
                                break;
                            }
                            // 检查data.resultData格式
                            else if (isset($resultData['resultData']) && is_array($resultData['resultData']) &&
                                    !empty($resultData['resultData']) && isset($resultData['resultData'][0]['fileUrl'])) {
                                $resultImageUrl = $resultData['resultData'][0]['fileUrl'];
                                break;
                            }
                        }

                        // 直接从响应中提取任何可能的URL
                        $responseString = json_encode($statusResponse->json());
                        if (preg_match('/https?:\/\/[^\s"\']+\.(png|jpg|jpeg|gif)/i', $responseString, $matches)) {
                            $resultImageUrl = $matches[0];
                            break;
                        }
                    }
                    // 如果任务正在运行
                    else if ($mainCode === 804) {
                        // 继续等待
                    }
                    // 其他错误状态
                    else if ($mainCode !== 0 && $mainCode !== 804) {
                        $errorMsg = $statusResponse->json('msg') ?? '未知错误';
                        Log::warning('换脸任务出现问题', [
                            'task_id' => $taskId,
                            'main_code' => $mainCode,
                            'msg' => $errorMsg
                        ]);
                    }

                } catch (\Exception $e) {
                    Log::warning('轮询过程中发生错误', [
                        'task_id' => $taskId,
                        'attempt' => $attempt,
                        'error' => $e->getMessage()
                    ]);

                    if ($attempt >= $maxAttempts) {
                        throw new \Exception('轮询任务结果失败: ' . $e->getMessage());
                    }
                }

                // 等待一段时间后再次查询
                sleep(10);
            }

            // 清理临时文件
            @unlink($inputImagePath);
            @unlink($faceImagePath);
            if ($maskImagePath) {
                @unlink($maskImagePath);
            }

            if ($resultImageUrl) {
                Log::info('换脸任务处理成功', [
                    'task_id' => $taskId,
                    'result_url' => $resultImageUrl
                ]);
                return [
                    'success' => true,
                    'result_image_url' => $resultImageUrl
                ];
            } else {
                Log::error('换脸任务超时或未返回结果图片URL', [
                    'task_id' => $taskId,
                    'attempts' => $attempt
                ]);
                throw new \Exception('换脸任务超时或未返回结果图片URL');
            }

        } catch (\Exception $e) {
            Log::error('调用换脸API失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => '调用换脸API失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 下载图片到临时文件
     */
    private function downloadImage(string $url)
    {
        // URL验证
        if (empty($url)) {
            throw new \Exception('图片URL不能为空');
        }

        // 创建临时文件
        $tempFile = tempnam(sys_get_temp_dir(), 'img_');

        // 处理URL格式
        if (!preg_match('/^https?:\/\//i', $url)) {
            if (strpos($url, '//') === 0) {
                $url = 'https:' . $url;
            } else if (strpos($url, '/') === 0) {
                $url = config('app.url') . $url;
            }
        }

        try {
            // 下载图片
            $response = Http::timeout(30)->get($url);

            if (!$response->successful()) {
                throw new \Exception('下载图片失败，HTTP状态码: ' . $response->status());
            }

            $imageData = $response->body();

            if (empty($imageData)) {
                throw new \Exception('下载的图片数据为空');
            }

            // 写入临时文件
            file_put_contents($tempFile, $imageData);

            // 验证文件大小
            if (filesize($tempFile) === 0) {
                throw new \Exception('下载的图片文件大小为0');
            }

            return $tempFile;

        } catch (\Exception $e) {
            // 清理临时文件
            @unlink($tempFile);

            Log::error('下载图片失败', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            throw new \Exception('下载图片失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成不同分辨率的图片
     */
    private function generateResolutions(string $imageUrl)
    {
        try {
            // 临时增加内存限制以处理大图片
            ini_set('memory_limit', '512M');

            // // 获取图片信息
            // $imageInfo = @getimagesize($imageUrl);

            // // 检查图片大小，如果超过特定大小，使用原生GD库处理
            // if (!$imageInfo || ($imageInfo[0] * $imageInfo[1] > 5000000)) { // 大于500万像素
            //     // 使用ImageProcessor类处理大图片
            //     $imageProcessor = new ImageProcessor();
            //     $resolutions = [
            //         'high_res' => ['width' => 300, 'height' => 300], // 300dpi
            //         'low_res' => ['width' => 70, 'height' => 70]   // 70dpi
            //     ];

            //     $result = $imageProcessor->generateResolutions($imageUrl, $resolutions);

            //     // 重命名键以匹配预期输出
            //     return [
            //         'standard' => $result['standard'] ?? $result['original'] ?? $imageUrl,
            //         'high_res' => $result['high_res'] ?? $result['original'] ?? $imageUrl,
            //         'low_res' => $result['low_res'] ?? $result['original'] ?? $imageUrl
            //     ];
            // }

            // 下载原始图片
            $tempFile = $this->downloadImage($imageUrl);

            // 使用Intervention/Image处理图片
            $image = Image::make($tempFile);

            // 获取原始尺寸
            $origWidth = $image->width();
            $origHeight = $image->height();

            // 生成结果数组
            $result = [
                'standard' => '',  // 原始分辨率
                'high_res' => '',  // 300dpi
                'low_res' => ''    // 70dpi
            ];

            // 保存原始分辨率图片
            $standardPath = 'processed/' . uniqid() . '_standard.jpg';
            Storage::disk('public')->put($standardPath, file_get_contents($tempFile));
            $result['standard'] = Storage::disk('public')->url($standardPath);

            // 生成300dpi高分辨率图片
            $highResPath = 'processed/' . uniqid() . '_300dpi.jpg';

            // 直接使用Imagick处理，确保DPI正确设置
            try {
                // 创建临时文件用于Intervention Image处理
                $tempFile = tempnam(sys_get_temp_dir(), 'temp_');
                $image->save($tempFile, 100);

                // 使用Imagick重新加载并设置DPI
                $imagick = new \Imagick($tempFile);
                $imagick->setImageResolution(300, 300);
                $imagick->setImageUnits(\Imagick::RESOLUTION_PIXELSPERINCH);
                $imagick->setImageFormat('jpeg');
                $imagick->setImageCompressionQuality(100);

                // 保存到最终临时文件
                $highResTempFile = tempnam(sys_get_temp_dir(), 'high_');
                $imagick->writeImage($highResTempFile);

                // 验证DPI设置
                $verifyImagick = new \Imagick($highResTempFile);
                $resolution = $verifyImagick->getImageResolution();
                $verifyImagick->clear();
                $imagick->clear();

                // 清理临时文件
                @unlink($tempFile);

                Log::info('成功设置高分辨率图片DPI为300', [
                    'actual_resolution' => $resolution
                ]);
            } catch (\Exception $e) {
                Log::warning('设置高分辨率图片DPI失败', ['error' => $e->getMessage()]);
                // 如果Imagick失败，使用原始方法
                $highResTempFile = tempnam(sys_get_temp_dir(), 'high_');
                $image->save($highResTempFile, 100);
            }

            // 上传到存储
            Storage::disk('public')->put($highResPath, file_get_contents($highResTempFile));
            $result['high_res'] = Storage::disk('public')->url($highResPath);

            // 清理临时文件
            @unlink($highResTempFile);

            // 生成72dpi低分辨率图片
            $lowResPath = 'processed/' . uniqid() . '_72dpi.jpg';

            // 直接使用Imagick处理，确保DPI正确设置
            try {
                // 创建临时文件用于Intervention Image处理
                $tempFile = tempnam(sys_get_temp_dir(), 'temp_');
                $image->save($tempFile, 85);

                // 使用Imagick重新加载并设置DPI
                $imagick = new \Imagick($tempFile);
                $imagick->setImageResolution(72, 72);
                $imagick->setImageUnits(\Imagick::RESOLUTION_PIXELSPERINCH);
                $imagick->setImageFormat('jpeg');
                $imagick->setImageCompressionQuality(85);

                // 保存到最终临时文件
                $lowResTempFile = tempnam(sys_get_temp_dir(), 'low_');
                $imagick->writeImage($lowResTempFile);

                // 验证DPI设置
                $verifyImagick = new \Imagick($lowResTempFile);
                $resolution = $verifyImagick->getImageResolution();
                $verifyImagick->clear();
                $imagick->clear();

                // 清理临时文件
                @unlink($tempFile);

                Log::info('成功设置低分辨率图片DPI为72', [
                    'actual_resolution' => $resolution
                ]);
            } catch (\Exception $e) {
                Log::warning('设置低分辨率图片DPI失败', ['error' => $e->getMessage()]);
                // 如果Imagick失败，使用原始方法
                $lowResTempFile = tempnam(sys_get_temp_dir(), 'low_');
                $image->save($lowResTempFile, 85);
            }

            // 上传到存储
            Storage::disk('public')->put($lowResPath, file_get_contents($lowResTempFile));
            $result['low_res'] = Storage::disk('public')->url($lowResPath);

            // 清理临时文件
            @unlink($lowResTempFile);
            @unlink($tempFile);

            return $result;

        } catch (\Exception $e) {
            Log::error('生成不同分辨率图片失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 失败时返回原始图片URL
            return [
                'standard' => $imageUrl,
                'high_res' => $imageUrl,
                'low_res' => $imageUrl
            ];
        }
    }

    /**
     * 更新批次进度
     */
    private function updateBatchProgress(string $batchId)
    {
        try {
            // 获取批次中的所有任务
            $tasks = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'task')
                ->get();

            $totalTasks = $tasks->count();
            $completedTasks = $tasks->whereIn('status', ['completed', 'failed'])->count();
            $progress = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;

            // 更新批次记录
            $batchRecord = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'batch')
                ->first();

            if ($batchRecord) {
                $batchRecord->completed_tasks = $completedTasks;
                $batchRecord->progress = $progress;

                // 如果所有任务都完成了，更新批次状态
                if ($completedTasks >= $totalTasks) {
                    $failedTasks = $tasks->where('status', 'failed')->count();
                    $batchRecord->status = $failedTasks > 0 ? 'completed_with_errors' : 'completed';

                    // 发送批次完成通知
                    $this->sendBatchCompletedNotification($batchRecord);
                }

                $batchRecord->save();
            }

            Log::info('更新批次进度', [
                'batch_id' => $batchId,
                'progress' => $progress,
                'completed_tasks' => $completedTasks,
                'total_tasks' => $totalTasks
            ]);

        } catch (\Exception $e) {
            Log::error('更新批次进度失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新 PicbookPreview 表
     */
    private function updatePicbookPreview(string $batchId, string $status, ?string $errorMessage = null)
    {
        try {
            $preview = PicbookPreview::where('batch_id', $batchId)->first();

            if ($preview) {
                // 获取批次中已完成的任务结果
                $completedTasks = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'task')
                    ->where('status', 'completed')
                    ->get();

                $resultImages = [];
                $pageResults = [];

                foreach ($completedTasks as $task) {
                    if ($task->result_image_url) {
                        $resultImages[] = [
                            'page_id' => $task->page_id,
                            'variant_id' => $task->variant_id,
                            'result_image_url' => $task->result_image_url,
                            'result' => $task->result
                        ];

                        // 建立页面ID到结果的映射
                        $pageResults[$task->page_id] = [
                            'result_image_url' => $task->result_image_url,
                            'result' => $task->result
                        ];
                    }
                }

                // 更新 preview_data 中的图片URL为换脸后的结果
                $previewData = $preview->preview_data ?? [];
                foreach ($previewData as &$pageData) {
                    if (isset($pageResults[$pageData['page_id']])) {
                        // 更新为换脸后的图片
                        $pageData['image_url'] = $pageResults[$pageData['page_id']]['result_image_url'];
                        $pageData['face_swapped'] = true;
                        $pageData['face_swap_result'] = $pageResults[$pageData['page_id']]['result'];
                    }
                }

                // 检查批次是否完全完成
                $totalTasks = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'task')
                    ->count();

                $completedCount = $completedTasks->count();
                $failedCount = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'task')
                    ->where('status', 'failed')
                    ->count();

                // 更新预览状态
                if ($completedCount + $failedCount >= $totalTasks) {
                    // 所有任务都已完成（成功或失败）
                    if ($failedCount > 0) {
                        $preview->status = PicbookPreview::STATUS_FAILED;
                    } else {
                        $preview->status = PicbookPreview::STATUS_COMPLETED;
                    }

                    // 更新 face_swap_batch 状态
                    $faceSwapBatch = $preview->face_swap_batch ?? [];
                    $faceSwapBatch['status'] = $preview->status === PicbookPreview::STATUS_COMPLETED ? 'completed' : 'failed';
                    $faceSwapBatch['completed_tasks'] = $completedCount;
                    $faceSwapBatch['failed_tasks'] = $failedCount;
                    $faceSwapBatch['completed_at'] = now()->toISOString();

                    if ($errorMessage) {
                        $faceSwapBatch['error'] = $errorMessage;
                    }

                    $preview->face_swap_batch = $faceSwapBatch;
                }

                // 保存更新的数据
                $preview->preview_data = $previewData;
                $preview->result_images = $resultImages;
                $preview->save();

                Log::info('更新PicbookPreview成功', [
                    'batch_id' => $batchId,
                    'preview_id' => $preview->id,
                    'status' => $preview->status,
                    'completed_tasks' => $completedCount,
                    'total_tasks' => $totalTasks,
                    'updated_pages' => count($pageResults)
                ]);
            }

        } catch (\Exception $e) {
            Log::error('更新PicbookPreview失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送任务完成通知
     */
    private function sendTaskCompletedNotification(AiFaceTask $task)
    {
        try {
            // 发送WebSocket通知
            event(new \App\Events\FaceSwapTaskCompleted($task));

            Log::info('发送任务完成通知', [
                'user_id' => $task->user_id,
                'batch_id' => $task->batch_id,
                'task_id' => $task->id
            ]);

        } catch (\Exception $e) {
            Log::error('发送任务完成通知失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送任务失败通知
     */
    private function sendTaskFailedNotification(AiFaceTask $task)
    {
        try {
            // 发送WebSocket通知
            event(new \App\Events\FaceSwapTaskFailed($task));

            Log::info('发送任务失败通知', [
                'user_id' => $task->user_id,
                'batch_id' => $task->batch_id,
                'task_id' => $task->id,
                'error' => $task->error_message
            ]);

        } catch (\Exception $e) {
            Log::error('发送任务失败通知失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送批次完成通知
     */
    private function sendBatchCompletedNotification(AiFaceTask $batchRecord)
    {
        try {
            // 获取批次中的所有任务结果
            $tasks = AiFaceTask::where('batch_id', $batchRecord->batch_id)
                ->where('type', 'task')
                ->get();

            $completedTasks = $tasks->where('status', 'completed');
            $failedTasks = $tasks->where('status', 'failed');

            $results = $completedTasks->map(function ($task) {
                return [
                    'task_id' => $task->id,
                    'page_id' => $task->page_id,
                    'result_image_url' => $task->result_image_url,
                    'result' => $task->result
                ];
            })->values()->toArray();

            // 发送WebSocket通知
            event(new \App\Events\FaceSwapBatchCompleted($batchRecord, $results));

            Log::info('发送批次完成通知', [
                'user_id' => $batchRecord->user_id,
                'batch_id' => $batchRecord->batch_id,
                'status' => $batchRecord->status,
                'completed_tasks' => $completedTasks->count(),
                'failed_tasks' => $failedTasks->count()
            ]);

        } catch (\Exception $e) {
            Log::error('发送批次完成通知失败', [
                'batch_id' => $batchRecord->batch_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 格式化时间
     */
    private function formatTime(int $seconds)
    {
        if ($seconds < 60) {
            return $seconds . '秒';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            return $minutes . '分钟';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return $hours . '小时' . ($minutes > 0 ? $minutes . '分钟' : '');
        }
    }

    /**
     * 根据角色序列选择对应的face_image
     */
    private function selectFaceImageForCharacter(array $faceImages, array $characterSequence)
    {
        if (empty($faceImages)) {
            return ['image' => '', 'index' => 0];
        }

        if (empty($characterSequence)) {
            // 如果没有角色序列，使用第一个face_image
            return ['image' => $faceImages[0], 'index' => 0];
        }

        // 取角色序列中的第一个角色索引
        $characterIndex = $characterSequence[0] - 1; // 角色序列从1开始，数组从0开始

        // 确保索引在有效范围内
        $faceImageIndex = min($characterIndex, count($faceImages) - 1);
        $faceImageIndex = max(0, $faceImageIndex);

        return [
            'image' => $faceImages[$faceImageIndex],
            'index' => $faceImageIndex
        ];
    }
}
