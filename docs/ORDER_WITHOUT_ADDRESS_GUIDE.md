# 无地址创建订单功能指南

## 概述

现在支持在创建订单时不提供地址信息，允许用户后期通过更新订单接口填写地址。这样可以提供更灵活的用户体验。

## API 变更

### 1. 创建订单 API

**端点**: `POST /api/order/create`

**变更内容**:
- `shipping_method` 字段改为可选
- 移除了地址必填验证
- 支持无地址创建订单

**请求示例**:

```json
{
  "cart_item_ids": [1, 2, 3],
  "payment_method": "stripe",
  "notes": "这是一个测试订单"
}
```

**响应示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "order": {
      "id": 10,
      "order_number": "PB20250729752294",
      "status": "pending",
      "payment_status": "pending",
      "shipping_address": null,
      "billing_address": null,
      "shipping_cost": 0,
      "total_amount": 29.99,
      "address_status": {
        "needs_shipping_address": true,
        "can_ship": false,
        "has_shipping_address": false,
        "has_billing_address": false
      },
      "permissions": {
        "can_cancel": true,
        "can_update_message": true,
        "can_update_address": true,
        "can_confirm": false,
        "should_auto_confirm": false,
        "can_pay": true,
        "needs_address_before_shipping": false
      }
    },
    "payment_data": {
      "client_secret": "pi_xxx_secret_xxx",
      "payment_intent_id": "pi_xxx",
      "publishable_key": "pk_test_xxx",
      "amount": 2999,
      "currency": "usd"
    }
  }
}
```

### 2. 更新订单地址 API

**端点**: `PUT /api/order/update-address/{id}`

**功能增强**:
- 更新地址时自动重新计算物流费用
- 支持部分地址更新

**请求示例**:

```json
{
  "shipping_address": {
    "first_name": "John Doe",
    "phone": "+1234567890",
    "country": "US",
    "city": "New York",
    "street": "123 Main Street, Apt 4B"
  },
  "use_shipping_as_billing": true
}
```

**响应示例**:

```json
{
  "success": true,
  "code": 200,
  "message": "订单地址更新成功",
  "data": {
    "order": {
      "id": 10,
      "shipping_address": {
        "first_name": "John Doe",
        "phone": "+1234567890",
        "country": "US",
        "city": "New York",
        "street": "123 Main Street, Apt 4B"
      },
      "billing_address": {
        "first_name": "John Doe",
        "phone": "+1234567890",
        "country": "US",
        "city": "New York",
        "street": "123 Main Street, Apt 4B"
      },
      "shipping_cost": 15.00,
      "total_amount": 44.99,
      "address_status": {
        "needs_shipping_address": false,
        "can_ship": false,
        "has_shipping_address": true,
        "has_billing_address": true
      },
      "permissions": {
        "needs_address_before_shipping": false
      }
    }
  }
}
```

## 订单状态说明

### address_status 字段

- `needs_shipping_address`: 是否需要收货地址
- `can_ship`: 是否可以发货（需要地址且已支付）
- `has_shipping_address`: 是否有收货地址
- `has_billing_address`: 是否有账单地址

### permissions 字段新增

- `needs_address_before_shipping`: 是否需要在发货前填写地址

## 业务流程

### 1. 无地址订单创建流程

```
1. 用户选择商品加入购物车
2. 创建订单（不提供地址）
3. 进行支付
4. 支付成功后，提示用户填写地址
5. 用户填写地址，系统重新计算物流费用
6. 订单进入发货流程
```

### 2. 物流费用处理

- **创建时无地址**: 物流费用设为 0
- **更新地址后**: 自动重新计算物流费用并更新订单总金额
- **费用差异**: 如果有费用差异，记录日志但不影响订单处理

## 注意事项

1. **支付处理**: 无地址订单可以正常支付，但发货前必须填写地址
2. **物流费用**: 地址更新后会重新计算物流费用，可能导致总金额变化
3. **订单状态**: 已支付但无地址的订单会在 `permissions.needs_address_before_shipping` 中标记
4. **地址验证**: 更新地址时仍会进行完整的地址格式验证

## 错误处理

### 常见错误

1. **地址格式错误**:
```json
{
  "success": false,
  "code": 422,
  "message": "Validation failed",
  "errors": {
    "shipping_address.country": ["The country field is required when shipping address is present."]
  }
}
```

2. **订单状态不允许更新地址**:
```json
{
  "success": false,
  "code": 400,
  "message": "订单状态不允许修改地址"
}
```

3. **物流费用计算失败**:
- 系统会记录错误日志
- 地址更新仍会成功
- 物流费用保持原值

## 测试建议

1. 测试无地址订单创建
2. 测试支付流程
3. 测试地址更新和费用重新计算
4. 测试各种地址格式验证
5. 测试订单状态变化对地址更新权限的影响