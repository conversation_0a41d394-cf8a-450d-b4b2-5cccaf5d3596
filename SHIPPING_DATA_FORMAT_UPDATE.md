# 物流数据格式更新文档

## 更新背景

根据真实的4PX API响应数据，更新了`getShippingOptions`方法中的数据处理逻辑，以正确处理实际的API响应格式。

## 真实API响应格式

### calculateFreight 方法返回的数据结构
```json
[
  {
    "4px_tracking_no": "",
    "charge_weight": "1.300",
    "estimated_time": "12-25",
    "is_show_track": "Y",
    "is_volume_cargo": "N",
    "logistics_channel_no": "",
    "logistics_product_code": "B1",
    "lump_sum_fee": "383.03",
    "ref_no": "",
    "remarks": ""
  }
]
```

### 关键字段说明
- `lump_sum_fee`: 总费用（人民币）
- `estimated_time`: 预估配送时间（如"12-25"天）
- `logistics_product_code`: 物流产品代码
- `charge_weight`: 计费重量
- `is_show_track`: 是否可跟踪（"Y"/"N"）

## 代码更新内容

### 1. 数据处理逻辑更新

#### 之前的处理（错误）
```php
// 错误：使用不存在的字段
if (!isset($option['freight']) || !isset($option['logistics_product_code'])) {
    continue;
}
$cost = $this->convertCurrency($option['freight'], 'CNY', $userCurrency);
```

#### 现在的处理（正确）
```php
// 正确：使用实际的API字段
if (!isset($option['lump_sum_fee']) || !isset($option['logistics_product_code'])) {
    continue;
}
$cost = $this->convertCurrency($option['lump_sum_fee'], 'CNY', $userCurrency);
```

### 2. 处理后的数据结构
```php
$processedOptions[] = [
    'code' => $option['logistics_product_code'],
    'cost' => $cost,                                    // 转换后的费用
    'original_cost' => $option['lump_sum_fee'],        // 原始费用（CNY）
    'currency' => $userCurrency,                       // 目标币种
    'original_currency' => 'CNY',                      // 原始币种
    'estimated_days' => $option['estimated_time'],     // 直接使用API返回的时间
    'charge_weight' => $option['charge_weight'],       // 计费重量
    'is_trackable' => ($option['is_show_track'] === 'Y'), // 是否可跟踪
    'raw_data' => $option                              // 原始数据
];
```

### 3. 返回格式更新
```php
return [
    'type' => 'express',
    'name' => 'Express Shipping',
    'code' => $option['code'],
    'cost' => $option['cost'],
    'currency' => $option['currency'],
    'original_cost' => $option['original_cost'],
    'original_currency' => $option['original_currency'],
    'estimated_days' => $option['estimated_days'],     // 如"3-7"
    'description' => 'Fastest delivery with tracking',
    'charge_weight' => $option['charge_weight'],        // 新增：计费重量
    'is_trackable' => $option['is_trackable']          // 新增：是否可跟踪
];
```

## 选择算法验证

基于真实数据的选择结果示例：

### Express选择（时间最短）
- 产品代码: D5, E4, E5, A1, S968
- 时间: 3-7天
- 费用范围: 615.70 - 1647.33 CNY
- 选择: D5 (615.70 CNY, 3-7天) - 最快且相对便宜

### Economy选择（价格最低）
- 产品代码: GG
- 时间: 15-35天
- 费用: 230.48 CNY
- 特点: 最便宜但时间较长，不可跟踪

### Standard选择（性价比最高）
- 基于性价比分数计算（天数/价格）
- 排除已选择的Express和Economy选项
- 选择剩余选项中性价比最优的

## 数据验证

### 价格范围分析
- 最低: 230.48 CNY (GG产品)
- 最高: 1647.33 CNY (E4产品)
- 价格差异: 约7倍

### 时间范围分析
- 最快: 3-7天 (多个产品)
- 最慢: 20-35天 (CQ产品)
- 时间差异: 约5倍

### 可跟踪性分析
- 可跟踪: 7个产品
- 不可跟踪: 2个产品 (CQ, GG)

## 币种转换示例

假设汇率 1 CNY = 0.14 USD：
- Express (D5): 615.70 CNY → 86.20 USD
- Economy (GG): 230.48 CNY → 32.27 USD
- Standard: 根据选择的产品而定

## 测试验证

创建了 `test_real_shipping_data.php` 脚本来验证：
1. 数据解析正确性
2. 选择算法有效性
3. 币种转换准确性

## 部署注意事项

1. **数据字段变更**: 确保所有引用物流数据的地方都使用正确的字段名
2. **错误处理**: 验证API响应格式的一致性
3. **测试验证**: 使用真实API数据进行充分测试
4. **监控告警**: 监控数据处理异常

## 后续优化

1. **缓存优化**: 对相同参数的查询结果进行缓存
2. **算法调优**: 根据用户反馈优化选择算法
3. **数据丰富**: 添加更多物流产品信息
4. **性能监控**: 监控数据处理性能