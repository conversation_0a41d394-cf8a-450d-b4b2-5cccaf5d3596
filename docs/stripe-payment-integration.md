# Stripe支付集成使用指南

## 概述

本项目已集成Stripe支付功能，支持两种支付方式：
1. **Stripe Checkout** - 托管支付页面，用户体验简单
2. **Stripe Elements** - 自定义支付表单，更灵活的UI控制

## 配置步骤

### 1. 环境变量配置

在 `.env` 文件中添加Stripe配置：

```env
# Stripe支付配置
STRIPE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### 2. 获取Stripe密钥

1. 登录 [Stripe Dashboard](https://dashboard.stripe.com/)
2. 在左侧菜单选择 "Developers" > "API keys"
3. 复制 "Publishable key" 和 "Secret key"
4. 将密钥添加到 `.env` 文件

### 3. 配置Webhook

1. 在Stripe Dashboard中选择 "Developers" > "Webhooks"
2. 点击 "Add endpoint"
3. 输入端点URL: `https://yourdomain.com/api/stripe/webhook`
4. 选择以下事件：
   - `checkout.session.completed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `charge.dispute.created`
5. 复制 "Signing secret" 到 `.env` 文件的 `STRIPE_WEBHOOK_SECRET`

## API接口说明

### 认证接口

所有支付相关接口都需要用户认证，请在请求头中包含：
```
Authorization: Bearer {your_auth_token}
```

### 主要接口

#### 1. 获取Stripe公钥
```
GET /api/stripe/public-key
```

#### 2. 创建Checkout会话
```
POST /api/stripe/create-checkout-session
Content-Type: application/json

{
    "order_id": 123
}
```

#### 3. 创建PaymentIntent
```
POST /api/stripe/create-payment-intent
Content-Type: application/json

{
    "order_id": 123
}
```

#### 4. 处理支付成功
```
POST /api/stripe/payment-success
Content-Type: application/json

{
    "session_id": "cs_test_...",
    "order_id": 123
}
```

#### 5. 处理支付取消
```
POST /api/stripe/payment-cancel
Content-Type: application/json

{
    "order_id": 123
}
```

#### 6. 获取支付状态
```
GET /api/stripe/payment-status/{orderId}
```

#### 7. Webhook处理
```
POST /api/stripe/webhook
```

## 前端集成

### 方式1: 使用Stripe Checkout（推荐）

```javascript
// 初始化支付客户端
const paymentClient = new StripePaymentClient('/api', authToken);
await paymentClient.initialize();

// 创建订单后，重定向到Stripe Checkout
await paymentClient.payWithCheckout(orderId);
```

### 方式2: 使用自定义表单

```html
<!-- HTML -->
<div id="card-element">
    <!-- Stripe Elements会在这里创建卡片输入框 -->
</div>
<button id="submit-payment">Pay Now</button>
```

```javascript
// JavaScript
const paymentClient = new StripePaymentClient('/api', authToken);
await paymentClient.initialize();

// 创建支付表单
const paymentFunction = await paymentClient.createPaymentForm('#card-element', orderId);

// 处理支付提交
document.getElementById('submit-payment').addEventListener('click', async () => {
    try {
        const result = await paymentFunction({
            name: 'Customer Name',
            email: '<EMAIL>'
        });
        
        if (result.success) {
            // 支付成功，重定向到成功页面
            window.location.href = '/payment/success?order_id=' + orderId;
        }
    } catch (error) {
        // 处理支付错误
        console.error('Payment failed:', error);
    }
});
```

## 测试页面

项目提供了以下测试页面：

1. **支付页面**: `/payment/stripe-checkout?order_id=123`
2. **成功页面**: `/payment/success?session_id=cs_test_...&order_id=123`
3. **取消页面**: `/payment/cancel?order_id=123`

## 支付流程

### Stripe Checkout流程

1. 用户创建订单
2. 前端调用 `/api/stripe/create-checkout-session`
3. 重定向到Stripe托管的支付页面
4. 用户完成支付
5. Stripe重定向回成功/取消页面
6. Webhook通知服务器更新订单状态

### 自定义表单流程

1. 用户创建订单
2. 前端初始化Stripe Elements
3. 用户输入卡片信息
4. 前端调用 `/api/stripe/create-payment-intent`
5. 使用PaymentIntent确认支付
6. Webhook通知服务器更新订单状态

## 错误处理

### 常见错误码

- `400` - 请求参数错误
- `401` - 未认证
- `403` - 无权限访问订单
- `404` - 订单不存在
- `422` - 验证失败
- `500` - 服务器内部错误

### 错误响应格式

```json
{
    "success": false,
    "message": "错误描述",
    "data": null,
    "errors": {
        "field": ["具体错误信息"]
    }
}
```

## 安全注意事项

1. **密钥安全**: 
   - 永远不要在前端代码中暴露Secret Key
   - 使用环境变量存储敏感信息

2. **Webhook验证**: 
   - 始终验证Webhook签名
   - 使用HTTPS端点接收Webhook

3. **订单验证**: 
   - 验证用户对订单的访问权限
   - 检查订单状态防止重复支付

4. **金额验证**: 
   - 在服务器端验证支付金额
   - 不要信任前端传递的金额

## 测试

### 测试卡号

Stripe提供以下测试卡号：

- **成功支付**: `4242424242424242`
- **需要验证**: `4000002500003155`
- **被拒绝**: `4000000000000002`
- **余额不足**: `4000000000009995`

### 测试流程

1. 使用测试密钥配置环境
2. 创建测试订单
3. 使用测试卡号完成支付
4. 检查订单状态更新
5. 验证Webhook事件处理

## 生产环境部署

1. 将测试密钥替换为生产密钥
2. 配置生产环境Webhook端点
3. 确保HTTPS配置正确
4. 监控支付事件和错误日志
5. 设置支付失败通知

## 故障排除

### 常见问题

1. **支付失败**
   - 检查Stripe密钥配置
   - 验证订单状态和金额
   - 查看Stripe Dashboard中的事件日志

2. **Webhook未触发**
   - 确认Webhook URL可访问
   - 检查签名验证
   - 查看Stripe Dashboard中的Webhook日志

3. **订单状态未更新**
   - 检查Webhook处理逻辑
   - 验证订单ID匹配
   - 查看应用程序日志

### 日志监控

重要的日志事件：
- 支付会话创建
- 支付成功/失败
- Webhook事件处理
- 订单状态更新

## 扩展功能

### 可选实现

1. **退款处理**
2. **订阅支付**
3. **多币种支持**
4. **分期付款**
5. **支付方式管理**

这些功能可以根据业务需求逐步添加。