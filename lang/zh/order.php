<?php

return [
    // 订单创建
    'created' => '订单创建成功',
    'create_failed' => '订单创建失败',
    'create_error' => '订单创建错误',
    
    // 订单状态
    'status' => [
        'pending' => '待付款',
        'processing' => '书籍准备中',
        'preparing' => '书籍制作中',
        'confirmed' => '已确认',
        'printed' => '已印刷',
        'packed' => '已打包',
        'shipped' => '已发货',
        'delivered' => '已送达',
        'completed' => '已完成',
        'cancelled' => '已取消',
        'refunded' => '已退款'
    ],
    
    // 支付状态
    'payment_status' => [
        'pending' => '待付款',
        'paid' => '已付款',
        'failed' => '支付失败',
        'refunded' => '已退款',
        'partially_refunded' => '部分退款'
    ],
    
    // 订单操作
    'confirmed' => '订单确认成功',
    'confirm_failed' => '订单确认失败',
    'cancelled' => '订单取消成功',
    'cancel_failed' => '订单取消失败',
    'updated' => '订单更新成功',
    'update_failed' => '订单更新失败',
    'message_updated' => '订单寄语更新成功',
    'address_updated' => '订单地址更新成功',
    
    // 订单查询
    'not_found' => '订单不存在',
    'access_denied' => '您无权访问此订单',
    'invalid' => '无效订单',
    'invalid_status' => '无效订单状态',
    'list_empty' => '暂无订单',
    'detail_success' => '订单详情获取成功',
    'list_success' => '订单列表获取成功',
    
    // 支付相关
    'payment_created' => '支付创建成功',
    'payment_create_failed' => '支付创建失败',
    'payment_success' => '支付成功',
    'payment_failed' => '支付失败',
    'payment_timeout' => '支付超时',
    'payment_cancelled' => '支付已取消',
    'payment_error' => '支付错误',
    
    // 物流相关
    'shipping_methods_success' => '物流方式获取成功',
    'shipping_cost_calculated' => '物流费用计算成功',
    'shipping_cost_validation_failed' => '物流费用校验失败，请重新计算物流费用',
    'shipping_address_required' => '收货地址为必填项',
    'invalid_shipping_method' => '无效的物流方式',
    'shipping_method_updated' => '物流方式更新成功',
    'shipping_service_unavailable' => '物流服务暂时不可用，请稍后重试或联系客服。',
    'shipping_calculation_error' => '暂时无法计算物流费用，请稍后重试。',
    
    // 购物车相关
    'cart_items_required' => '购物车商品为必填项',
    'invalid_cart_items' => '购物车中包含无效商品，请移除后再尝试下单',
    'cart_empty' => '购物车为空，无法创建订单',
    
    // 地址相关
    'address_required' => '地址信息为必填项',
    'invalid_address' => '地址信息无效',
    'address_not_found' => '地址不存在或无权访问',
    
    // 时间限制
    'message_update_expired' => '订单创建超过4小时，无法修改寄语',
    'auto_confirm_pending' => '订单将在48小时后自动确认',
    'confirm_period_expired' => '确认期限已过',
    
    // 处理进度
    'processing_progress' => '订单处理进度获取成功',
    'tracking_info' => '物流跟踪信息获取成功',
    'no_tracking_number' => '此订单暂无物流跟踪号',
    
    // 配置相关
    'physical_config_success' => '物理参数配置获取成功',
    
    // 错误消息
    'processing_error' => '订单处理错误',
    'system_error' => '系统错误',
    'permission_denied' => '权限不足',
    'validation_failed' => '数据验证失败',
    'operation_failed' => '操作失败',
    'data_error' => '数据错误',
    
    // 通用消息
    'success' => '成功',
    'failed' => '失败',
    'processing' => '处理中...',
    'please_wait' => '请稍候...',
    
    // 新增翻译键
    'cancel_reason' => '取消原因',
    'invalid_status_transition' => '无法将订单状态从 :current 变更为 :new',
    'address_field_required' => '地址信息缺少必填字段: :field',
    'invalid_country_code' => '国家代码必须是2位字符',
    'invalid_phone_format' => '电话号码格式不正确',
    'cannot_repay' => '此订单无法重新支付',
    'payment_intent_created' => '支付意图创建成功',
    
    // 物流方式
    'shipping_methods' => [
        'standard' => [
            'name' => '标准物流',
            'description' => '标准国际物流'
        ],
        'express' => [
            'name' => '快速物流',
            'description' => '快速国际物流'
        ]
    ]
];