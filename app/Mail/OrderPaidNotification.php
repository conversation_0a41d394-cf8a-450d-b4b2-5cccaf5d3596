<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\Order;

class OrderPaidNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $order;
    public $user;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
        $this->user = $order->user;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('订单支付成功 - ' . $this->order->order_number)
            ->view('emails.orders.paid')
            ->with([
                'order' => $this->order,
                'user' => $this->user,
                'orderItems' => $this->order->items->load('picbook'),
            ]);
    }
}