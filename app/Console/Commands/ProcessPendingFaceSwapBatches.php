<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AiFaceTask;
use App\Jobs\ProcessSimpleFaceSwapBatch;
use App\Jobs\ProcessHighPriorityFaceSwapBatch;
use Illuminate\Support\Facades\Log;

class  ProcessPendingFaceSwapBatches extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'faceswap:process-pending';

    /**
     * The console command description.
     */
    protected $description = '处理等待中的换脸批次任务';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始检查等待中的换脸批次...');

        // 检查是否有正在处理的批次
        $processingBatch = AiFaceTask::where('type', 'batch')
            ->where('status', 'processing')
            ->first();

        if ($processingBatch) {
            $this->info("有批次正在处理中: {$processingBatch->batch_id}");
            return;
        }

        // 获取下一个待处理的批次（优先级高的优先）
        $nextBatch = AiFaceTask::where('type', 'batch')
            ->where('status', 'pending')
            ->orderBy('is_priority', 'desc') // 高优先级优先
            ->orderBy('created_at', 'asc')   // 创建时间早的优先
            ->first();

        if (!$nextBatch) {
            $this->info('没有等待处理的批次');
            return;
        }

        $this->info("找到待处理批次: {$nextBatch->batch_id} (优先级: " . ($nextBatch->is_priority ? '高' : '普通') . ")");

        // 分派处理任务前，先更新批次状态
        $nextBatch->status = 'processing';
        $nextBatch->save();

        // 分派处理任务
        if ($nextBatch->is_priority) {
            ProcessHighPriorityFaceSwapBatch::dispatch($nextBatch->batch_id);
            $this->info("已分派高优先级批次处理任务");
        } else {
            ProcessSimpleFaceSwapBatch::dispatch($nextBatch->batch_id);
            $this->info("已分派普通批次处理任务");
        }

        Log::info('调度器处理等待批次', [
            'batch_id' => $nextBatch->batch_id,
            'is_priority' => $nextBatch->is_priority,
            'created_at' => $nextBatch->created_at
        ]);
    }
}
