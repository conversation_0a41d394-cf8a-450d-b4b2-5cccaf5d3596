#!/bin/bash

# Laravel 权限诊断脚本
echo "🔍 Laravel 权限诊断报告"
echo "=========================="

# 检查当前用户
echo "👤 当前用户: $(whoami)"
echo "🌐 Web 服务器进程用户:"
ps aux | grep -E "(nginx|apache|php-fpm)" | grep -v grep | awk '{print $1}' | sort | uniq

echo ""
echo "📁 关键目录权限检查:"
echo "-------------------"

# 检查 storage 目录
echo "storage/ 目录:"
if [ -d "storage" ]; then
    ls -ld storage/
    echo "  子目录:"
    ls -la storage/ | grep "^d"
else
    echo "  ❌ storage/ 目录不存在"
fi

echo ""
echo "bootstrap/cache/ 目录:"
if [ -d "bootstrap/cache" ]; then
    ls -ld bootstrap/cache/
    echo "  文件:"
    ls -la bootstrap/cache/
else
    echo "  ❌ bootstrap/cache/ 目录不存在"
fi

echo ""
echo "📝 关键文件权限检查:"
echo "-------------------"

# 检查日志文件
if [ -f "storage/logs/laravel.log" ]; then
    echo "laravel.log:"
    ls -la storage/logs/laravel.log
    echo "  可写性测试:"
    if [ -w "storage/logs/laravel.log" ]; then
        echo "  ✅ 可写"
    else
        echo "  ❌ 不可写"
    fi
else
    echo "❌ storage/logs/laravel.log 不存在"
fi

# 检查缓存文件
echo ""
echo "缓存文件:"
for file in "bootstrap/cache/config.php" "bootstrap/cache/routes.php" "bootstrap/cache/services.php"; do
    if [ -f "$file" ]; then
        echo "$file:"
        ls -la "$file"
        if [ -w "$file" ]; then
            echo "  ✅ 可写"
        else
            echo "  ❌ 不可写"
        fi
    else
        echo "⚠️  $file 不存在"
    fi
done

echo ""
echo "🔧 目录可写性测试:"
echo "-----------------"

# 测试目录写入权限
test_dirs=("storage/logs" "storage/framework/cache" "storage/framework/sessions" "storage/framework/views" "bootstrap/cache")

for dir in "${test_dirs[@]}"; do
    if [ -d "$dir" ]; then
        test_file="$dir/.write_test_$$"
        if touch "$test_file" 2>/dev/null; then
            echo "✅ $dir - 可写"
            rm -f "$test_file"
        else
            echo "❌ $dir - 不可写"
        fi
    else
        echo "⚠️  $dir - 目录不存在"
    fi
done

echo ""
echo "💡 建议的修复命令:"
echo "-----------------"
echo "如果发现权限问题，请运行:"
echo "  ./scripts/fix-permissions.sh"
echo ""
echo "或者手动执行:"
echo "  sudo chown -R www:www storage/ bootstrap/cache/"
echo "  sudo chmod -R 775 storage/ bootstrap/cache/"

echo ""
echo "🎯 诊断完成！"