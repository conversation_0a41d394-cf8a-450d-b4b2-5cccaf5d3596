<?php

namespace App\Services;

use App\Http\Controllers\ImageController;
use App\Models\PicbookCoverVariant;
use App\Models\PicbookPageVariant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\Models\Picbook;
use App\Models\PicbookPage;
use App\Models\PicbookProcessingLog;
use Illuminate\Support\Facades\Storage;


class PicbookImageProcessor
{
    private $cacheEnabled = true;
    private $cacheTtl = 86400; // 24小时
    protected $imageProcessor;

    public function __construct(ImageProcessor $imageProcessor)
    {
        $this->imageProcessor = $imageProcessor;
    }
    
    /**
     * 设置缓存选项
     */
    public function setCacheOptions(bool $enabled, int $ttl = 86400)
    {
        $this->cacheEnabled = $enabled;
        $this->cacheTtl = $ttl;
        return $this;
    }
    
    /**
     * 清除图片处理缓存
     */
    public function clearCache($variantId = null, $userId = null)
    {
        if ($variantId && $userId) {
            $pattern = "picbook_*_{$variantId}_{$userId}_*";
            Cache::deleteMatching($pattern);
        } else if ($variantId) {
            $pattern = "picbook_*_{$variantId}_*";
            Cache::deleteMatching($pattern);
        } else if ($userId) {
            $pattern = "picbook_*_{$userId}_*";
            Cache::deleteMatching($pattern);
        } else {
            $patterns = [
                'picbook_*',
                'skin_merge_*',
                'text_process_*',
                'face_swap_*'
            ];
            foreach ($patterns as $pattern) {
                Cache::deleteMatching($pattern);
            }
        }
    }

    /**
     * 处理页面变体的多角色
     *
     * @param PicbookPageVariant $variant 页面变体
     * @param array $characters 角色数据
     * @return array 处理结果
     */
    public function processPageVariantWithMultipleCharacters($variant, $characters)
    {
        $result = [
            'success' => true,
            'message' => '处理成功',
            'image_url' => $variant->image_url
        ];
        try {
            // 处理文本元素
            if ($variant->has_text && !empty($variant->text_elements)) {
                $textElements = is_array($variant->text_elements) ? $variant->text_elements : $variant->text_elements;
                
                if (!is_array($textElements)) {
                    Log::error('文本元素格式错误', [
                        'variant_id' => $variant->id,
                        'text_elements' => $variant->text_elements
                    ]);
                    throw new \Exception('文本元素格式错误');
                }
                
                foreach ($textElements as &$element) {
                    $characterIndex = ($element['character'] ?? 1) - 1;
                    if (isset($characters[$characterIndex])) {
                        $element['text'] = $characters[$characterIndex]['full_name'];
                    } else {
                        Log::warning('找不到对应的角色数据', [
                            'character_index' => $characterIndex,
                            'characters_count' => count($characters)
                        ]);
                        $element['text'] = '';
                    }
                }
                
                // 应用文本到图像
                $result['image_url'] = $this->imageProcessor->addTextToImage(
                    $result['image_url'],
                    $textElements
                );
            }
        } catch (\Exception $e) {
            Log::error('处理页面变体多角色失败: ' . $e->getMessage(), [
                'variant_id' => $variant->id,
                'trace' => $e->getTraceAsString()
            ]);
            
            $result['success'] = false;
            $result['message'] = '处理失败: ' . $e->getMessage();
        }
        
        return $result;
    }

    /**
     * 处理AI换脸 - 异步方式
     *
     * @param array $faceData 需要处理的换脸数据
     * @param array $characters 角色信息
     * @return array 处理结果
     */
    // public function processFaceSwap(array $faceData, array $characters = [])
    // {
    //     try {
    //         if (empty($faceData)) {
    //             Log::info('无需处理AI换脸任务：数据为空');
    //             return [
    //                 'success' => true,
    //                 'message' => '无需处理AI换脸任务',
    //                 'batch_id' => null
    //             ];
    //         }
            
    //         // 获取当前用户ID
    //         $userId = auth()->id() ?? 0;
            
    //         // 创建批次ID
    //         $batchId = \Illuminate\Support\Str::uuid()->toString();
            
    //         // 获取角色照片URL
    //         $characterPhotoUrl = $characters[0]['photo'] ?? null;
    //         if (empty($characterPhotoUrl)) {
    //             Log::warning('处理AI换脸失败：未提供有效的角色照片', [
    //                 'user_id' => $userId,
    //                 'batch_id' => $batchId
    //             ]);
                
    //             return [
    //                 'success' => false,
    //                 'message' => '未提供有效的角色照片',
    //                 'batch_id' => null
    //             ];
    //         }
            
    //         // 保存批次信息到缓存（用于频道授权）
    //         \Illuminate\Support\Facades\Cache::put("face_swap:batch_info:{$batchId}", [
    //             'user_id' => $userId,
    //             'created_at' => now()->toDateTimeString(),
    //             'total_pages' => count($faceData)
    //         ], now()->addHours(24));
            
    //         // 记录批次信息
    //         Log::info('开始异步处理AI换脸任务', [
    //             'batch_id' => $batchId,
    //             'user_id' => $userId,
    //             'pages_count' => count($faceData)
    //         ]);
            
    //         // 创建批次任务
    //         \App\Jobs\ProcessFaceSwapBatch::dispatch($batchId, $faceData, $characterPhotoUrl, $userId);
            
    //         return [
    //             'success' => true,
    //             'message' => '已将AI换脸任务加入队列',
    //             'batch_id' => $batchId,
    //             'total_pages' => count($faceData)
    //         ];
    //     } catch (\Exception $e) {
    //         Log::error('处理AI换脸任务失败', [
    //             'error' => $e->getMessage(),
    //             'trace' => $e->getTraceAsString()
    //         ]);
            
    //         return [
    //             'success' => false,
    //             'message' => '处理AI换脸任务失败: ' . $e->getMessage(),
    //             'batch_id' => null
    //         ];
    //     }
    // }
    
    /**
     * 获取AI换脸进度
     *
     * @param string $batchId 批次ID
     * @return array 进度信息
     */
    // public function getFaceSwapProgress(string $batchId)
    // {
    //     // 从缓存中获取进度信息
    //     $progress = \Illuminate\Support\Facades\Cache::get("face_swap:progress:{$batchId}", [
    //         'completed' => 0,
    //         'total' => 0,
    //         'progress' => 0,
    //         'updated_at' => now()->toDateTimeString()
    //     ]);
        
    //     // 获取批次状态
    //     $status = \Illuminate\Support\Facades\Cache::get("face_swap:batch_status:{$batchId}", 'pending');
        
    //     // 获取队列位置
    //     $queuePosition = \Illuminate\Support\Facades\Cache::get("face_swap:queue_position:{$batchId}", null);
        
    //     // 获取所有处理结果
    //     $results = [];
    //     if ($progress['completed'] > 0) {
    //         // 获取所有已处理页面的结果
    //         for ($variantId = 1; $variantId <= 1000; $variantId++) {
    //             $resultKey = "face_swap:result:{$batchId}:{$variantId}";
    //             $result = \Illuminate\Support\Facades\Cache::get($resultKey);
    //             if ($result) {
    //                 $results[] = $result;
    //             }
    //         }
    //     }
        
    //     // 按页码排序
    //     usort($results, function($a, $b) {
    //         return ($a['page_number'] ?? 0) <=> ($b['page_number'] ?? 0);
    //     });
        
    //     return [
    //         'success' => true,
    //         'batch_id' => $batchId,
    //         'status' => $status,
    //         'completed' => $progress['completed'],
    //         'total' => $progress['total'],
    //         'progress' => $progress['progress'],
    //         'queue_position' => $queuePosition,
    //         'results' => $results,
    //         'updated_at' => $progress['updated_at']
    //     ];
    // }
} 