<?php

return [
    // 通用消息
    'list_success' => '成功获取绘本列表',
    'show_success' => '成功获取绘本详情',
    'created' => '绘本创建成功',
    'updated' => '绘本更新成功',
    'deleted' => '绘本删除成功',
    'not_found' => '绘本不存在',
    'not_accessible' => '您无权访问此绘本',
    'restore_success' => '恢复绘本成功',
    'force_delete_success' => '永久删除绘本成功',
    'restore_failed' => '恢复绘本失败',
    'force_delete_failed' => '永久删除绘本失败',
    'create_failed' => '创建绘本失败',
    'update_failed' => '更新绘本失败',
    'delete_failed' => '删除绘本失败',
    'variant_exists' => '该变体组合已存在',
    'page_variant_exists' => '该页面变体组合已存在',
    'skincolors_count_mismatch' => '提供的肤色数量(:provided)与角色数量(:required)不匹配',
    
    // 预览相关
    'preview_success' => '成功生成绘本预览',
    'preview_failed' => '预览生成失败',
    'options_success' => '成功获取绘本选项',
    'preview_limit_reached' => '您今日预览次数已达上限，请明天再试',
    'preview_not_found' => '预览记录不存在',
    'update_options_success' => 'Successfully updated picbook options',
    'get_options_success' => 'Successfully retrieved picbook options',
    'get_options_price_success' => 'Successfully retrieved picbook options price',
    'preview_processing' => '',
    'get_preview_list_success' => '',
    'get_preview_detail_success' => '',
    
    // 角色相关
    'characters_count_mismatch' => '角色数量不匹配，提供了 :provided 个，需要 :required 个',
    'language_not_supported' => '不支持的语言',
    'gender_not_supported' => '不支持的性别',
    'skincolor_not_supported' => '不支持的肤色',
    'language_not_supported_for_character' => '角色 :index 不支持该语言',
    'gender_not_supported_for_character' => '角色 :index 不支持该性别',
    'skincolor_not_supported_for_character' => '角色 :index 不支持该肤色',
    'variant_not_found' => '未找到绘本变体',
    'variant_not_found_for_character' => '未找到角色 :index 的变体',
    'character_invalid_name' => '无效的角色名称',
    'character_invalid_photo' => '无效的角色照片',
    'character_photo_too_large' => '角色照片太大',
    'character_photo_wrong_format' => '不支持的角色照片格式',
    'mismatch_character_masks' => '角色蒙版数量不匹配',
    'page_no_character_sequence' => '页面没有角色序列配置',
    // 页面相关
    'page_not_found' => '页面不存在',
    'invalid_page_ids' => '无效的页面ID',
    'variant_success' => '获取绘本变体成功',
    'pages_success' => '获取绘本页面成功',
    
    // 批量处理相关
    'batch_process_success' => '批量处理成功',
    'batch_process_failed' => '批量处理失败',
    'batch_process_in_progress' => '批量处理正在进行中',
    'batch_publish_success' => '批量发布成功',
    'batch_publish_failed' => '批量发布失败',
    'variants_generated' => '变体生成成功',
    'logs_retrieved' => '成功获取处理日志',
    'generate_variants_failed' => '生成变体失败',
    'missing_support_info' => '绘本缺少必要的支持信息',
    'masks_updated' => '蒙版更新成功',
    'set_masks_failed' => '设置蒙版失败',
    'contents_updated' => '内容更新成功',
    'set_contents_failed' => '设置内容失败',
    
    // 问答相关
    'answer_saved' => '回答保存成功',
    'answer_not_found' => '未找到回答',
    'answer_retrieved' => '成功获取回答',
    'save_answer_failed' => '保存回答失败',
    'get_answer_failed' => '获取回答失败',
    
    // 选择相关
    'choices_retrieved' => '成功获取选择项',
    'choice_pages_success' => '选择页面获取成功',
    'choice_invalid' => '无效的选择',
    
    // 业务规则消息
    'cannot_unpublish' => '已发布的绘本不能取消发布',
    'cannot_delete_published' => '已发布的绘本不能删除',
    
    // 页面相关消息
    'page' => [
        // 基本操作
        'list_success' => '获取页面列表成功',
        'detail_success' => '获取页面详情成功',
        'create_success' => '创建页面成功',
        'update_success' => '更新页面成功',
        'delete_success' => '删除页面成功',
        'restore_success' => '恢复页面成功',
        'force_delete_success' => '永久删除页面成功',
        
        // 错误消息
        'create_failed' => '创建页面失败',
        'update_failed' => '更新页面失败',
        'delete_failed' => '删除页面失败',
        'restore_failed' => '恢复页面失败',
        'force_delete_failed' => '永久删除页面失败',
        
        // 变体相关
        'variant' => [
            'list_success' => '获取页面变体列表成功',
            'detail_success' => '获取页面变体详情成功',
            'create_success' => '创建页面变体成功',
            'update_success' => '更新页面变体成功',
            'delete_success' => '删除页面变体成功',
            'restore_success' => '恢复页面变体成功',
            'force_delete_success' => '永久删除页面变体成功',
            
            // 错误消息
            'create_failed' => '创建页面变体失败',
            'update_failed' => '更新页面变体失败',
            'delete_failed' => '删除页面变体失败',
            'restore_failed' => '恢复页面变体失败',
            'force_delete_failed' => '永久删除页面变体失败',
            'not_found' => '页面变体不存在',
            'exists' => '页面变体已存在',
        ]
    ],

    // 页面变体相关消息
    'page_variant' => [
        'no_sequence_with_masks' => '页面没有角色序列但提供了蒙版',
        'masks_required' => '需要提供角色蒙版',
        'masks_count_mismatch' => '蒙版数量(:masks)与角色序列数量(:sequence)不匹配',
        'invalid_mask_url' => '无效的蒙版URL'
    ],

    // 选择类型相关
    'choices_type' => [
        'min_pages_error' => '选择类型为:type时，总页数不能少于:pages页',
        'type_names' => [
            '1' => '8选4',
            '2' => '16选8'
        ]
    ],

    // 新增消息
    'updated' => '绘本更新成功',
    'batch_process_success' => '批量处理成功',
    'batch_process_failed' => '批量处理失败',
    'batch_process_in_progress' => '批量处理正在进行中',
    'variants_generated' => '变体生成成功',
    'logs_retrieved' => '成功获取处理日志',
    'answer_saved' => '回答保存成功',
    'answer_not_found' => '未找到回答',
    'answer_retrieved' => '成功获取回答',
    'choices_retrieved' => '成功获取选择项',
    'choice_pages_success' => '获取选择页面成功',
    'choice_invalid' => '无效的选择',
    // 封面变体相关消息
    'cover_variant_exists' => '该封面变体组合已存在',
    'cover_variant_create_success' => '创建封面变体成功',
    'cover_variant_create_failed' => '创建封面变体失败',
    'cover_variant_update_success' => '更新封面变体成功',
    'cover_variant_update_failed' => '更新封面变体失败',
    'cover_variant_delete_success' => '删除封面变体成功',
    'cover_variant_delete_failed' => '删除封面变体失败',
    'cover_variants_list_success' => '获取封面变体列表成功',
    'cover_variants_list_failed' => '获取封面变体列表失败',

    // Cart related
    'preview_not_completed' => '预览尚未完成，无法添加到购物车',
    'preview_not_found' => '预览不存在',
]; 