<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\ApiController;
use App\Models\Picbook;
use App\Models\PicbookProcessingLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\PicbookPage;
use App\Models\PicbookPageVariant;
use App\Models\PicbookVariant;
use App\Services\PicbookImageProcessor;

class BatchProcessController extends ApiController
{
    /**
     * 生成绘本变体
     *
     * @param Request $request
     * @param int $picbookId
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateVariants(Request $request, $picbookId)
    {
        $validator = Validator::make($request->all(), [
            'languages' => 'required|array',
            'genders' => 'required|array',
            'skincolors' => 'required|array'
        ]);

        if ($validator->fails()) {
            return $this->error(__('validation.failed'), $validator->errors(), 422);
        }

        try {
            // 先验证绘本是否存在
            $picbook = Picbook::find($picbookId);
            if (!$picbook) {
                return $this->error(__('picbook.not_found'), null, 404);
            }
            
            // 更新支持的语言、性别和肤色
            $picbook->supported_languages = $request->languages;
            $picbook->supported_genders = $request->genders;
            $picbook->supported_skincolors = $request->skincolors;
            $picbook->save();
            
            $createdCount = 0;
            $skippedCount = 0;
            $errorCount = 0;
            $details = [];
            
            // 为每种语言、性别和肤色组合创建绘本变体
            foreach ($request->languages as $language) {
                foreach ($request->genders as $gender) {
                    foreach ($request->skincolors as $skincolor) {
                        try {
                            // 检查变体是否已存在
                            $existingVariant = PicbookVariant::where([
                                'picbook_id' => $picbook->id,
                                'language' => $language,
                                'gender' => $gender,
                                'skincolor' => $skincolor
                            ])->first();
                            
                            if ($existingVariant) {
                                $skippedCount++;
                                continue;
                            }
                            
                            // 创建新变体
                            $variant = new PicbookVariant();
                            $variant->picbook_id = $picbook->id;
                            $variant->language = $language;
                            $variant->gender = $gender;
                            $variant->skincolor = $skincolor;
                            $variant->bookname = $picbook->default_name; // 默认使用绘本的默认名称
                            $variant->cover = $picbook->default_cover; // 默认使用绘本的默认封面
                            $variant->status = 1; // 默认为已发布状态
                            $variant->character_url = $request->character_url; // 人物图片
                            $variant->save();
                            
                            $createdCount++;
                            
                        } catch (\Exception $e) {
                            $errorCount++;
                            Log::error('创建绘本变体失败', [
                                'picbook_id' => $picbook->id,
                                'language' => $language,
                                'gender' => $gender,
                                'skincolor' => $skincolor,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }
            }
            
            $details[] = [
                'created_count' => $createdCount,
                'skipped_count' => $skippedCount,
                'error_count' => $errorCount
            ];
            
            // 创建处理日志
            $log = new PicbookProcessingLog();
            $log->picbook_id = $picbook->id;
            $log->process_type = 'generate_variants';
            $log->status = 1; // 成功
            $log->message = "绘本变体生成完成，创建: {$createdCount}，跳过: {$skippedCount}，失败: {$errorCount}";
            $log->details = [
                'languages' => $request->languages,
                'genders' => $request->genders,
                'skincolors' => $request->skincolors,
                'variants' => $details
            ];
            $log->save();
            
            return $this->success([
                'supported_languages' => $picbook->supported_languages,
                'supported_genders' => $picbook->supported_genders,
                'supported_skincolors' => $picbook->supported_skincolors,
                'created_count' => $createdCount,
                'skipped_count' => $skippedCount,
                'error_count' => $errorCount,
                'details' => $details
            ], __('picbook.variants_generated'));
            
        } catch (\Exception $e) {
            Log::error('生成变体失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $picbookId
            ]);
            
            // 只有在绘本存在时才创建处理日志
            if (Picbook::find($picbookId)) {
                $log = new PicbookProcessingLog();
                $log->picbook_id = $picbookId;
                $log->process_type = 'generate_variants';
                $log->status = 2; // 失败
                $log->message = '变体生成失败: ' . $e->getMessage();
                $log->save();
            }
            
            return $this->error(__('picbook.generate_variants_failed'),['error' => $e->getMessage()],500);
        }
    }

    /**
     * 获取处理日志
     *
     * @param Request $request
     * @param int $picbookId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLogs(Request $request, $picbookId)
    {
        $picbook = Picbook::find($picbookId);
        if (!$picbook) {
            return $this->error(__('picbook.not_found'), null, 404);
        }

        $logs = $picbook->processingLogs()
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page ?? 10);

        return $this->success($logs, __('picbook.logs_retrieved'));
    }

    /**
     * 自动创建页面变体
     *
     * @param Request $request
     * @param int $picbookId
     * @return \Illuminate\Http\JsonResponse
     */
    public function generatePageVariants(Request $request, $picbookId)
    {
        try {
            // 先验证绘本是否存在
            $picbook = Picbook::find($picbookId);
            if (!$picbook) {
                return $this->error(__('picbook.not_found'), null, 404);
            }
            
            // 获取支持的语言、性别和肤色
            $languages = $picbook->supported_languages;
            $genders = $picbook->supported_genders;
            $skincolors = $picbook->supported_skincolors;
            
            if (empty($languages) || empty($genders) || empty($skincolors)) {
                return $this->error(__('picbook.missing_support_info'),['picbook' => '绘本缺少必要的支持信息'],400);
            }
            
            // 获取所有页面
            $pages = $picbook->pages;
            
            $createdCount = 0;
            $skippedCount = 0;
            $errorCount = 0;
            $details = [];
            
            // 遍历所有页面
            foreach ($pages as $page) {
                $pageResult = $this->generateVariantsForPage($page, $languages, $genders, $skincolors);
                
                $createdCount += $pageResult['created_count'];
                $skippedCount += $pageResult['skipped_count'];
                $errorCount += $pageResult['error_count'];
                $details[] = [
                    'page_id' => $page->id,
                    'page_number' => $page->page_number,
                    'created_count' => $pageResult['created_count'],
                    'skipped_count' => $pageResult['skipped_count'],
                    'error_count' => $pageResult['error_count']
                ];
            }
            
            // 创建处理日志
            $log = new PicbookProcessingLog();
            $log->picbook_id = $picbook->id;
            $log->process_type = 'generate_page_variants';
            $log->status = 1; // 成功
            $log->message = "变体生成完成，创建: {$createdCount}，跳过: {$skippedCount}，失败: {$errorCount}";
            $log->details = $details;
            $log->save();
            
            return $this->success([
                'created_count' => $createdCount,
                'skipped_count' => $skippedCount,
                'error_count' => $errorCount,
                'details' => $details
            ], __('picbook.variants_generated'));
            
        } catch (\Exception $e) {
            Log::error('生成页面变体失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $picbookId
            ]);
            
            // 只有在绘本存在时才创建处理日志
            if (Picbook::find($picbookId)) {
                $log = new PicbookProcessingLog();
                $log->picbook_id = $picbookId;
                $log->process_type = 'generate_page_variants';
                $log->status = 2; // 失败
                $log->message = '变体生成失败: ' . $e->getMessage();
                $log->save();
            }
            
            return $this->error(__('picbook.generate_variants_failed'),['error' => $e->getMessage()],500);
        }
    }

    /**
     * 为单个页面生成变体-已用
     *
     * @param PicbookPage $page
     * @param array $languages
     * @param array $genders
     * @param array $skincolors
     * @return array
     */
    private function generateVariantsForPage(PicbookPage $page, array $languages, array $genders, array $skincolors)
    {
        $result = [
            'created_count' => 0,
            'skipped_count' => 0,
            'error_count' => 0
        ];
        
        // 获取页面的角色序列
        $characterSequence = $page->character_sequence ?? [];
        $characterCount = count($characterSequence);
        
        if ($characterCount <= 0) {
            Log::warning('页面没有角色序列配置', [
                'page_id' => $page->id
            ]);
            return $result;
        }
        
        // 生成所有可能的肤色组合
        $allCombinations = $this->generateSkinCombinations($characterCount, $skincolors);
        
        foreach ($languages as $language) {
            foreach ($genders as $gender) {
                foreach ($allCombinations as $combination) {
                    try {
                        // 检查变体是否已存在
                        $existingVariant = PicbookPageVariant::where([
                            'page_id' => $page->id,
                            'language' => $language,
                            'gender' => $gender,
                            'character_skincolors' => $combination
                        ])->first();
                        
                        if ($existingVariant) {
                            Log::info('变体已存在，跳过', [
                                'page_id' => $page->id,
                                'language' => $language,
                                'gender' => $gender,
                                'character_skincolors' => $combination
                            ]);
                            $result['skipped_count']++;
                            continue;
                        }
                        
                        // 创建新变体
                        $variant = new PicbookPageVariant();
                        $variant->page_id = $page->id;
                        $variant->language = $language;
                        $variant->gender = $gender;
                        $variant->character_skincolors = $combination;
                        $variant->image_url = $page->image_url;
                        $variant->skin_mask_url = $page->mask_image_url;
                        $variant->has_text = $page->has_replaceable_text;
                        $variant->text_elements = $page->text_elements;
                        $variant->has_face = $page->is_ai_face;
                        $variant->face_config = [
                            'mask_url' => $page->mask_image_url,
                            'character_sequence' => $characterSequence
                        ];
                        $variant->is_published = true;
                        $variant->variant_type = $page->has_choice ? 2 : ($page->has_question ? 1 : 0);
                        
                        // 使用事务保存变体
                        \DB::transaction(function() use ($variant) {
                            $variant->save();
                        });
                        
                        Log::info('成功创建新变体', [
                            'page_id' => $page->id,
                            'language' => $language,
                            'gender' => $gender,
                            'character_skincolors' => $combination
                        ]);
                        
                        $result['created_count']++;
                        
                    } catch (\Exception $e) {
                        Log::error('创建页面变体失败', [
                            'error' => $e->getMessage(),
                            'page_id' => $page->id,
                            'language' => $language,
                            'gender' => $gender,
                            'character_skincolors' => $combination,
                            'trace' => $e->getTraceAsString()
                        ]);
                        
                        $result['error_count']++;
                    }
                }
            }
        }
        
        return $result;
    }

    /**
     * 生成所有可能的肤色组合（笛卡尔积） - 已用
     *
     * @param int $characterCount 角色数量
     * @param array $skinTypes 肤色类型数组
     * @return array 所有可能的肤色组合
     */
    private function generateSkinCombinations($characterCount, $skinTypes)
    {
        $result = [[]];
        
        for ($i = 0; $i < $characterCount; $i++) {
            $temp = [];
            foreach ($result as $combination) {
                foreach ($skinTypes as $skinType) {
                    $newCombination = $combination;
                    $newCombination[] = $skinType;
                    $temp[] = $newCombination;
                }
            }
            $result = $temp;
        }
        
        return $result;
    }

    /**
     * 根据肤色代码获取肤色类型 - 已用
     *
     * @param int $skincode 肤色代码
     * @return string 肤色类型
     */
    private function getSkinTypeFromCode($skincode)
    {
        $skinTypeMap = [
            1 => 'white',
            2 => 'brown',
            3 => 'black'
        ];
        
        return $skinTypeMap[$skincode] ?? 'white';
    }

    /**
     * 批量设置页面变体蒙版 - 已用
     *
     * @param Request $request
     * @param int $picbookId
     * @return \Illuminate\Http\JsonResponse
     */
    public function setPageVariantsMasks(Request $request, $picbookId)
    {
        $validator = Validator::make($request->all(), [
            'page_id' => 'required|integer',
            'masks' => 'required|array',
            'masks.*.gender' => 'required|integer',
            'masks.*.skin_mask_url' => 'required|string',
            'masks.*.character_masks' => 'required|array',
            'process_images' => 'boolean',
            'batch_size' => 'integer|min:1'
        ]);
        
        if ($validator->fails()) {
            return $this->error($validator->errors(), __('validation.failed'), 422);
        }
        
        try {
            // 先验证绘本是否存在
            $picbook = Picbook::find($picbookId);
            if (!$picbook) {
                return $this->error(__('picbook.not_found'), null, 404);
            }
            
            // 验证页面是否存在
            $page = PicbookPage::where('picbook_id', $picbookId)
                ->where('id', $request->page_id)
                ->first();
                
            if (!$page) {
                Log::error('设置蒙版失败: 找不到指定的页面', [
                    'picbook_id' => $picbookId,
                    'page_id' => $request->page_id
                ]);
                return $this->error(__('picbook.page_not_found'), null, 404);
            }
            
            // 记录初始状态
            Log::info('开始处理页面变体蒙版', [
                'picbook_id' => $picbookId,
                'page_id' => $request->page_id,
                'character_sequence' => $page->character_sequence,
                'request_data' => $request->all()
            ]);
            
            // 获取页面的角色序列
            $characterSequence = $page->character_sequence ?? [];
            $characterCount = count($characterSequence);
            
            if (empty($characterSequence)) {
                Log::warning('页面没有角色序列配置', [
                    'page_id' => $request->page_id
                ]);
                return $this->error(__('picbook.page_no_character_sequence'), null, 400);
            }
            
            // 验证角色数量与提供的蒙版数量是否匹配
            foreach ($request->masks as $maskConfig) {
                if (count($maskConfig['character_masks']) !== $characterCount) {
                    Log::error('角色蒙版数量不匹配', [
                        'page_id' => $request->page_id,
                        'gender' => $maskConfig['gender'],
                        'provided_masks' => count($maskConfig['character_masks']),
                        'required_masks' => $characterCount
                    ]);
                    return $this->error(__('picbook.character_masks_count_mismatch', [
                        'provided' => count($maskConfig['character_masks']),
                        'required' => $characterCount
                    ]), null, 422);
                }
                
                // 验证每个角色的蒙版配置是否完整
                foreach ($maskConfig['character_masks'] as $index => $characterMask) {
                    if (!isset($characterMask['white']) || !isset($characterMask['brown']) || !isset($characterMask['black'])) {
                        Log::error('角色蒙版配置不完整', [
                            'page_id' => $request->page_id,
                            'gender' => $maskConfig['gender'],
                            'character_index' => $index,
                            'mask_config' => $characterMask
                        ]);
                        return $this->error(__('picbook.character_mask_incomplete', [
                            'character_index' => $index + 1
                        ]), null, 422);
                    }
                }
            }
            
            // 更新所有变体
            $updatedCount = 0;
            $queuedCount = 0;
            $errorCount = 0;
            $skippedCount = 0;
            $details = [];
            
            // 设置批处理大小，默认为5
            $batchSize = $request->batch_size ?? 5;
            
            // 生成批处理ID
            $batchId = uniqid('page_' . $page->id . '_', true);
            
            // 记录 process_images 设置
            Log::info('队列处理设置', [
                'process_images' => $request->process_images ? 'true' : 'false',
                'batch_id' => $batchId
            ]);
            
            // 按性别分组处理
            foreach ($request->masks as $maskConfig) {
                $gender = $maskConfig['gender'];
                $baseImageUrl = $maskConfig['skin_mask_url'];
                $characterMasksConfig = $maskConfig['character_masks'];
                
                Log::info('开始处理性别变体', [
                    'page_id' => $request->page_id,
                    'gender' => $gender,
                    'base_image_url' => $baseImageUrl,
                    'batch_id' => $batchId
                ]);
                
                // 获取支持的肤色类型
                $skinTypes = ['white', 'brown', 'black'];
                
                // 生成所有可能的肤色组合
                $allCombinations = $this->generateSkinCombinations($characterCount, $skinTypes);
                
                // 获取指定性别的变体
                $variants = PicbookPageVariant::where('page_id', $page->id)
                    ->where('gender', $gender)
                    ->get();
                
                Log::info('找到需要处理的变体', [
                    'page_id' => $request->page_id,
                    'gender' => $gender,
                    'variant_count' => $variants->count(),
                    'batch_id' => $batchId
                ]);
                
                $genderUpdatedCount = 0;
                $genderQueuedCount = 0;
                $genderErrorCount = 0;
                $genderSkippedCount = 0;
                
                // 分批处理变体
                $variantChunks = $variants->chunk($batchSize);
                $totalVariants = $variants->count();
                
                // 初始化批处理进度信息
                $batchInfo = [
                    'total' => 0, // 将在循环中更新
                    'success' => 0,
                    'failed' => 0,
                    'progress' => 0,
                    'picbook_id' => $picbookId,
                    'page_id' => $page->id,
                    'gender' => $gender,
                    'created_at' => now()->toDateTimeString(),
                ];
                
                Log::info('开始处理变体', [
                    'variants_count' => count($variants),
                    'combinations_count' => count($allCombinations),
                    'gender' => $gender,
                    'batch_id' => $batchId
                ]);
                
                foreach ($variantChunks as $variantChunk) {
                    foreach ($variantChunk as $variant) {
                        try {
                            // 使用变体现有的肤色组合，而不是重新分配
                            $existingSkinColors = $variant->character_skincolors;
                            
                            // 添加更详细的日志
                            Log::info('处理变体', [
                                'variant_id' => $variant->id,
                                'existing_skincolors' => $existingSkinColors,
                                'characterCount' => $characterCount,
                                'batch_id' => $batchId
                            ]);
                            
                            // 如果肤色组合为空，使用默认组合
                            if (empty($existingSkinColors)) {
                                // 创建默认的肤色组合 - 全部使用白色(1)
                                $existingSkinColors = array_fill(0, $characterCount, 1);
                                Log::info('使用默认肤色组合', [
                                    'variant_id' => $variant->id,
                                    'default_skincolors' => $existingSkinColors,
                                    'batch_id' => $batchId
                                ]);
                            }
                            
                            // 确保肤色组合长度匹配
                            if (count($existingSkinColors) != $characterCount) {
                                // 调整肤色组合长度
                                if (count($existingSkinColors) < $characterCount) {
                                    // 如果太短，添加默认值
                                    $existingSkinColors = array_pad($existingSkinColors, $characterCount, 1);
                                } else {
                                    // 如果太长，截断
                                    $existingSkinColors = array_slice($existingSkinColors, 0, $characterCount);
                                }
                                
                                Log::info('调整肤色组合长度', [
                                    'variant_id' => $variant->id,
                                    'adjusted_skincolors' => $existingSkinColors,
                                    'batch_id' => $batchId
                                ]);
                            }
                            
                            // 构建角色蒙版数组，基于变体现有的肤色组合
                            $characterMasks = [];
                            $allSkipped = false;
                            
                            for ($i = 0; $i < $characterCount; $i++) {
                                // 将肤色代码转换为肤色类型（white, brown, black）
                                $skinColorCode = $existingSkinColors[$i];
                                $skinType = $this->getSkinTypeFromCode($skinColorCode);
                                
                                // 如果找不到对应的肤色类型蒙版，尝试使用默认白色肤色
                                if (!isset($characterMasksConfig[$i][$skinType])) {
                                    Log::warning('找不到对应肤色类型的蒙版，尝试使用白色', [
                                        'variant_id' => $variant->id,
                                        'character_index' => $i,
                                        'requested_skin_type' => $skinType,
                                        'fallback_skin_type' => 'white',
                                        'batch_id' => $batchId
                                    ]);
                                    
                                    $skinType = 'white';
                                    $existingSkinColors[$i] = 1; // 更新为白色肤色代码
                                    
                                    // 如果白色也没有，则跳过整个变体
                                    if (!isset($characterMasksConfig[$i][$skinType])) {
                                        Log::error('无法找到任何可用的蒙版，跳过此变体', [
                                            'variant_id' => $variant->id,
                                            'character_index' => $i,
                                            'batch_id' => $batchId
                                        ]);
                                        $allSkipped = true;
                                        break;
                                    }
                                }
                                
                                $characterMasks[] = $characterMasksConfig[$i][$skinType];
                            }
                            
                            if ($allSkipped) {
                                $genderSkippedCount++;
                                $skippedCount++;
                                continue;
                            }
                            
                            try {
                                // 更新变体记录，使用 find 和 save 方法，捕获可能的唯一性约束异常
                                $variantRecord = PicbookPageVariant::find($variant->id);
                                
                                if ($variantRecord) {
                                    // 更新记录
                                    $variantRecord->character_masks = $characterMasks;
                                    $variantRecord->character_skincolors = $existingSkinColors;
                                    $variantRecord->skin_mask_url = $baseImageUrl;
                                    
                                    // 尝试保存，捕获唯一性约束异常
                                    try {
                                        $variantRecord->save();
                                        $genderUpdatedCount++;
                                        $updatedCount++;
                                        
                                        // 如果需要处理图片，则添加到队列
                                        if ($request->process_images) {
                                            // 增加批处理总数
                                            $batchInfo['total']++;
                                            
                                            // 检查 ProcessPageVariantImage 类是否存在
                                            if (!class_exists(\App\Jobs\ProcessPageVariantImage::class)) {
                                                Log::error('ProcessPageVariantImage 类不存在', [
                                                    'variant_id' => $variantRecord->id,
                                                    'batch_id' => $batchId
                                                ]);
                                                continue;
                                            }
                                            
                                            // 记录任务信息
                                            Log::info('任务分派信息', [
                                                'class' => \App\Jobs\ProcessPageVariantImage::class,
                                                'variant_id' => $variantRecord->id,
                                                'batch_id' => $batchId
                                            ]);
                                            
                                            try {
                                                // 分派队列任务
                                                $job = new \App\Jobs\ProcessPageVariantImage(
                                                    $variantRecord->id,
                                                    $baseImageUrl,
                                                    $characterMasks,
                                                    $existingSkinColors,
                                                    $picbookId,
                                                    $page->id,
                                                    $batchId
                                                );
                                                
                                                dispatch($job);
                                                
                                                $genderQueuedCount++;
                                                $queuedCount++;
                                                
                                                Log::info('已将变体添加到图片处理队列', [
                                                    'variant_id' => $variantRecord->id,
                                                    'batch_id' => $batchId
                                                ]);
                                            } catch (\Exception $e) {
                                                Log::error('分派队列任务失败', [
                                                    'error' => $e->getMessage(),
                                                    'variant_id' => $variantRecord->id,
                                                    'trace' => $e->getTraceAsString(),
                                                    'batch_id' => $batchId
                                                ]);
                                            }
                                        } else {
                                            Log::info('未启用图片处理，跳过队列', [
                                                'variant_id' => $variantRecord->id,
                                                'batch_id' => $batchId
                                            ]);
                                        }
                                    } catch (\Exception $e) {
                                        // 捕获唯一性约束异常
                                        Log::warning('变体更新忽略 - 可能的唯一性约束冲突', [
                                            'error' => $e->getMessage(),
                                            'variant_id' => $variantRecord->id,
                                            'character_skincolors' => $existingSkinColors,
                                            'gender' => $gender,
                                            'batch_id' => $batchId
                                        ]);
                                        
                                        // 尝试使用不同的肤色组合
                                        // 这里我们只是跳过，记为 skipped
                                        $genderSkippedCount++;
                                        $skippedCount++;
                                    }
                                } else {
                                    Log::warning('找不到变体记录', [
                                        'variant_id' => $variant->id,
                                        'batch_id' => $batchId
                                    ]);
                                }
                            } catch (\Exception $e) {
                                Log::error('更新页面变体蒙版失败', [
                                    'error' => $e->getMessage(),
                                    'variant_id' => $variant->id,
                                    'gender' => $gender,
                                    'trace' => $e->getTraceAsString(),
                                    'batch_id' => $batchId
                                ]);
                                
                                $genderErrorCount++;
                                $errorCount++;
                            }
                        } catch (\Exception $e) {
                            Log::error('更新页面变体蒙版失败', [
                                'error' => $e->getMessage(),
                                'variant_id' => $variant->id,
                                'gender' => $gender,
                                'trace' => $e->getTraceAsString(),
                                'batch_id' => $batchId
                            ]);
                            
                            $genderErrorCount++;
                            $errorCount++;
                        }
                    }
                    
                    // 每处理完一批，强制进行垃圾回收
                    gc_collect_cycles();
                }
                
                $details[] = [
                    'gender' => $gender,
                    'updated_count' => $genderUpdatedCount,
                    'queued_count' => $genderQueuedCount,
                    'error_count' => $genderErrorCount,
                    'skipped_count' => $genderSkippedCount
                ];
                
                Log::info('完成性别变体处理', [
                    'page_id' => $request->page_id,
                    'gender' => $gender,
                    'updated_count' => $genderUpdatedCount,
                    'queued_count' => $genderQueuedCount,
                    'error_count' => $genderErrorCount,
                    'batch_id' => $batchId
                ]);
                
                // 保存批处理信息到缓存
                if ($request->process_images && $batchInfo['total'] > 0) {
                    $cacheKey = "picbook:image_batch:{$batchId}";
                    cache()->put($cacheKey, $batchInfo, now()->addHours(24));
                }
            }
            
            // 创建处理日志
            $log = new PicbookProcessingLog();
            $log->picbook_id = $picbook->id;
            $log->process_type = 'set_page_variants_masks';
            $log->status = 1; // 成功
            $log->message = "已更新 {$updatedCount} 个变体的蒙版，已加入队列 {$queuedCount} 个图片处理任务";
            $log->details = [
                'page_id' => $page->id,
                'updated_count' => $updatedCount,
                'queued_count' => $queuedCount,
                'error_count' => $errorCount,
                'batch_id' => $batchId,
                'details' => $details
            ];
            $log->save();
            
            return $this->success([
                'updated_count' => $updatedCount,
                'queued_count' => $queuedCount,
                'error_count' => $errorCount,
                'batch_id' => $batchId,
                'details' => $details
            ], __('picbook.masks_updated'));
            
        } catch (\Exception $e) {
            Log::error('设置页面变体蒙版失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $picbookId,
                'page_id' => $request->page_id,
                'trace' => $e->getTraceAsString()
            ]);
            
            // 只有在绘本存在时才创建处理日志
            if (Picbook::find($picbookId)) {
                $log = new PicbookProcessingLog();
                $log->picbook_id = $picbookId;
                $log->process_type = 'set_page_variants_masks';
                $log->status = 2; // 失败
                $log->message = '设置蒙版失败: ' . $e->getMessage();
                $log->save();
            }
            
            return $this->error(__('picbook.set_masks_failed'),['error' => $e->getMessage()],500);
        }
    }

    /**
     * 批量设置页面变体内容
     *
     * @param Request $request
     * @param int $picbookId
     * @return \Illuminate\Http\JsonResponse
     */
    public function setPageVariantsContent(Request $request, $picbookId)
    {
        $validator = Validator::make($request->all(), [
            'page_id' => 'required|integer',
            'contents' => 'required|array',
            'contents.*.language' => 'required|string|size:2',
            'contents.*.content' => 'required|string'
        ]);
        
        if ($validator->fails()) {
            return $this->error($validator->errors(), __('validation.failed'), 422);
        }
        
        try {
            // 先验证绘本是否存在
            $picbook = Picbook::find($picbookId);
            if (!$picbook) {
                return $this->error(__('picbook.not_found'), null, 404);
            }
            
            // 验证页面是否存在
            $page = PicbookPage::where('picbook_id', $picbookId)
                ->where('id', $request->page_id)
                ->first();
                
            if (!$page) {
                Log::error('设置页面内容失败: 找不到指定的页面', [
                    'picbook_id' => $picbookId,
                    'page_id' => $request->page_id
                ]);
                return $this->error(__('picbook.page_not_found'), null, 404);
            }
            
            $updatedCount = 0;
            $details = [];
            
            foreach ($request->contents as $content) {
                $variants = PicbookPageVariant::where('page_id', $page->id)
                    ->where('language', $content['language'])
                    ->get();
                
                $languageUpdatedCount = 0;
                foreach ($variants as $variant) {
                    $variant->content = $content['content'];
                    $variant->save();
                    $languageUpdatedCount++;
                    $updatedCount++;
                }
                
                $details[] = [
                    'language' => $content['language'],
                    'updated_count' => $languageUpdatedCount
                ];
            }
            
            // 创建处理日志
            $log = new PicbookProcessingLog();
            $log->picbook_id = $picbook->id;
            $log->process_type = 'set_page_variants_content';
            $log->status = 1; // 成功
            $log->message = "已更新 {$updatedCount} 个变体的内容";
            $log->details = [
                'page_id' => $page->id,
                'updated_count' => $updatedCount,
                'details' => $details
            ];
            $log->save();
            
            return $this->success([
                'updated_count' => $updatedCount,
                'details' => $details
            ], __('picbook.contents_updated'));
            
        } catch (\Exception $e) {
            Log::error('设置页面变体内容失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $picbookId,
                'page_id' => $request->page_id
            ]);
            
            // 只有在绘本存在时才创建处理日志
            if (Picbook::find($picbookId)) {
                $log = new PicbookProcessingLog();
                $log->picbook_id = $picbookId;
                $log->process_type = 'set_page_variants_content';
                $log->status = 2; // 失败
                $log->message = '设置内容失败: ' . $e->getMessage();
                $log->save();
            }
            
            return $this->error(__('picbook.set_contents_failed'),['error' => $e->getMessage()],500);
        }
    }

    /**
     * 获取批次处理进度
     *
     * @param Request $request
     * @param string $batchId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBatchProgress(Request $request, $batchId)
    {
        $cacheKey = "picbook:image_batch:{$batchId}";
        $batchInfo = cache()->get($cacheKey);
        
        if (!$batchInfo) {
            return $this->error(__('picbook.batch_not_found'), null, 404);
        }
        
        $completed = $batchInfo['success'] + $batchInfo['failed'];
        $isCompleted = $completed >= $batchInfo['total'];
        
        // 计算进度
        $progress = ($batchInfo['total'] > 0) ? ($completed / $batchInfo['total']) * 100 : 0;
        $batchInfo['progress'] = round($progress, 2);
        $batchInfo['is_completed'] = $isCompleted;
        
        // 获取最近的处理日志
        if ($isCompleted && isset($batchInfo['picbook_id'])) {
            $log = PicbookProcessingLog::where('picbook_id', $batchInfo['picbook_id'])
                ->where('process_type', 'process_page_variants_images')
                ->where('details->batch_id', $batchId)
                ->orderBy('created_at', 'desc')
                ->first();
            
            if ($log) {
                $batchInfo['log'] = [
                    'id' => $log->id,
                    'message' => $log->message,
                    'created_at' => $log->created_at->toDateTimeString()
                ];
            }
        }
        
        return $this->success($batchInfo, __('picbook.batch_progress_retrieved'));
    }
} 