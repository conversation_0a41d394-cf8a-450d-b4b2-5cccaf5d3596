<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Stripe Keys
    |--------------------------------------------------------------------------
    |
    | The Stripe publishable and secret key give you access to Stripe's
    | API. The "publishable" key is typically used when interacting with
    | Stripe.js and the "secret" key is used when making server-side requests.
    |
    */

    'key' => env('STRIPE_KEY'),
    'secret' => env('STRIPE_SECRET'),
    'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),

    /*
    |--------------------------------------------------------------------------
    | Stripe Currency
    |--------------------------------------------------------------------------
    |
    | This is the default currency that will be used when generating charges
    | from your application. Of course, you are welcome to use any of the
    | various world currencies that are currently supported via Stripe.
    |
    */

    'currency' => env('STRIPE_CURRENCY', 'usd'),

    /*
    |--------------------------------------------------------------------------
    | Stripe API Version
    |--------------------------------------------------------------------------
    |
    | This is the Stripe API version that will be used when making requests
    | to the Stripe API. You should set this to the latest version that
    | your application supports.
    |
    */

    'api_version' => '2024-06-20',

    /*
    |--------------------------------------------------------------------------
    | Payment Success URL
    |--------------------------------------------------------------------------
    |
    | This is the URL that customers will be redirected to after a successful
    | payment. You can customize this based on your application's needs.
    |
    */

    'success_url' => env('APP_URL') . '/payment/success',

    /*
    |--------------------------------------------------------------------------
    | Payment Cancel URL
    |--------------------------------------------------------------------------
    |
    | This is the URL that customers will be redirected to if they cancel
    | the payment process. You can customize this based on your application's needs.
    |
    */

    'cancel_url' => env('APP_URL') . '/payment/cancel',
];