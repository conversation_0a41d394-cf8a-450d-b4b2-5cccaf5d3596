# SimpleFaceSwapController::createByPicbookId API 文档

## 概述

`createByPicbookId` 是 AI 换脸系统的核心 API，用于通过绘本 ID 创建个性化预览。该 API 支持多语言、多性别、多肤色的个性化绘本预览生成，集成了 AI 换脸技术和实时通知系统。

## API 端点

```
POST /api/simple-face-swap/create-by-picbook
```

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| `picbook_id` | integer | 是 | 绘本ID，必须存在于 picbooks 表中 | `1` |
| `face_image` | string | 是 | 用户上传的人脸图片（Base64 或 URL） | `data:image/jpeg;base64,...` |
| `full_name` | string | 是 | 用户姓名，最大255字符 | `小明` |
| `language` | string | 是 | 语言代码，2位字符 | `zh`, `en` |
| `gender` | integer | 是 | 性别：1=男，2=女 | `1` |
| `skincolor` | integer | 是 | 肤色：1-5 代表不同肤色 | `3` |
| `user_id` | integer | 否 | 用户ID，未提供时使用当前认证用户 | `123` |

## 业务流程

### 完整流程时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API服务器
    participant Queue as 队列系统
    participant AI as AI服务
    participant WS as WebSocket服务

    Client->>API: POST /api/simple-face-swap/create-by-picbook
    API->>API: 参数验证
    API->>API: 检查预览限制
    API->>API: 重复请求检测
    API->>API: 收集预览页面
    API->>Queue: 创建换脸批次任务
    API->>Client: 返回批次ID和预览数据
    
    Note over Client: 建立WebSocket连接
    Client->>WS: 连接 private-user.{userId}
    
    Queue->>Queue: 处理批次任务
    Queue->>AI: 调用换脸API
    AI->>Queue: 返回处理结果
    
    Queue->>WS: 发送任务完成通知
    WS->>Client: face-swap.task.completed
    
    Note over Queue: 检查批次是否完成
    alt 批次完成
        Queue->>WS: 发送批次完成通知
        WS->>Client: face-swap.batch.completed
    end
    
    Note over Client: 显示最终结果
```

### 1. 参数验证阶段
- 验证绘本是否存在
- 检查语言、性别、肤色是否被绘本支持
- 验证用户是否达到每日预览上限（50次/天）

### 2. 重复请求检测
- 使用 MD5 哈希检测相同参数的重复请求
- 已完成的请求直接返回缓存结果
- 处理中的请求返回当前状态

### 3. 预览页面收集
- 获取绘本的所有预览页面（`is_preview = true`）
- 根据语言、性别、肤色筛选对应的页面变体
- 处理文字合并（将用户姓名添加到图片中）
- 识别需要换脸的页面（包含 `face_config.mask_url`）

### 4. 换脸任务创建
- 为每个需要换脸的页面创建 AI 任务
- 生成唯一的批次ID（`face_` + UUID）
- 将任务添加到 `face_swap` 队列（普通优先级）
- 创建 `PicbookPreview` 记录跟踪整个预览状态

### 5. 异步处理流程
- 任务通过 Laravel Horizon 队列系统处理
- 使用 `ProcessSimpleFaceSwapBatch` Job 处理批次
- 调用第三方 AI API（runninghub.cn）进行换脸
- 生成多分辨率结果图片（高清、标准、低清）

## 响应格式

### 成功响应

```json
{
    "success": true,
    "data": {
        "preview_data": [
            {
                "page_id": 1,
                "page_number": 1,
                "has_question": false,
                "has_choice": false,
                "choice_type": 0,
                "image_url": "https://example.com/processed/page1.jpg",
                "content": "故事内容...",
                "question": null,
                "choice_options": null,
                "has_face_swap": true,
                "character_sequence": [1, 2]
            }
        ],
        "characters": [1, 2, 3],
        "face_swap_info": {
            "batch_id": "face_12345678-1234-1234-1234-123456789abc",
            "total_tasks": 5,
            "face_swap_pages": [
                {
                    "page_id": 1,
                    "variant_id": 10,
                    "character_sequence": [1, 2]
                }
            ],
            "status": "processing"
        },
        "preview_id": 456,
        "total_pages": 8,
        "face_swap_pages_count": 5
    },
    "message": "预览创建成功"
}
```

### 错误响应

```json
{
    "success": false,
    "errors": {
        "picbook_id": ["绘本ID不存在"]
    },
    "message": "参数验证失败"
}
```

## 任务状态和通知

### 任务状态
- `pending`: 等待处理
- `processing`: 处理中
- `completed`: 已完成
- `failed`: 处理失败

### 实时通知系统

#### WebSocket 频道认证
系统使用 Laravel Reverb 和 Sanctum 进行 WebSocket 认证：

**认证端点**: `POST /api/broadcasting/auth`
**认证中间件**: `auth:sanctum`

#### 支持的频道和事件

##### 频道和事件总览（Simple 预览实际使用）

| 频道类型 | 频道名称 | 事件名称 | 描述 | 认证要求 | 使用状态 |
|---------|----------|----------|------|----------|----------|
| 用户私有 | `user.{userId}` | `face-swap.task.completed` | 单个任务完成 | 用户本人 | ✅ 使用中 |
| 用户私有 | `user.{userId}` | `face-swap.task.failed` | 单个任务失败 | 用户本人 | ✅ 使用中 |
| 用户私有 | `user.{userId}` | `face-swap.batch.completed` | 批次完成 | 用户本人 | ✅ 使用中 |
| 用户私有 | `user.{userId}` | `face-swap.queue-status` | 队列状态更新 | 用户本人 | 🔶 可选使用 |
| 公共频道 | `face-swap-queue-status` | `face-swap.queue-status` | 全局队列状态 | 所有认证用户 | 🔶 可选使用 |
| 订单私有 | `orders.{userId}` | `order.paid` | 订单支付成功 | 用户本人 | ✅ 订单功能 |

**说明**:
- ✅ 使用中: 当前 Simple 预览功能正在使用
- 🔶 可选使用: 根据需要可以启用
- ❌ 未使用: 以下事件当前未使用，可考虑清理
  - `face-swap.progress` (任务进度更新)
  - `task.failed` (管理员任务失败通知)
  - `face-swap.completed` (旧版兼容事件)

##### 1. 单个任务完成通知
**频道**: `private-user.{user_id}`
**事件**: `face-swap.task.completed`

**通知数据**:
```json
{
    "type": "face_swap_task_completed",
    "batch_id": "face_12345678-1234-1234-1234-123456789abc",
    "task_id": 789,
    "page_id": 1,
    "variant_id": 10,
    "result_image_url": "https://example.com/result/standard_1.jpg",
    "result": {
        "standard_url": "https://example.com/result/standard_1.jpg",
        "high_res_url": "https://example.com/result/high_1.jpg",
        "low_res_url": "https://example.com/result/low_1.jpg"
    },
    "progress": 60,
    "timestamp": "2024-01-15T10:30:00.000Z"
}
```

##### 2. 单个任务失败通知
**频道**: `private-user.{user_id}`
**事件**: `face-swap.task.failed`

**通知数据**:
```json
{
    "type": "face_swap_task_failed",
    "batch_id": "face_12345678-1234-1234-1234-123456789abc",
    "task_id": 789,
    "page_id": 1,
    "variant_id": 10,
    "error_message": "API调用失败: 图片格式不支持",
    "timestamp": "2024-01-15T10:30:00.000Z"
}
```

##### 3. 批次完成通知
**频道**: `private-user.{user_id}`
**事件**: `face-swap.batch.completed`

**通知数据**:
```json
{
    "type": "face_swap_batch_completed",
    "batch_id": "face_12345678-1234-1234-1234-123456789abc",
    "status": "completed",
    "progress": 100,
    "total_tasks": 5,
    "completed_tasks": 5,
    "is_priority": false,
    "results": [
        {
            "task_id": 789,
            "page_id": 1,
            "result_image_url": "https://example.com/result/standard_1.jpg",
            "result": {
                "standard_url": "https://example.com/result/standard_1.jpg",
                "high_res_url": "https://example.com/result/high_1.jpg",
                "low_res_url": "https://example.com/result/low_1.jpg"
            }
        }
    ],
    "timestamp": "2024-01-15T10:30:00.000Z"
}
```

##### 4. 队列状态通知（可选）
**频道**: `private-user.{user_id}`, `face-swap-queue-status`
**事件**: `face-swap.queue-status`

**通知数据**:
```json
{
    "user_id": 123,
    "regular_queue_length": 15,
    "priority_queue_length": 3,
    "total_queue_length": 18,
    "queue_type": "regular",
    "queue_position": 8,
    "estimated_wait_time": 960,
    "timestamp": "2024-01-15T10:20:00.000Z"
}
```

#### 频道授权规则

##### 用户私有频道
```php
// routes/channels.php
Broadcast::channel('user.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});
```

##### AI换脸进度频道（兼容旧版本）
```php
Broadcast::channel('face-swap.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});
```

##### AI换脸批次频道
```php
Broadcast::channel('face-swap-batch.{batchId}', function ($user, $batchId) {
    // 从缓存或数据库验证批次所有权
    $batchInfo = Cache::get('face_swap:batch:auth:' . $batchId);
    if (!$batchInfo) {
        $batchRecord = AiFaceTask::where('batch_id', $batchId)
            ->where('type', 'batch')
            ->first();
        if ($batchRecord) {
            Cache::put('face_swap:batch:auth:' . $batchId, 
                ['user_id' => $batchRecord->user_id], 
                now()->addHours(1)
            );
            return (int) $user->id === (int) $batchRecord->user_id;
        }
        return false;
    }
    return (int) $user->id === (int) $batchInfo['user_id'];
});
```

##### 订单频道
```php
Broadcast::channel('orders.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});
```

##### 公共队列状态频道
```php
Broadcast::channel('face-swap-queue-status', function ($user) {
    return true; // 所有认证用户都可以监听
});
```

##### 管理后台频道
```php
Broadcast::channel('admin-dashboard', function ($user) {
    return $user instanceof \App\Models\Admin;
});
```

### 队列处理时间预估

#### 处理优先级
1. **高优先级队列** (`high_priority_face_swap`): 付费订单，优先处理
2. **普通队列** (`face_swap`): 免费预览，按顺序处理
3. **默认队列** (`default`): 其他后台任务

#### 时间预估算法
- 平均处理时间：120秒/任务
- 预估等待时间 = (队列中待处理任务数 + 正在处理任务数) × 120秒
- 高优先级任务会插队处理

#### 队列状态查询
```
GET /api/simple-face-swap/queue-status
```

响应示例：
```json
{
    "success": true,
    "data": {
        "queue_info": {
            "total_pending": 15,
            "total_processing": 3,
            "high_priority_pending": 2,
            "estimated_wait_time": 2160,
            "estimated_wait_time_formatted": "36分钟"
        },
        "user_tasks": [
            {
                "batch_id": "face_xxx",
                "status": "pending",
                "progress": 0,
                "created_at": "2024-01-15T10:00:00.000000Z",
                "is_priority": false
            }
        ],
        "user_tasks_count": 1
    }
}
```

## 数据库设计

### 核心表结构

#### `picbook_previews` 表
存储用户预览会话信息：
```sql
- id: 主键
- user_id: 用户ID
- picbook_id: 绘本ID
- batch_id: 换脸批次ID
- status: 预览状态 (pending/completed/failed)
- preview_data: 预览页面数据 (JSON)
- characters: 角色序列 (JSON)
- face_swap_batch: 换脸批次信息 (JSON)
- result_images: 结果图片URLs (JSON)
- md5_key: 请求参数哈希值
- language: 语言
- skin_color: 肤色 (JSON数组)
- created_at/updated_at: 时间戳
```

#### `ai_face_tasks` 表
存储 AI 换脸任务详情：
```sql
- id: 主键
- batch_id: 批次ID
- user_id: 用户ID
- type: 任务类型 (batch/task)
- status: 任务状态
- face_image_url: 人脸图片URL
- target_image_url: 目标图片URL
- mask_image: 蒙版图片URL
- result_image_url: 结果图片URL
- page_id: 页面ID
- variant_id: 变体ID
- character_sequence: 角色序列 (JSON)
- is_priority: 是否高优先级
- progress: 进度百分比
- total_tasks: 总任务数
- completed_tasks: 已完成任务数
- config: 任务配置 (JSON)
- result: 处理结果 (JSON)
- error_message: 错误信息
- created_at/updated_at/completed_at: 时间戳
```

## 限制和配置

### 业务限制
- 每用户每天最多50次预览
- 最大批次大小：20个任务
- 最大并发任务：5个
- 单个图片最大10MB

### 超时配置
- API 请求超时：30秒
- 队列任务超时：300秒（5分钟）
- 第三方 API 超时：120秒
- 缓存TTL：3600秒（1小时）

### 重试机制
- 队列任务最多重试3次
- API 调用失败自动重试
- 网络异常指数退避重试

## 错误处理

### 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 参数验证失败 | 检查请求参数格式和值 |
| 404 | 绘本或预览页面不存在 | 确认绘本ID有效且包含预览页 |
| 422 | 业务规则验证失败 | 检查语言、性别、肤色支持情况 |
| 429 | 达到每日预览上限 | 等待次日重置或升级账户 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 错误响应示例

```json
{
    "success": false,
    "error": "预览次数已达每日上限",
    "message": "今日预览次数已用完，请明天再试"
}
```

## 监控和日志

### 关键日志点
1. 请求参数验证
2. 重复请求检测
3. 批次任务创建
4. AI API 调用
5. 任务完成通知
6. 错误异常捕获

### 性能监控
- 队列长度监控
- 任务处理时间统计
- API 成功率监控
- 用户预览使用情况

### Horizon 监控面板
访问 `/horizon` 查看：
- 队列实时状态
- 任务处理统计
- 失败任务详情
- 性能指标图表

## 最佳实践

### 客户端集成

#### JavaScript WebSocket 集成示例

```javascript
// 1. 建立 WebSocket 连接
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

const echo = new Echo({
    broadcaster: 'reverb',
    key: process.env.MIX_REVERB_APP_KEY,
    wsHost: process.env.MIX_REVERB_HOST,
    wsPort: process.env.MIX_REVERB_PORT,
    wssPort: process.env.MIX_REVERB_PORT,
    forceTLS: (process.env.MIX_REVERB_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],
    auth: {
        headers: {
            Authorization: `Bearer ${authToken}`,
        },
    },
});

// 2. 监听用户私有频道（实际使用的事件）
const userId = getCurrentUserId();
echo.private(`user.${userId}`)
    .listen('.face-swap.task.completed', (data) => {
        console.log('任务完成:', data);
        updateTaskProgress(data.batch_id, data.task_id, data.result);
    })
    .listen('.face-swap.task.failed', (data) => {
        console.log('任务失败:', data);
        handleTaskError(data.batch_id, data.task_id, data.error_message);
    })
    .listen('.face-swap.batch.completed', (data) => {
        console.log('批次完成:', data);
        handleBatchCompleted(data.batch_id, data.results);
    });

// 3. 可选：监听队列状态（如果需要显示队列信息）
echo.channel('face-swap-queue-status')
    .listen('.face-swap.queue-status', (data) => {
        console.log('队列状态更新:', data);
        updateQueueStatus(data.total_queue_length, data.estimated_wait_time);
    });

// 3. 处理任务进度更新
function updateTaskProgress(batchId, taskId, result) {
    const progressElement = document.querySelector(`[data-batch-id="${batchId}"]`);
    if (progressElement) {
        // 更新进度条
        const progress = calculateProgress(batchId);
        progressElement.querySelector('.progress-bar').style.width = `${progress}%`;
        
        // 显示完成的图片
        if (result && result.standard_url) {
            displayCompletedImage(taskId, result.standard_url);
        }
    }
}

// 4. 处理批次完成
function handleBatchCompleted(batchId, results) {
    // 隐藏进度条
    hideProgressBar(batchId);
    
    // 显示所有结果
    results.forEach(result => {
        displayFinalResult(result.page_id, result.result);
    });
    
    // 显示完成消息
    showSuccessMessage('您的个性化预览已生成完成！');
}

// 5. 错误处理
function handleTaskError(batchId, taskId, errorMessage) {
    console.error(`任务 ${taskId} 失败:`, errorMessage);
    showErrorMessage(`处理失败: ${errorMessage}`);
}
```

#### React Hook 集成示例

```javascript
import { useEffect, useState } from 'react';
import Echo from 'laravel-echo';

export const useFaceSwapNotifications = (userId, authToken) => {
    const [taskProgress, setTaskProgress] = useState({});
    const [completedTasks, setCompletedTasks] = useState([]);
    const [errors, setErrors] = useState([]);

    useEffect(() => {
        if (!userId || !authToken) return;

        const echo = new Echo({
            // ... Echo 配置
            auth: {
                headers: {
                    Authorization: `Bearer ${authToken}`,
                },
            },
        });

        const channel = echo.private(`user.${userId}`);

        // 监听任务完成
        channel.listen('.face-swap.task.completed', (data) => {
            setTaskProgress(prev => ({
                ...prev,
                [data.batch_id]: {
                    ...prev[data.batch_id],
                    progress: data.progress,
                    completedTasks: (prev[data.batch_id]?.completedTasks || 0) + 1
                }
            }));

            setCompletedTasks(prev => [...prev, data]);
        });

        // 监听任务失败
        channel.listen('.face-swap.task.failed', (data) => {
            setErrors(prev => [...prev, data]);
        });

        // 监听批次完成
        channel.listen('.face-swap.batch.completed', (data) => {
            setTaskProgress(prev => ({
                ...prev,
                [data.batch_id]: {
                    ...prev[data.batch_id],
                    status: 'completed',
                    progress: 100,
                    results: data.results
                }
            }));
        });

        return () => {
            channel.stopListening('.face-swap.task.completed');
            channel.stopListening('.face-swap.task.failed');
            channel.stopListening('.face-swap.batch.completed');
            echo.disconnect();
        };
    }, [userId, authToken]);

    return {
        taskProgress,
        completedTasks,
        errors,
        clearErrors: () => setErrors([])
    };
};
```

#### Vue.js 组合式 API 示例

```javascript
import { ref, onMounted, onUnmounted } from 'vue';
import Echo from 'laravel-echo';

export function useFaceSwapNotifications(userId, authToken) {
    const taskProgress = ref({});
    const completedTasks = ref([]);
    const errors = ref([]);
    let echo = null;

    const setupWebSocket = () => {
        echo = new Echo({
            // ... Echo 配置
        });

        echo.private(`user.${userId}`)
            .listen('.face-swap.task.completed', (data) => {
                // 更新任务进度
                if (!taskProgress.value[data.batch_id]) {
                    taskProgress.value[data.batch_id] = {};
                }
                taskProgress.value[data.batch_id].progress = data.progress;
                completedTasks.value.push(data);
            })
            .listen('.face-swap.task.failed', (data) => {
                errors.value.push(data);
            })
            .listen('.face-swap.batch.completed', (data) => {
                taskProgress.value[data.batch_id] = {
                    ...taskProgress.value[data.batch_id],
                    status: 'completed',
                    results: data.results
                };
            });
    };

    onMounted(() => {
        if (userId && authToken) {
            setupWebSocket();
        }
    });

    onUnmounted(() => {
        if (echo) {
            echo.disconnect();
        }
    });

    return {
        taskProgress,
        completedTasks,
        errors
    };
}
```

#### 通知处理最佳实践

1. **连接管理**
   - 在用户登录后建立连接
   - 在页面卸载时正确断开连接
   - 处理连接断开和重连逻辑

2. **进度显示**
   - 实时更新进度条
   - 显示当前处理的页面
   - 提供预估完成时间

3. **错误处理**
   - 显示友好的错误信息
   - 提供重试机制
   - 记录错误日志用于调试

4. **性能优化**
   - 避免频繁的 DOM 更新
   - 使用防抖处理高频通知
   - 及时清理不需要的监听器

### 性能优化
1. 使用 Redis 缓存频繁查询的数据
2. 图片预处理和压缩
3. CDN 加速图片访问
4. 数据库查询优化

### 安全考虑
1. 用户认证和授权
2. 图片内容安全检查
3. 请求频率限制
4. 敏感数据加密存储

## WebSocket 配置和故障排除

### 环境配置

#### Laravel Reverb 配置
```env
BROADCAST_CONNECTION=reverb
REVERB_APP_ID=dreamazebook
REVERB_APP_KEY=reverb_app_key
REVERB_APP_SECRET=reverb_app_secret
REVERB_HOST=localhost
REVERB_PORT=8080
REVERB_SCHEME=http
```

#### 启动 Reverb 服务器
```bash
# 开发环境
php artisan reverb:start

# 生产环境（使用 Supervisor）
php artisan reverb:start --host=0.0.0.0 --port=8080
```

### 常见问题排除

#### 1. WebSocket 连接失败
**问题**: 客户端无法连接到 WebSocket 服务器
**解决方案**:
- 检查 Reverb 服务器是否正在运行
- 验证防火墙设置是否允许 WebSocket 端口
- 确认客户端配置的主机和端口正确

#### 2. 认证失败
**问题**: WebSocket 频道认证失败
**解决方案**:
- 确认 Sanctum token 有效且未过期
- 检查认证头格式: `Authorization: Bearer {token}`
- 验证用户ID匹配

#### 3. 通知未收到
**问题**: 客户端未收到 WebSocket 通知
**解决方案**:
- 检查事件监听器是否正确注册
- 验证频道名称格式
- 查看服务器日志确认事件是否触发

#### 4. 连接频繁断开
**问题**: WebSocket 连接不稳定
**解决方案**:
- 实现自动重连机制
- 增加心跳检测
- 检查网络环境和代理设置

### 调试工具

#### 1. Laravel 日志
```bash
# 查看 WebSocket 相关日志
tail -f storage/logs/laravel.log | grep -i "broadcast\|websocket\|reverb"
```

#### 2. Reverb 调试模式
```bash
# 启用详细日志
php artisan reverb:start --debug
```

#### 3. 浏览器开发者工具
- Network 标签查看 WebSocket 连接状态
- Console 查看客户端错误信息
- Application 标签检查认证信息

## 相关 API

- `GET /api/simple-face-swap/{batchId}/status` - 查询批次状态
- `GET /api/simple-face-swap/queue-status` - 查询队列状态
- `POST /api/simple-face-swap/preview` - 获取预览页面
- `POST /api/simple-face-swap/full-book` - 创建整本书换脸任务
- `POST /api/broadcasting/auth` - WebSocket 频道认证

## 更新日志

- v1.0.0: 初始版本，支持基础换脸功能
- v1.1.0: 添加多分辨率输出支持
- v1.2.0: 集成实时通知系统
- v1.3.0: 优化队列处理和错误处理
- v1.4.0: 添加重复请求检测和缓存机制