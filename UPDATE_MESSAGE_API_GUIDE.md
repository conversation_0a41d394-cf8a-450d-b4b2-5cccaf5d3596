# UpdateMessage API 使用指南

## 概述

`updateMessage` API 现在只支持单个订单项的寄语更新，不同类型的订单项有不同的处理逻辑：
1. **寄语页面（dedication）**: 只在底图上添加文字，处理快速
2. **换脸页面（picbook）**: 需要重新处理整个图片（包含换脸和寄语）

## API 端点

```
PUT /api/order/update-message/{id}
```

## 请求参数

### 必需参数
- `message` (required, string): 新的寄语内容，最大500字符
- `item_id` (required, integer): 订单项ID，指定要更新的订单项

## 使用示例

### 1. 更新单个订单项的寄语（寄语页面）

```bash
curl -X PUT "http://your-domain/api/order/update-message/123" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "专属定制寄语：愿你的每一天都充满阳光和欢笑！",
    "item_id": 456
  }'
```

**寄语页面响应示例：**
```json
{
  "success": true,
  "message": "订单寄语已更新",
  "data": {
    "order": {
      "id": 123,
      "status": "processing",
      "items": [
        {
          "id": 456,
          "message": "专属定制寄语：愿你的每一天都充满阳光和欢笑！",
          "status": "completed",
          "processing_progress": 100,
          "preview_type": "dedication"
        },
        {
          "id": 457,
          "message": "原来的寄语内容",
          "status": "processing",
          "processing_progress": 50,
          "preview_type": "picbook"
        }
      ]
    }
  }
}
```

### 2. 更新单个订单项的寄语（换脸页面）

```bash
curl -X PUT "http://your-domain/api/order/update-message/123" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "更新后的换脸页面寄语",
    "item_id": 457
  }'
```

**换脸页面响应示例：**
```json
{
  "success": true,
  "message": "订单寄语已更新",
  "data": {
    "order": {
      "id": 123,
      "status": "processing",
      "items": [
        {
          "id": 456,
          "message": "专属定制寄语：愿你的每一天都充满阳光和欢笑！",
          "status": "completed",
          "processing_progress": 100,
          "preview_type": "dedication"
        },
        {
          "id": 457,
          "message": "更新后的换脸页面寄语",
          "status": "processing",
          "processing_progress": 50,
          "preview_type": "picbook"
        }
      ]
    }
  }
}
```

## 业务逻辑说明

### 不同类型订单项的处理逻辑

#### 1. 寄语页面（dedication 类型）
- **特点**: 只在底图上添加文字，不涉及换脸处理
- **处理流程**:
  1. 获取寄语页面底图
  2. 使用TextOverlayService在底图上添加文字
  3. 根据配置处理文字样式（字体、颜色、位置等）
  4. 生成最终图片
- **进度逻辑**: 
  - 开始处理: 设置50%进度
  - 处理完成: 直接设置为100%完成
- **处理速度**: 非常快，因为只涉及文字添加

#### 2. 换脸页面（picbook 类型）
- **特点**: 需要重新处理整个图片（包含换脸和寄语）
- **处理流程**:
  1. 重新进行AI换脸处理
  2. 换脸完成后设置50%进度
  3. 然后进行寄语更新处理
  4. 最终生成完整的图片
- **进度逻辑**: 
  - 开始处理: 设置0%进度
  - 换脸完成: 设置50%进度
  - 寄语更新: 设置75%进度
  - 处理完成: 设置100%完成
- **处理速度**: 较慢，因为需要完整的AI处理流程

### 状态流转图

```
寄语页面更新:
pending → processing(50%) → completed(100%)

换脸页面更新:
pending → processing(0%) → face_swap_processing → processing(50%) → dedication_update → processing(75%) → completed(100%)
```

## 错误处理

### 常见错误代码

| 错误代码 | 说明 |
|---------|------|
| 422 | 参数验证失败（如寄语过长、item_id不存在等） |
| 404 | 订单或订单项不存在 |
| 403 | 权限不足（非订单所有者） |
| 400 | 业务规则不满足（如超过4小时限制、订单状态不正确等） |

### 错误响应示例

```json
{
  "success": false,
  "message": "订单项不存在",
  "data": null
}
```

## 限制条件

1. **时间限制**: 订单创建后4小时内才能更新寄语
2. **状态限制**: 仅在 `pending`、`processing`、`ai_processing` 状态下可更新
3. **权限限制**: 只有订单所有者可以更新寄语
4. **内容限制**: 寄语内容最大500字符

## 日志记录

系统会记录以下信息：
- 订单寄语更新日志
- 订单项寄语更新日志
- 不同类型订单项的处理进度
- 错误和异常信息

### 日志示例

```json
{
  "order_id": 123,
  "order_item_id": 456,
  "user_id": 789,
  "new_message": "更新后的寄语内容",
  "item_type": "dedication",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 前端集成建议

### 1. 单个订单项更新组件

```javascript
async function updateOrderItemMessage(orderId, itemId, message) {
  try {
    const response = await fetch(`/api/order/update-message/${orderId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        message: message,
        item_id: itemId
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // 更新UI显示
      updateItemDisplay(itemId, message);
      // 显示成功消息
      showSuccessMessage('寄语更新成功');
    } else {
      // 显示错误消息
      showErrorMessage(result.message);
    }
  } catch (error) {
    console.error('更新寄语失败:', error);
    showErrorMessage('网络错误，请重试');
  }
}
```

### 2. 进度显示组件

```javascript
function showItemProgress(item) {
  const progressPercentage = item.processing_progress;
  const statusText = getItemStatusText(item.status);
  
  return `
    <div class="progress-item">
      <div class="progress-info">
        <span>订单项 #${item.id}</span>
        <span>${statusText}</span>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" style="width: ${progressPercentage}%"></div>
      </div>
      <div class="progress-text">${progressPercentage}%</div>
    </div>
  `;
}
```

## 性能优化建议

1. **批量更新**: 如果需要更新多个订单项，建议使用多次API调用而不是批量操作
2. **缓存策略**: 可以缓存订单项的状态信息，减少重复请求
3. **错误重试**: 实现适当的错误重试机制，特别是网络错误时
4. **进度轮询**: 对于长时间处理的任务，实现进度轮询来实时更新UI

## 监控指标

建议监控以下指标：
- 寄语更新成功率
- 不同类型订单项的处理时间
- 错误率和常见错误类型
- 用户操作频率和峰值时段