<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('preview_id')->nullable()->constrained('picbook_previews')->nullOnDelete();
            $table->foreignId('picbook_id')->constrained()->onDelete('cascade');
            $table->integer('quantity')->default(1)->comment('数量');
            $table->decimal('price', 10, 2)->default(0)->comment('基础价格');
            $table->decimal('cover_price', 10, 2)->default(0)->comment('封面价格');
            $table->decimal('binding_price', 10, 2)->default(0)->comment('装帧价格');
            $table->decimal('gift_box_price', 10, 2)->default(0)->comment('礼盒价格');
            $table->decimal('total_price', 10, 2)->default(0)->comment('总价');
            $table->string('recipient_name')->nullable()->comment('赠与人姓名');
            $table->text('message')->nullable()->comment('赠言/寄语');
            $table->string('cover_type')->default('default')->comment('封面类型');
            $table->string('binding_type')->default('standard')->comment('装帧方式');
            $table->string('gift_box')->default('standard')->comment('礼盒类型');
            $table->string('face_image')->nullable()->comment('用户上传的人脸图片');
            $table->json('result_images')->nullable()->comment('换脸后的图片列表');
            $table->string('status')->default('pending')->comment('状态');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index(['order_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
}; 