<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 先删除旧表（如果存在）
        Schema::dropIfExists('ai_face_tasks');

        // 重新创建带有所有必要字段的表
        Schema::create('ai_face_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('task_id')->nullable(); // AI服务返回的任务ID
            $table->string('status')->default('pending'); // pending, processing, completed, failed
            $table->string('input_image');
            $table->string('mask_image')->nullable();
            $table->string('face_image');
            $table->string('result_image')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->json('character_sequence')->nullable();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_face_tasks');
    }
};
