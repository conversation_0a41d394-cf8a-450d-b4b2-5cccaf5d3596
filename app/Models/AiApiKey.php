<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Exception;
use Illuminate\Support\Facades\Cache;

class AiApiKey extends Model
{
    protected $fillable = ['api_key', 'current_tasks', 'is_active'];

    /**
     * 获取可用的API密钥
     * 
     * @return \App\Models\AiApiKey
     * @throws \Exception 如果没有可用的API密钥
     */
    public static function getAvailableKey()
    {
        $key = static::where('is_active', true)
            ->orderBy('current_tasks')
            ->first();
            
        if (!$key) {
            throw new Exception('没有可用的AI换脸API密钥');
        }
        
        return $key;
    }
    
    /**
     * 获取并尝试锁定API密钥
     * 适用于多队列环境下的并发控制
     * 
     * @param int $lockSeconds 锁定秒数
     * @return \App\Models\AiApiKey|null 如果成功锁定则返回密钥，否则返回null
     * @throws \Exception 如果没有可用的API密钥
     */
    public static function getAndLockAvailableKey($lockSeconds = 30)
    {
        // 获取所有活跃的API密钥，按当前任务数排序
        $keys = static::where('is_active', true)
            ->orderBy('current_tasks')
            ->get();
            
        if ($keys->isEmpty()) {
            throw new Exception('没有可用的AI换脸API密钥');
        }
        
        // 尝试锁定任务数最少的密钥
        foreach ($keys as $key) {
            $lockKey = 'api_key_lock:' . $key->id;
            $lock = Cache::lock($lockKey, $lockSeconds);
            
            if ($lock->get()) {
                // 成功获取锁
                return $key;
            }
        }
        
        // 所有密钥都被锁定，返回null表示需要稍后重试
        return null;
    }
    
    /**
     * 释放API密钥锁
     * 
     * @return bool 是否成功释放锁
     */
    public function releaseLock()
    {
        $lockKey = 'api_key_lock:' . $this->id;
        return Cache::lock($lockKey, 30)->release();
    }
    
    /**
     * 获取API密钥当前并发数
     * 
     * @return int 当前并发数
     */
    public function getConcurrentCount()
    {
        $concurrentKey = 'api_key_concurrent:' . $this->id;
        return (int)Cache::get($concurrentKey, 0);
    }
    
    /**
     * 增加API密钥并发数
     * 
     * @param int $maxConcurrent 最大并发数
     * @return bool 是否成功增加
     */
    public function incrementConcurrent($maxConcurrent = 5)
    {
        $concurrentKey = 'api_key_concurrent:' . $this->id;
        
        // 原子操作增加并发计数
        $current = Cache::increment($concurrentKey, 1);
        
        // 如果超过最大并发，减回去并返回失败
        if ($current > $maxConcurrent) {
            Cache::decrement($concurrentKey, 1);
            return false;
        }
        
        return true;
    }
    
    /**
     * 减少API密钥并发数
     * 
     * @return void
     */
    public function decrementConcurrent()
    {
        $concurrentKey = 'api_key_concurrent:' . $this->id;
        $current = (int)Cache::get($concurrentKey, 0);
        
        if ($current > 0) {
            Cache::decrement($concurrentKey, 1);
        }
    }
    
    /**
     * 增加当前任务数
     * 
     * @return void
     */
    public function incrementTasks()
    {
        $this->increment('current_tasks');
    }
    
    /**
     * 减少当前任务数
     * 
     * @return void
     */
    public function decrementTasks()
    {
        if ($this->current_tasks > 0) {
            $this->decrement('current_tasks');
        }
    }
    
    /**
     * 重置当前任务数
     * 
     * @return void
     */
    public function resetTasks()
    {
        $this->update(['current_tasks' => 0]);
    }
    
    /**
     * 重置并发计数
     * 
     * @return void
     */
    public function resetConcurrent()
    {
        $concurrentKey = 'api_key_concurrent:' . $this->id;
        Cache::forget($concurrentKey);
    }
    
    /**
     * 针对VIP用户获取API密钥
     * 这个方法会优先考虑任务负载较轻的密钥
     * 
     * @return \App\Models\AiApiKey
     * @throws \Exception 如果没有可用的API密钥
     */
    public static function getVipKey()
    {
        $key = static::where('is_active', true)
            ->where('current_tasks', '<', 10) // VIP用户使用负载较轻的密钥
            ->orderBy('current_tasks')
            ->first();
            
        if (!$key) {
            // 如果没有满足条件的密钥，退回到普通逻辑
            return static::getAvailableKey();
        }
        
        return $key;
    }
} 