<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Models\User;
use App\Services\MailService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class UserAuthController extends Controller
{
    use ApiResponse;

    /**
     * @var MailService
     */
    protected $mailService;

    /**
     * UserAuthController constructor.
     *
     * @param MailService $mailService
     */
    public function __construct(MailService $mailService)
    {
        $this->mailService = $mailService;
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        // 发送欢迎邮件
        try {
            $this->mailService->sendWelcomeEmail($user);
        } catch (\Exception $e) {
            // 记录邮件发送失败，但不影响用户注册
            \Log::error('Failed to send welcome email: ' . $e->getMessage());
        }

        $token = $user->createToken('auth_token')->plainTextToken;

        return $this->success([
            'user' => $user,
            'token' => $token
        ], __('auth.register_success'), 201);
    }

    public function login(LoginRequest $request): JsonResponse
    {
        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return $this->error(
                __('auth.failed'),
                ['email' => [__('auth.failed')]],
                401
            );
        }

        $token = $user->createToken('auth_token')->plainTextToken;

        return $this->success([
            'user' => $user,
            'token' => $token
        ], __('auth.login_success'));
    }

    public function logout(): JsonResponse
    {
        $user = auth()->user();
        
        if ($user && $user->currentAccessToken()) {
            $user->currentAccessToken()->delete();
        }

        return $this->success(null, __('auth.logout_success'));
    }

    /**
     * 获取当前用户信息
     */
    public function me(): JsonResponse
    {
        $user = auth()->user();
        
        if (!$user) {
            return $this->error(__('auth.unauthenticated'), null, 401);
        }
        
        return $this->success($user, __('auth.user_info_success'));
    }

    /**
     * 修改个人信息
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'avatar' => 'string|url'
        ]);

        if ($validator->fails()) {
            return $this->error(
                __('validation.failed'),
                $validator->errors(),
                422
            );
        }

        $user = auth()->user();
        $user->update($request->only(['name', 'avatar']));

        return $this->success($user, __('auth.profile_update_success'));
    }

     /**
     * 发送重置密码邮件
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendResetLink(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return $this->error('Invalid email format', 422, $validator->errors());
        }

        $token = $this->mailService->sendResetPasswordEmail($request->email);

        if (!$token) {
            return $this->error('Email not found', 404);
        }

        return $this->success([
            'message' => 'Password reset link has been sent to your email',
            'token' => $token
        ]);
    }

    /**
     * 修改密码
     */
    public function updatePassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return $this->error(
                __('validation.failed'),
                $validator->errors(),
                422
            );
        }

        $user = auth()->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return $this->error(__('auth.password_incorrect'));
        }

        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return $this->success(null, __('auth.password_update_success'));
    }

    /**
     * 测试邮件发送功能
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function testEmail(Request $request): JsonResponse
    {
        try {
            // 创建一个临时用户对象用于测试
            $testUser = new User([
                'name' => 'Test User',
                'email' => '<EMAIL>'
            ]);

            $this->mailService->sendWelcomeEmail($testUser);
            
            return $this->success([], '测试邮件已发送，请检查收件箱');
        } catch (\Exception $e) {
            Log::error('Test email sending failed: ' . $e->getMessage());
            return $this->error('邮件发送失败：' . $e->getMessage(), 500);
        }
    }
}