#!/bin/bash

# Reverb WebSocket 监控脚本
set -e

echo "📡 开始监控 Reverb WebSocket 状态..."

# 检查 Reverb 连接状态
echo "🔗 Reverb 连接测试："
php artisan reverb:ping

echo ""
echo "📊 Reverb 统计信息："
php artisan tinker --execute="
try {
    echo 'Reverb 配置检查:' . PHP_EOL;
    echo 'Host: ' . config('reverb.servers.reverb.host') . PHP_EOL;
    echo 'Port: ' . config('reverb.servers.reverb.port') . PHP_EOL;
    echo 'App ID: ' . config('reverb.apps.apps.0.app_id') . PHP_EOL;
} catch (Exception \$e) {
    echo 'Reverb 配置检查失败: ' . \$e->getMessage() . PHP_EOL;
}
"

echo ""
echo "📡 Reverb 监控完成！"