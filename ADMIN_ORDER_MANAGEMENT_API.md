# 后台订单管理 API 文档

## 概述

完善的后台订单管理系统，提供订单列表、详情查看、状态管理、批量操作、统计分析和数据导出等功能。

## API 端点

### 1. 获取订单列表

```http
GET /api/admin/orders
```

**查询参数:**
- `page` (int): 页码，默认 1
- `per_page` (int): 每页数量，默认 15
- `status` (string): 订单状态筛选
- `payment_status` (string): 支付状态筛选
- `processing_status` (string): 处理状态筛选
- `shipping_status` (string): 物流状态筛选
- `user_search` (string): 用户搜索（姓名或邮箱）
- `order_number` (string): 订单号搜索
- `date_from` (date): 开始日期
- `date_to` (date): 结束日期
- `amount_from` (decimal): 最小金额
- `amount_to` (decimal): 最大金额
- `sort_by` (string): 排序字段，默认 created_at
- `sort_order` (string): 排序方向，默认 desc

**响应示例:**
```json
{
  "success": true,
  "message": "订单列表获取成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "order_number": "ORD-20240816-001",
        "user_id": 1,
        "status": "confirmed",
        "payment_status": "paid",
        "processing_status": "pending",
        "shipping_status": "pending",
        "total_amount": "29.99",
        "created_at": "2024-08-16T00:00:00Z",
        "paid_at": "2024-08-16T00:05:00Z",
        "user": {
          "id": 1,
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "items": [
          {
            "id": 1,
            "picbook_id": 1,
            "quantity": 1,
            "price": "29.99",
            "picbook": {
              "id": 1,
              "title": "My Adventure Book"
            }
          }
        ]
      }
    ],
    "first_page_url": "http://localhost/api/admin/orders?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "http://localhost/api/admin/orders?page=5",
    "next_page_url": "http://localhost/api/admin/orders?page=2",
    "path": "http://localhost/api/admin/orders",
    "per_page": 15,
    "prev_page_url": null,
    "to": 15,
    "total": 67
  }
}
```

### 2. 获取订单详情

```http
GET /api/admin/orders/{id}
```

**响应示例:**
```json
{
  "success": true,
  "message": "订单详情获取成功",
  "data": {
    "id": 1,
    "order_number": "ORD-20240816-001",
    "user_id": 1,
    "status": "confirmed",
    "payment_status": "paid",
    "processing_status": "pending",
    "shipping_status": "pending",
    "total_amount": "29.99",
    "created_at": "2024-08-16T00:00:00Z",
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "items": [...],
    "shippingAddress": {
      "id": 1,
      "name": "John Doe",
      "address": "123 Main St",
      "city": "New York",
      "country": "US"
    },
    "paymentRecords": [...],
    "processingLogs": [...],
    "shippingRecords": [...]
  }
}
```

### 3. 更新订单状态

```http
PUT /api/admin/orders/{id}/status
```

**请求体:**
```json
{
  "status": "processing",
  "note": "开始处理订单"
}
```

**状态值:**
- `pending`: 待确认
- `confirmed`: 已确认
- `processing`: 处理中
- `shipped`: 已发货
- `delivered`: 已送达
- `cancelled`: 已取消
- `refunded`: 已退款

### 4. 更新支付状态

```http
PUT /api/admin/orders/{id}/payment-status
```

**请求体:**
```json
{
  "payment_status": "paid",
  "note": "支付确认"
}
```

**支付状态值:**
- `pending`: 待支付
- `paid`: 已支付
- `failed`: 支付失败
- `refunded`: 已退款
- `partial_refund`: 部分退款

### 5. 更新处理状态

```http
PUT /api/admin/orders/{id}/processing-status
```

**请求体:**
```json
{
  "processing_status": "completed",
  "note": "处理完成"
}
```

**处理状态值:**
- `pending`: 待处理
- `processing`: 处理中
- `completed`: 已完成
- `failed`: 处理失败

### 6. 手动处理订单

```http
POST /api/admin/orders/{id}/process
```

**请求体:**
```json
{
  "dedication_text": "给宝贝的专属绘本"
}
```

**功能说明:**
- 触发订单的图片处理流程
- 调用 `EnhancedPicbookProcessor` 服务
- 自动更新处理状态

### 7. 批量更新订单状态

```http
POST /api/admin/orders/batch-update-status
```

**请求体:**
```json
{
  "order_ids": [1, 2, 3, 4, 5],
  "status": "confirmed",
  "note": "批量确认订单"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "批量更新完成，成功: 4，失败: 1",
  "data": {
    "success_count": 4,
    "fail_count": 1,
    "results": [
      {
        "order_id": 1,
        "success": true,
        "message": "更新成功"
      },
      {
        "order_id": 2,
        "success": false,
        "message": "无效的状态变更"
      }
    ]
  }
}
```

### 8. 获取订单统计

```http
GET /api/admin/orders/statistics/overview
```

**查询参数:**
- `date_from` (date): 统计开始日期
- `date_to` (date): 统计结束日期

**响应示例:**
```json
{
  "success": true,
  "message": "统计数据获取成功",
  "data": {
    "status_stats": {
      "pending": 15,
      "confirmed": 32,
      "processing": 8,
      "shipped": 12,
      "delivered": 45,
      "cancelled": 3,
      "refunded": 1
    },
    "payment_stats": {
      "pending": 18,
      "paid": 95,
      "failed": 2,
      "refunded": 1
    },
    "processing_stats": {
      "pending": 25,
      "processing": 8,
      "completed": 80,
      "failed": 3
    },
    "revenue_stats": {
      "total_revenue": "2847.65",
      "pending_revenue": "539.82"
    },
    "daily_stats": [
      {
        "date": "2024-08-15",
        "orders": 12,
        "revenue": "358.88"
      }
    ],
    "popular_items": [
      {
        "picbook_id": 1,
        "order_count": 25,
        "total_quantity": 28,
        "picbook": {
          "id": 1,
          "title": "Adventure Book"
        }
      }
    ]
  }
}
```

### 9. 导出订单数据

```http
POST /api/admin/orders/export
```

**请求体:**
```json
{
  "format": "csv",
  "date_from": "2024-08-01",
  "date_to": "2024-08-16",
  "status": "delivered",
  "payment_status": "paid"
}
```

**参数说明:**
- `format`: 导出格式 (csv, excel)
- 其他参数与列表查询相同

**响应:**
- 返回文件下载流
- 文件名格式: `orders_export_YYYY-MM-DD_HH-mm-ss.csv`

## 状态转换规则

### 订单状态转换
```
pending → confirmed, cancelled
confirmed → processing, cancelled  
processing → shipped, cancelled
shipped → delivered
delivered → refunded
cancelled → (无法转换)
refunded → (无法转换)
```

### 自动状态更新
- 支付成功 → 订单状态自动从 `pending` 变为 `confirmed`
- 处理完成 → 订单状态自动从 `confirmed` 变为 `processing`

## 日志记录

系统会自动记录以下操作日志：
- 订单状态变更
- 支付状态变更  
- 处理状态变更
- 物流状态变更
- 管理员备注

日志包含：
- 操作类型
- 变更前后的值
- 操作备注
- 操作管理员
- 操作时间

## 权限要求

所有 API 端点都需要：
- 用户已登录 (`auth:sanctum`)
- 用户类型为管理员 (`check.user.type:admin`)

## 错误处理

### 常见错误码
- `400`: 无效的状态转换
- `404`: 订单不存在
- `422`: 参数验证失败
- `500`: 服务器内部错误

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "errors": {
    "field": ["具体错误信息"]
  }
}
```

## 使用示例

### JavaScript 示例

```javascript
// 获取订单列表
const getOrders = async (page = 1, filters = {}) => {
  const params = new URLSearchParams({
    page,
    per_page: 15,
    ...filters
  });
  
  const response = await fetch(`/api/admin/orders?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json'
    }
  });
  
  return response.json();
};

// 更新订单状态
const updateOrderStatus = async (orderId, status, note = '') => {
  const response = await fetch(`/api/admin/orders/${orderId}/status`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify({ status, note })
  });
  
  return response.json();
};

// 批量更新状态
const batchUpdateStatus = async (orderIds, status, note = '') => {
  const response = await fetch('/api/admin/orders/batch-update-status', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    body: JSON.stringify({
      order_ids: orderIds,
      status,
      note
    })
  });
  
  return response.json();
};
```

## 前端页面

已提供完整的订单管理前端页面 (`resources/views/admin/orders/index.html`)，包含：

### 功能特性
- 📊 实时统计面板
- 🔍 多条件筛选搜索
- 📋 订单列表展示
- 👁️ 订单详情查看
- ✏️ 状态批量管理
- 📤 数据导出功能
- 🔄 实时状态更新

### 界面组件
- 统计卡片显示
- 高级筛选表单
- 响应式数据表格
- 模态框详情编辑
- 批量操作工具栏
- 分页导航组件

### 交互功能
- 全选/批量选择
- 状态快速更新
- 实时搜索筛选
- 订单详情编辑
- 一键处理订单
- CSV/Excel 导出

## 总结

这个订单管理系统提供了：

✅ **完整的 CRUD 操作**
✅ **灵活的筛选和搜索**
✅ **批量操作支持**
✅ **详细的统计分析**
✅ **完善的日志记录**
✅ **数据导出功能**
✅ **响应式前端界面**
✅ **状态转换验证**
✅ **权限控制**
✅ **错误处理**

可以满足后台管理员对订单的全方位管理需求。