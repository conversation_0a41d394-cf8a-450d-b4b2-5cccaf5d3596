<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\EnhancedPicbookProcessor;

echo "=== 升级后的 EnhancedPicbookProcessor 测试 ===\n\n";

try {
    $processor = new EnhancedPicbookProcessor();
    
    // 模拟绘本处理
    $picbookPath = "picbooks/test";
    $pageConfig = [
        'skinToneFilter' => [
            'brown' => ['saturate' => '+2', 'hue' => '-4', 'brightness' => '-31'],
            'dark' => ['brightness' => '-80', 'contrast' => '-48']
        ],
        'hairColorFilter' => [
            'brown' => ['saturate' => '+2', 'hue' => '-4', 'brightness' => '-31'],
            'dark' => ['brightness' => '-80', 'contrast' => '-48']
        ]
    ];
    
    $userOptions = [
        'skin_tone' => 'brown',
        'hair_style' => '1',
        'hair_color' => 'brown'
    ];
    
    echo "测试参数:\n";
    echo "皮肤色调: {$userOptions['skin_tone']}\n";
    echo "发型: {$userOptions['hair_style']}\n";
    echo "发色: {$userOptions['hair_color']}\n\n";
    
    $result = $processor->processPage($picbookPath, $pageConfig, $userOptions);
    
    echo "处理结果:\n";
    echo "成功: " . ($result['success'] ? '是' : '否') . "\n";
    echo "消息: " . $result['message'] . "\n";
    if ($result['success']) {
        echo "输出路径: " . $result['image_path'] . "\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈: " . $e->getTraceAsString() . "\n";
}