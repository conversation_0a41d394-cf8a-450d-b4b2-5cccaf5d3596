<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // 配置S3存储选择逻辑
        Storage::macro('picbook', function ($driver = null) {
            return Storage::disk('s3_picbook');
        });
        
        Storage::macro('faceswap', function ($driver = null) {
            return Storage::disk('s3_faceswap');
        });
        
        // 重写默认的S3实现，使其能够智能地选择正确的S3存储桶
        Storage::macro('smartS3', function ($path) {
            // 根据路径前缀决定使用哪个S3存储桶
            if (str_starts_with($path, 'faceswap/') || str_starts_with($path, 'ai-faces/')) {
                return Storage::disk('s3_faceswap');
            } else {
                return Storage::disk('s3_picbook');
            }
        });
        
        // 助手函数已移动到 app/helpers.php 文件中
    }
}
