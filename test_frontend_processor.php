<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\FrontendConsistentProcessor;

echo "=== FrontendConsistentProcessor 测试 ===\n\n";

$processor = new FrontendConsistentProcessor();

// 测试不同的滤镜组合
$testCases = [
    'original' => [],
    'brightness_only' => ['brightness' => '-31'],
    'saturate_only' => ['saturate' => '+2'],
    'hue_only' => ['hue' => '-4'],
    'contrast_only' => ['contrast' => '-30'],
    'brown_filter' => ['saturate' => '+2', 'hue' => '-4', 'brightness' => '-31'],
    'dark_filter' => ['brightness' => '-80', 'contrast' => '-48'],
    'all_filters' => ['saturate' => '+2', 'hue' => '-4', 'brightness' => '-31', 'contrast' => '-30']
];

$testImage = __DIR__ . '/public/picbooks/test/layer_skin.png';
$outputDir = __DIR__ . '/public/picbooks/test/frontend_consistent_test';

if (!is_dir($outputDir)) {
    mkdir($outputDir, 0755, true);
}

foreach ($testCases as $caseName => $filters) {
    echo "测试 {$caseName}...\n";
    if (!empty($filters)) {
        echo "  滤镜: " . json_encode($filters) . "\n";
    }
    
    $result = $processor->processTestImage(
        $testImage,
        $outputDir . '/' . $caseName . '.png',
        $filters
    );
    
    echo "  结果: " . ($result['success'] ? '✓ 成功' : '✗ 失败') . "\n\n";
}

echo "=== 测试完成 ===\n";
echo "输出目录: {$outputDir}\n";
echo "请查看生成的PNG文件，确认滤镜效果是否符合预期。\n";