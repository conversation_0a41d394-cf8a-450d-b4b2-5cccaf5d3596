<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Services\OrderService;
use App\Services\CartService;
use App\Services\ExpService;
use App\Services\StripePaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Traits\ApiResponse;

class OrderController extends Controller
{
    use ApiResponse;

    protected $orderService;
    protected $cartService;
    protected $expService;
    protected $stripeService;

    public function __construct(
        OrderService $orderService,
        CartService $cartService,
        ExpService $expService,
        StripePaymentService $stripeService
    ) {
        $this->orderService = $orderService;
        $this->cartService = $cartService;
        $this->expService = $expService;
        $this->stripeService = $stripeService;
    }

    /**
     * 创建订单
     */
    public function create(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                // 地址相关验证 - 支持地址ID或直接传入地址信息（创建时可选）
                'shipping_address_id' => 'nullable|integer|exists:user_addresses,id',
                'shipping_address' => 'nullable|array',
                'shipping_address.first_name' => 'required_with:shipping_address|string|max:32',
                'shipping_address.phone' => 'required_with:shipping_address|string|max:32',
                'shipping_address.country' => 'required_with:shipping_address|string|max:10',
                'shipping_address.city' => 'required_with:shipping_address|string|max:64',
                'shipping_address.street' => 'required_with:shipping_address|string|max:64',

                'billing_address_id' => 'nullable|integer|exists:user_addresses,id',
                'billing_address' => 'nullable|array',
                'use_shipping_as_billing' => 'nullable|boolean',

                // 订单基本信息
                'cart_item_ids' => 'required|array',
                'cart_item_ids.*' => 'required|integer|exists:cart_items,id',
                'shipping_method' => 'nullable|string', // 改为可选，允许后期设置
                'shipping_cost' => 'nullable|numeric|min:0', // 可选，后端会重新计算并校验
                'payment_method' => 'required|string|in:stripe',
                'coupon_code' => 'nullable|string',
                'notes' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            // 地址验证改为可选（创建订单时不强制要求地址）
            // 如果提供了地址信息，则进行验证
            if ($request->shipping_address) {
                $this->orderService->validateAddressData($request->shipping_address);
            }
            if ($request->billing_address) {
                $this->orderService->validateAddressData($request->billing_address);
            }

            // 获取用户ID
            $userId = Auth::id();

            // 验证选中的购物车项
            $cartItemIds = $request->cart_item_ids;
            $validation = $this->cartService->validateCartItems($userId, $cartItemIds);
            if (!$validation['valid']) {
                return $this->error(__('order.invalid_cart_items'), $validation['invalid_items']);
            }

            // 地址验证已在上面处理

            // 使用数据库事务确保订单创建和支付创建的原子性
            $result = DB::transaction(function () use ($userId, $cartItemIds, $request) {
                // 创建订单（使用安全版本，不会删除购物车数据）
                $order = $this->orderService->createOrderFromCartItemsSafe($userId, $cartItemIds, $request->all());

                // 创建PaymentIntent - 如果失败会抛出异常，触发事务回滚
                $paymentData = $this->createPaymentIntent($order);

                // 如果支付成功，启动订单处理
                if ($order->payment_status === Order::PAYMENT_PAID) {
                    $this->orderService->startOrderProcessing($order);
                }

                // 只有在事务即将成功提交时才删除购物车项
                foreach ($cartItemIds as $cartItemId) {
                    $this->cartService->removeCartItem($userId, $cartItemId);
                }

                return [
                    'order' => $order,
                    'payment_data' => $paymentData
                ];
            });

            return $this->success([
                'order' => $this->formatOrderResponse($result['order']),
                'payment_data' => $result['payment_data']
            ], __('order.created'));
        } catch (\Exception $e) {
            // 数据库事务会自动回滚，包括：
            // 1. 订单创建回滚
            // 2. 订单项创建回滚
            // 3. 购物车数据删除回滚
            // 4. PaymentIntent创建失败不会影响数据库状态
            Log::error('订单创建失败', [
                'user_id' => $userId ?? null,
                'cart_item_ids' => $cartItemIds ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取订单列表
     */
    public function list(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'nullable|string',
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            // 获取用户ID
            $userId = Auth::id();

            // 获取订单列表
            $orders = $this->orderService->getUserOrders(
                $userId,
                $request->status,
                $request->page ?? 1,
                $request->per_page ?? 10
            );

            // 格式化订单列表响应数据
            $formattedItems = collect($orders->items())->map(function ($order) {
                return $this->formatOrderResponse($order);
            });

            // 构建响应数据，包含格式化的订单和分页信息
            $responseData = [
                'data' => $formattedItems,
                'meta' => [
                    'total' => $orders->total(),
                    'per_page' => $orders->perPage(),
                    'current_page' => $orders->currentPage(),
                    'last_page' => $orders->lastPage(),
                    'from' => $orders->firstItem(),
                    'to' => $orders->lastItem()
                ]
            ];

            return response()->json([
                'success' => true,
                'code' => 200,
                'message' => __('order.list_success'),
                'data' => $formattedItems,
                'meta' => $responseData['meta']
            ]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取订单详情
     */
    public function detail($id)
    {
        try {
            // 获取用户ID
            $userId = Auth::id();

            // 获取订单详情
            $order = $this->orderService->getOrderDetail($id, $userId);

            // 格式化订单响应数据
            $formattedOrder = $this->formatOrderResponse($order);

            // 为未支付订单添加PaymentIntent支持
            $responseData = ['order' => $formattedOrder];

            if ($this->shouldIncludePaymentIntent($order)) {
                try {
                    $paymentData = $this->getOrCreatePaymentIntent($order);
                    $responseData['payment_data'] = $paymentData;
                } catch (\Exception $e) {
                    Log::warning('获取PaymentIntent失败', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage()
                    ]);
                    // 不影响订单详情的返回，只是没有支付数据
                }
            }

            return $this->success($responseData, __('order.detail_success'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 取消订单
     */
    public function cancel(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'reason' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            // 获取用户ID
            $userId = Auth::id();

            // 取消订单
            $order = $this->orderService->cancelOrder($id, $userId, $request->reason);

            return $this->success($this->formatOrderResponse($order), __('order.cancelled'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 支付回调
     */
    public function paymentCallback(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|exists:orders,id',
                'payment_id' => 'required|string',
                'payment_status' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            // 验证支付状态
            $paymentStatus = $request->payment_status;
            if (!in_array($paymentStatus, [
                Order::PAYMENT_PAID,
                Order::PAYMENT_FAILED,
                Order::PAYMENT_REFUNDED,
                Order::PAYMENT_PARTIALLY_REFUNDED
            ])) {
                return $this->error(__('order.invalid_status'));
            }

            // 更新支付状态
            $order = $this->orderService->updatePaymentStatus(
                $request->order_id,
                $paymentStatus,
                $request->payment_id
            );

            return $this->success($this->formatOrderResponse($order), __('order.updated'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 更新订单地址
     */
    public function updateAddress(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'shipping_address_id' => 'nullable|integer|exists:user_addresses,id',
                'shipping_address' => 'nullable|array',
                'shipping_address.first_name' => 'required_with:shipping_address|string|max:32',
                'shipping_address.phone' => 'required_with:shipping_address|string|max:32',
                'shipping_address.country' => 'required_with:shipping_address|string|max:10',
                'shipping_address.city' => 'required_with:shipping_address|string|max:64',
                'shipping_address.street' => 'required_with:shipping_address|string|max:64',

                'billing_address_id' => 'nullable|integer|exists:user_addresses,id',
                'billing_address' => 'nullable|array',
                'use_shipping_as_billing' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            // 获取用户ID
            $userId = Auth::id();

            // 验证直接传入的地址信息
            if ($request->shipping_address) {
                $this->orderService->validateAddressData($request->shipping_address);
            }
            if ($request->billing_address) {
                $this->orderService->validateAddressData($request->billing_address);
            }

            // 更新订单地址（返回订单和物流选项）
            $result = $this->orderService->updateOrderAddress($id, $userId, $request->all());
            
            $responseData = [
                'order' => $this->formatOrderResponse($result['order']),
            ];
            if ($this->shouldIncludePaymentIntent($result['order'])) {
                try {
                    $paymentData = $this->getOrCreatePaymentIntent($result['order']);
                    $responseData['payment_data'] = $paymentData;
                } catch (\Exception $e) {
                    Log::warning('获取PaymentIntent失败', [
                        'order_id' => $result['order']->id,
                        'error' => $e->getMessage()
                    ]);
                    // 不影响订单详情的返回，只是没有支付数据
                }
            }

            return $this->success($responseData, __('order.address_updated'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取用户可用地址列表
     */
    public function getAvailableAddresses()
    {
        try {
            // 获取用户ID
            $userId = Auth::id();

            // 获取可用地址列表
            $addresses = $this->orderService->getAvailableAddresses($userId);

            return $this->success($addresses, __('order.success'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取可用的物流方式
     */
    public function getShippingMethods()
    {
        // 返回默认物流选项（基于实际绘本尺寸调整费用）
        $data =  [
            [
                'code' => 'STANDARD',
                'name' => __('order.shipping_methods.standard.name'),
                'description' => __('order.shipping_methods.standard.description'),
                'cost' => 22.00,  // 调整为更大更重包裹的费用
                'currency' => 'USD',
                'estimated_days' => '10-20',
                'tracking_available' => true,
                'recommended' => true
            ],
            [
                'code' => 'EXPRESS',
                'name' => __('order.shipping_methods.express.name'),
                'description' => __('order.shipping_methods.express.description'),
                'cost' => 35.00,  // 调整为更大更重包裹的费用
                'currency' => 'USD',
                'estimated_days' => '5-10',
                'tracking_available' => true,
                'recommended' => false
            ]
        ];
        return $this->success($data, __('order.shipping_methods_success'));
    }

    /**
     * 计算物流费用（已弃用，请使用 /api/shipping/prices）
     * 
     * @deprecated 请使用新的物流价格API: POST /api/shipping/prices
     */
    public function calculateShippingCost()
    {
        // 重定向到新的物流价格API
        return response()->json([
            'success' => false,
            'code' => 410,
            'message' => '此API已弃用，请使用新的物流价格API',
            'data' => [
                'deprecated_endpoint' => '/api/order/calculate-shipping',
                'new_endpoint' => '/api/shipping/prices',
                'migration_guide' => '请使用POST /api/shipping/prices获取多个物流选项的价格'
            ]
        ], 410);
    }

    /**
     * 更新订单项寄语（只支持单个订单项更新）
     */
    public function updateMessage(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'message' => 'required|string|max:500',
                'item_id' => 'required|integer|exists:order_items,id'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            $userId = Auth::id();
            $itemId = $request->input('item_id');
            
            // 只更新单个订单项
            $order = $this->orderService->updateOrderItemMessage($id, $itemId, $userId, $request->message);

            return $this->success($this->formatOrderResponse($order), __('order.message_updated'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }



    /**
     * 确认订单
     */
    public function confirm($id)
    {
        try {
            $userId = Auth::id();
            $order = $this->orderService->confirmOrder($id, $userId);

            return $this->success($this->formatOrderResponse($order), __('order.confirmed'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 重新支付订单
     */
    public function repay($id)
    {
        try {
            $userId = Auth::id();

            // 获取订单详情
            $order = $this->orderService->getOrderDetail($id, $userId);

            // 检查订单是否可以重新支付
            if (!$this->canRepayOrder($order)) {
                return $this->error(__('order.cannot_repay'), null, 400);
            }

            // 创建或获取PaymentIntent
            $paymentData = $this->getOrCreatePaymentIntent($order);

            return $this->success([
                'order' => $this->formatOrderResponse($order),
                'payment_data' => $paymentData
            ], __('order.payment_intent_created'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取订单处理进度
     */
    public function getProcessingProgress($id)
    {
        try {
            $userId = Auth::id();
            $order = $this->orderService->getOrderDetail($id, $userId);

            $totalItems = $order->items->count();
            $completedItems = $order->items->where('status', 'completed')->count();
            $processingItems = $order->items->where('status', 'processing')->count();
            $failedItems = $order->items->where('status', 'failed')->count();

            $progress = [
                'total_items' => $totalItems,
                'completed_items' => $completedItems,
                'processing_items' => $processingItems,
                'failed_items' => $failedItems,
                'progress_percentage' => $totalItems > 0 ? round(($completedItems / $totalItems) * 100, 2) : 0,
                'status' => $order->status,
                'status_text' => __('order.status.' . $order->status),
                'can_update_message' => $order->canUpdateMessage(),
                // 'should_auto_confirm' => $order->shouldAutoConfirm(),
                'items_detail' => $order->items->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'status' => $item->status,
                        'status_text' => __('order.status.' . $item->status),
                        'progress' => $item->processing_progress ?? 0,
                        'picbook_name' => $item->picbook->default_name ?? '',
                        'recipient_name' => $item->recipient_name
                    ];
                })
            ];

            return $this->success($progress, __('order.processing_progress'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 选择物流方式
     */
    public function selectShippingMethod(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'shipping_method' => 'required|string',
                'shipping_cost' => 'required|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            $userId = Auth::id();
            $order = $this->orderService->updateShippingMethod(
                $id, 
                $userId, 
                $request->shipping_method, 
                $request->shipping_cost
            );

            $responseData = [
                'order' => $this->formatOrderResponse($order),
            ];
            if ($this->shouldIncludePaymentIntent($order)) {
                try {
                    $paymentData = $this->getOrCreatePaymentIntent($order);
                    $responseData['payment_data'] = $paymentData;
                } catch (\Exception $e) {
                    Log::warning('获取PaymentIntent失败', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage()
                    ]);
                    // 不影响订单详情的返回，只是没有支付数据
                }
            }

            return $this->success($responseData, __('order.shipping_method_updated'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取物流跟踪信息
     */
    public function getTrackingInfo($id)
    {
        try {
            $userId = Auth::id();
            $order = $this->orderService->getOrderDetail($id, $userId);

            if (!$order->tracking_number) {
                return $this->error(__('order.no_tracking_number'), null, 404);
            }

            // 注意：ExpService中没有getTrackingInfo方法，需要实现或使用其他方法
            // $trackingInfo = $this->expService->getTrackingInfo($order->tracking_number);
            $trackingInfo = ['message' => 'Tracking info not implemented yet'];

            return $this->success([
                'order' => $this->formatOrderResponse($order),
                'tracking_info' => $trackingInfo
            ], __('order.tracking_info'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 计算包裹信息（重量和尺寸）
     *
     * @param array $cartItemIds 购物车项ID数组
     * @return array
     */
    private function calculatePackageInfo(array $cartItemIds)
    {
        // 从配置文件获取绘本物理参数
        $bookWeight = config('picbook.physical.weight', 500);
        $bookDimensions = [
            config('picbook.physical.dimensions.length', 20),
            config('picbook.physical.dimensions.width', 15),
            config('picbook.physical.dimensions.height', 2)
        ];
        $packagingWeight = config('picbook.physical.packaging_weight', 50);
        $packagingDimensions = [
            config('picbook.physical.packaging_dimensions.length', 2),
            config('picbook.physical.packaging_dimensions.width', 2),
            config('picbook.physical.packaging_dimensions.height', 1)
        ];

        // 计算总重量和尺寸
        $totalWeight = $packagingWeight;
        $maxLength = $packagingDimensions[0];
        $maxWidth = $packagingDimensions[1];
        $totalHeight = $packagingDimensions[2];

        $totalBooks = 0;

        foreach ($cartItemIds as $cartItemId) {
            $cartItem = \App\Models\CartItem::find($cartItemId);
            if ($cartItem) {
                $quantity = $cartItem->quantity;
                $totalBooks += $quantity;

                // 累加重量
                $totalWeight += $bookWeight * $quantity;

                // 计算包装尺寸（假设书籍叠放）
                $maxLength = max($maxLength, $bookDimensions[0] + $packagingDimensions[0]);
                $maxWidth = max($maxWidth, $bookDimensions[1] + $packagingDimensions[1]);
                $totalHeight += $bookDimensions[2] * $quantity;
            }
        }

        // 如果没有找到商品，使用默认值（单本书）
        if ($totalBooks === 0) {
            $totalWeight = $bookWeight + $packagingWeight;
            $dimensions = [
                $bookDimensions[0] + $packagingDimensions[0],
                $bookDimensions[1] + $packagingDimensions[1],
                $bookDimensions[2] + $packagingDimensions[2]
            ];
        } else {
            $dimensions = [$maxLength, $maxWidth, $totalHeight + $packagingDimensions[2]];
        }

        return [
            'weight' => $totalWeight,
            'dimensions' => $dimensions,
            'total_books' => $totalBooks
        ];
    }

    /**
     * 获取绘本物理参数配置
     */
    // public function getPhysicalConfig()
    // {
    //     try {
    //         $config = [
    //             'book' => [
    //                 'weight' => config('picbook.physical.weight', 500),
    //                 'dimensions' => [
    //                     'length' => config('picbook.physical.dimensions.length', 20),
    //                     'width' => config('picbook.physical.dimensions.width', 15),
    //                     'height' => config('picbook.physical.dimensions.height', 2),
    //                 ],
    //                 'weight_unit' => 'g',
    //                 'dimension_unit' => 'cm'
    //             ],
    //             'packaging' => [
    //                 'weight' => config('picbook.physical.packaging_weight', 50),
    //                 'dimensions' => [
    //                     'length' => config('picbook.physical.packaging_dimensions.length', 2),
    //                     'width' => config('picbook.physical.packaging_dimensions.width', 2),
    //                     'height' => config('picbook.physical.packaging_dimensions.height', 1),
    //                 ],
    //                 'weight_unit' => 'g',
    //                 'dimension_unit' => 'cm'
    //             ],
    //             'shipping' => [
    //                 'cost_tolerance' => config('picbook.shipping.cost_validation_tolerance', 0.01),
    //                 'abnormal_threshold' => config('picbook.shipping.abnormal_cost_threshold', 100.00),
    //                 'default_currency' => config('picbook.shipping.default_currency', 'USD'),
    //                 'recommended_products' => config('picbook.shipping.recommended_products', [])
    //             ]
    //         ];

    //         return $this->success($config, '获取物理参数配置成功');

    //     } catch (\Exception $e) {
    //         return $this->error($e->getMessage(), null, 500);
    //     }
    // }

    /**
     * 创建PaymentIntent
     *
     * @param Order $order 订单对象
     * @return array 支付数据
     */
    private function createPaymentIntent(Order $order)
    {
        try {
            // 创建PaymentIntent
            $paymentIntent = $this->stripeService->createPaymentIntent($order);

            // 更新订单支付信息
            $order->payment_method = 'stripe';
            $order->setStripePaymentIntent($paymentIntent->id, [
                'amount' => $paymentIntent->amount,
                'currency' => $paymentIntent->currency,
                'status' => $paymentIntent->status,
                'created_at' => $paymentIntent->created
            ]);

            return [
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'publishable_key' => config('stripe.key'),
                'amount' => $paymentIntent->amount,
                'currency' => $paymentIntent->currency
            ];
        } catch (\Exception $e) {
            Log::error('创建PaymentIntent失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            throw new \Exception('创建支付失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取支付链接（保留兼容性）
     *
     * @param Order $order 订单
     * @param string $paymentMethod 支付方式
     * @return string|null 支付链接
     */
    private function getPaymentUrl(Order $order, string $paymentMethod)
    {
        // 根据不同的支付方式，返回不同的支付链接
        switch ($paymentMethod) {
            case 'stripe':
                return '/api/stripe/create-checkout-session';
            case 'paypal':
                return '/api/payment/paypal/' . $order->id;
            case 'alipay':
                return '/api/payment/alipay/' . $order->id;
            case 'wechat':
                return '/api/payment/wechat/' . $order->id;
            case 'bank_transfer':
                return '/api/payment/bank-transfer/' . $order->id;
            default:
                return null;
        }
    }

    /**
     * 判断是否应该包含PaymentIntent信息
     *
     * @param Order $order 订单对象
     * @return bool
     */
    private function shouldIncludePaymentIntent(Order $order)
    {
        // 只有待支付状态的订单才需要PaymentIntent
        return $order->payment_status === Order::PAYMENT_PENDING &&
            $order->status === Order::STATUS_PENDING &&
            $order->payment_method === 'stripe';
    }

    /**
     * 判断订单是否可以重新支付
     *
     * @param Order $order 订单对象
     * @return bool
     */
    private function canRepayOrder(Order $order)
    {
        // 只有待支付或支付失败的订单可以重新支付
        return in_array($order->payment_status, [Order::PAYMENT_PENDING, Order::PAYMENT_FAILED]) &&
            $order->status === Order::STATUS_PENDING &&
            in_array($order->payment_method, ['stripe', null]);
    }

    /**
     * 获取或创建PaymentIntent
     *
     * @param Order $order 订单对象
     * @return array|null 支付数据
     */
    private function getOrCreatePaymentIntent(Order $order)
    {
        // 如果订单已有有效的PaymentIntent，尝试获取它
        if ($order->stripe_payment_intent_id) {
            try {
                $paymentIntent = $this->stripeService->retrievePaymentIntent($order->stripe_payment_intent_id);

                // 检查PaymentIntent状态是否仍然有效
                if (in_array($paymentIntent->status, ['requires_payment_method', 'requires_confirmation', 'requires_action'])) {
                    return [
                        'client_secret' => $paymentIntent->client_secret,
                        'payment_intent_id' => $paymentIntent->id,
                        'publishable_key' => config('stripe.key'),
                        'amount' => $paymentIntent->amount,
                        'currency' => $paymentIntent->currency,
                        'status' => $paymentIntent->status,
                        'is_existing' => true
                    ];
                }
            } catch (\Exception $e) {
                Log::warning('获取现有PaymentIntent失败，将创建新的', [
                    'order_id' => $order->id,
                    'payment_intent_id' => $order->stripe_payment_intent_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // 创建新的PaymentIntent
        $paymentData = $this->createPaymentIntent($order);
        $paymentData['is_existing'] = false;

        return $paymentData;
    }

    /**
     * 格式化订单响应数据，添加本地化信息
     *
     * @param Order $order 订单对象
     * @return array 格式化后的订单数据
     */
    private function formatOrderResponse($order)
    {
        $orderArray = $order->toArray();

        // 添加本地化的状态信息
        $orderArray['status_text'] = __('order.status.' . $order->status);
        $orderArray['payment_status_text'] = __('order.payment_status.' . $order->payment_status);

        // 格式化订单项
        if (isset($orderArray['items']) && is_array($orderArray['items'])) {
            foreach ($orderArray['items'] as &$item) {
                // 添加本地化的状态信息
                if (isset($item['status'])) {
                    $item['status_text'] = __('order.status.' . $item['status']);
                }
            }
        }

        // 添加地址状态信息
        $orderArray['address_status'] = [
            'needs_shipping_address' => $order->needsShippingAddress(),
            'can_ship' => $order->canShip(),
            'has_shipping_address' => !empty($order->shipping_address),
            'has_billing_address' => !empty($order->billing_address)
        ];

        // 添加物流状态信息
        $orderArray['logistics_status'] = [
            'can_create_logistics' => $order->canCreateLogistics(),
            'has_logistics_order' => $order->hasLogisticsOrder(),
            'logistics_request_no' => $order->logistics_request_no,
            'logistics_status' => $order->logistics_status,
            'tracking_number' => $order->tracking_number
        ];

        // 添加物流选项信息（如果有的话）
        if (!empty($order->shipping_options)) {
            $orderArray['shipping_options'] = $order->shipping_options;
        } else if ($order->shipping_address && isset($order->shipping_address['country'])) {
            // 如果没有保存的物流选项但有地址，尝试获取当前的物流选项
            try {
                $orderArray['shipping_options'] = $this->orderService->getShippingOptionsForOrder($order);
            } catch (\Exception $e) {
                Log::warning('获取订单物流选项失败', [
                    'order_id' => $order->id,
                    'error' => $e->getMessage()
                ]);
                $orderArray['shipping_options'] = [];
            }
        } else {
            $orderArray['shipping_options'] = [];
        }

        // 添加操作权限信息
        $orderArray['permissions'] = [
            'can_cancel' => in_array($order->status, [Order::STATUS_PENDING, Order::STATUS_PROCESSING]),
            'can_update_message' => $order->canUpdateMessage(),
            'can_update_address' => in_array($order->status, [Order::STATUS_PENDING, Order::STATUS_PROCESSING]),
            'can_confirm' => $order->status === Order::STATUS_PROCESSING,
            // 'should_auto_confirm' => $order->shouldAutoConfirm(),
            'can_pay' => $order->payment_status === Order::PAYMENT_PENDING && $order->status === Order::STATUS_PENDING,
            'needs_address_before_shipping' => $order->needsShippingAddress() && $order->payment_status === Order::PAYMENT_PAID
        ];

        return $orderArray;
    }
}
