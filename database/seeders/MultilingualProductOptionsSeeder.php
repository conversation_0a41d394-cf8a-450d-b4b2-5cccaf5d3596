<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductOption;
use Illuminate\Support\Facades\DB;

class MultilingualProductOptionsSeeder extends Seeder
{
    /**
     * 将现有的产品选项转换为多语言格式
     *
     * @return void
     */
    public function run()
    {
        $options = ProductOption::all();
        
        foreach ($options as $option) {
            // 跳过已经是数组格式的选项
            if (is_array($option->name) || is_array($option->description)) {
                continue;
            }
            
            // 将名称转换为多语言格式
            $name = $option->name;
            $mlName = [
                'en' => $name,
                'zh' => $name, // 初始化中文与英文相同，后续可手动更新
            ];
            
            // 将描述转换为多语言格式
            $description = $option->description;
            $mlDescription = null;
            
            if ($description) {
                $mlDescription = [
                    'en' => $description,
                    'zh' => $description, // 初始化中文与英文相同，后续可手动更新
                ];
            }
            
            // 更新记录
            DB::table('product_options')
                ->where('id', $option->id)
                ->update([
                    'name' => $mlName,
                    'description' => $mlDescription,
                    'updated_at' => now()
                ]);
                
            $this->command->info("已将选项 {$option->id} ({$option->option_type}:{$option->option_key}) 转换为多语言格式");
        }
        
        $this->command->info('所有产品选项已成功转换为多语言格式');
    }
} 