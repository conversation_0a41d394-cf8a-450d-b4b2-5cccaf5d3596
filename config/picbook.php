<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 绘本物理参数配置
    |--------------------------------------------------------------------------
    |
    | 这些参数用于计算物流费用和包装尺寸
    |
    */
    'physical' => [
        // 单本绘本重量（克）- 实际0.6kg = 600g
        'weight' => env('PICBOOK_WEIGHT', 600),
        
        // 单本绘本尺寸（厘米）- 实际365x265x50mm = 36.5x26.5x5cm
        'dimensions' => [
            'length' => env('PICBOOK_LENGTH', 36.5),
            'width' => env('PICBOOK_WIDTH', 26.5),
            'height' => env('PICBOOK_HEIGHT', 5.0),
        ],
        
        // 包装额外重量（克）- 包装盒、填充物等
        'packaging_weight' => env('PICBOOK_PACKAGING_WEIGHT', 100),
        
        // 包装额外尺寸（厘米）- 包装盒厚度
        'packaging_dimensions' => [
            'length' => env('PICBOOK_PACKAGING_LENGTH', 3),
            'width' => env('PICBOOK_PACKAGING_WIDTH', 3),
            'height' => env('PICBOOK_PACKAGING_HEIGHT', 2),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 物流配置
    |--------------------------------------------------------------------------
    */
    'shipping' => [
        // 费用校验允许的误差（美元）
        'cost_validation_tolerance' => env('SHIPPING_COST_TOLERANCE', 0.01),
        
        // 异常费用阈值（美元）
        'abnormal_cost_threshold' => env('SHIPPING_ABNORMAL_THRESHOLD', 100.00),
        
        // 默认货币
        'default_currency' => env('SHIPPING_DEFAULT_CURRENCY', 'USD'),
        
        // 推荐的物流产品代码
        'recommended_products' => [
            'YANWEN_ECONOMY' => '燕文经济小包',
            'YANWEN_STANDARD' => '燕文标准小包', 
            'YANWEN_EXPRESS' => '燕文快递',
            'CAINIAO_ECONOMY' => '菜鸟经济小包',
            'CAINIAO_STANDARD' => '菜鸟标准小包'
        ],
    ],
];