<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserAddress>
 */
class UserAddressFactory extends Factory
{
    protected $model = UserAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'company' => $this->faker->optional()->company(),
            'phone' => $this->faker->phoneNumber(),
            'phone2' => $this->faker->optional()->phoneNumber(),
            'email' => $this->faker->optional()->email(),
            'post_code' => $this->faker->postcode(),
            'country' => $this->faker->countryCode(),
            'state' => $this->faker->optional()->state(),
            'city' => $this->faker->city(),
            'district' => $this->faker->optional()->citySuffix(),
            'street' => $this->faker->streetAddress(),
            'house_number' => $this->faker->optional()->buildingNumber(),
            'second_name' => $this->faker->optional()->name(),
            'is_default' => false,
        ];
    }

    /**
     * Indicate that the address is the default address.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }
}