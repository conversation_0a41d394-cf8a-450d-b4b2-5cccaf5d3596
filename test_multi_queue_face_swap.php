<?php
/**
 * 多队列换脸测试脚本
 */
require __DIR__.'/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Log;

$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// 测试参数
$user_id = 7; // 假设这是一个有效的用户ID
$is_priority = true; // 测试优先级队列 (用于用户下单后需要生成的页面)
$batch_id = \Illuminate\Support\Str::uuid(); // 生成一个唯一的批次ID

// 准备测试数据
$pages = [
    [
        'page_id' => 8,
        'variant_id' => 326
    ]
];

// 从测试图片文件读取base64
$face_image = base64_encode(file_get_contents(storage_path('app/public/test_face.jpg')));

echo "开始多队列换脸测试\n";
echo "批次ID: {$batch_id}\n";
echo "用户ID: {$user_id}\n";
echo "是否优先任务: " . ($is_priority ? "是" : "否") . "\n";
echo "页面数量: " . count($pages) . "\n";

try {
    // 记录开始
    Log::info('开始多队列换脸测试', [
        'batch_id' => $batch_id,
        'user_id' => $user_id,
        'pages_count' => count($pages),
        'is_priority' => $is_priority
    ]);
    
    // 分发批处理任务
    \App\Jobs\ProcessFaceSwapBatch::dispatch(
        $batch_id,
        $pages,
        $face_image,
        $user_id,
        $is_priority
    );
    
    echo "测试任务已分发到队列\n";
    echo "批次处理已开始，请检查日志获取更多信息\n";
    
} catch (\Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    Log::error('多队列换脸测试失败', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}