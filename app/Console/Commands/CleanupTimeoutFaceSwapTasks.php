<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AiFaceTask;
use App\Models\PicbookPreview;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CleanupTimeoutFaceSwapTasks extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'faceswap:cleanup-timeout {--timeout=30 : 超时时间（分钟）}';

    /**
     * The console command description.
     */
    protected $description = '清理超时的换脸任务';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $timeoutMinutes = $this->option('timeout');
        $timeoutTime = Carbon::now()->subMinutes($timeoutMinutes);
        
        $this->info("开始清理超过 {$timeoutMinutes} 分钟的超时任务...");
        
        // 查找超时的处理中任务
        $timeoutTasks = AiFaceTask::where('status', 'processing')
            ->where('updated_at', '<', $timeoutTime)
            ->get();
            
        if ($timeoutTasks->isEmpty()) {
            $this->info('没有发现超时任务');
            return;
        }
        
        $cleanedCount = 0;
        
        foreach ($timeoutTasks as $task) {
            // 更新任务状态为失败
            $task->update([
                'status' => 'failed',
                'error_message' => "任务超时（超过{$timeoutMinutes}分钟未完成）",
                'completed_at' => now()
            ]);
            
            $cleanedCount++;
            
            // 如果是批次任务，重置为pending状态以便重新处理
            if ($task->type === 'batch') {
                $task->update(['status' => 'pending']);
                $this->warn("批次 {$task->batch_id} 已重置为待处理状态");
            }
            
            Log::warning('清理超时任务', [
                'task_id' => $task->id,
                'batch_id' => $task->batch_id,
                'type' => $task->type,
                'timeout_minutes' => $timeoutMinutes
            ]);
        }
        
        // 更新相关的预览状态
        $timeoutBatches = $timeoutTasks->where('type', 'batch')->pluck('batch_id');
        if ($timeoutBatches->isNotEmpty()) {
            PicbookPreview::whereIn('batch_id', $timeoutBatches)
                ->update(['status' => PicbookPreview::STATUS_PENDING]);
        }
        
        $this->info("已清理 {$cleanedCount} 个超时任务");
        
        // 显示当前队列状态
        $this->displayQueueStatus();
    }
    
    private function displayQueueStatus()
    {
        $stats = [
            'pending_batches' => AiFaceTask::where('type', 'batch')->where('status', 'pending')->count(),
            'processing_batches' => AiFaceTask::where('type', 'batch')->where('status', 'processing')->count(),
            'pending_tasks' => AiFaceTask::where('type', 'task')->where('status', 'pending')->count(),
            'processing_tasks' => AiFaceTask::where('type', 'task')->where('status', 'processing')->count(),
        ];
        
        $this->table(
            ['状态', '批次数', '任务数'],
            [
                ['待处理', $stats['pending_batches'], $stats['pending_tasks']],
                ['处理中', $stats['processing_batches'], $stats['processing_tasks']]
            ]
        );
    }
}