<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ExpService
{
    // 4px快递服务
    protected $baseUrl;
    protected $appKey;
    protected $appSecret;
    protected $token;
    protected $tokenExpireTime;

    public function __construct()
    {
        $this->baseUrl = config('services.4px.base_url', 'https://open.4px.com/router/api/service');
        $this->appKey = config('services.4px.app_key');
        $this->appSecret = config('services.4px.app_secret');
        $this->token = null;
        $this->tokenExpireTime = 0;
    }

    /**
     * 生成API签名
     * 
     * @param array $requestData 请求参数
     * @param array $data 请求数据
     * @return string
     */
    protected function generateSign($requestData, $data)
    {
        // 按照4PX API官方文档要求生成签名

        // 1. 移除不参与签名的参数（access_token和language）
        $signParams = $requestData;
        unset($signParams['access_token']);
        unset($signParams['language']);
        unset($signParams['sign']); // 移除sign本身

        // 2. 按首字母升序排序
        ksort($signParams);

        // 3. 连接字符串(去掉所有=和&),连接参数名与参数值
        $signStr = '';
        foreach ($signParams as $key => $value) {
            $signStr .= $key . $value;
        }

        // 4. 在尾部加上body信息(JSON压缩格式)和appSecret
        $bodyJson = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        $signStr .= $bodyJson . $this->appSecret;

        // 5. 使用MD5加密生成32位小写签名值
        return strtolower(md5($signStr));
    }

    /**
     * 发送API请求
     * 
     * @param string $method API方法名
     * @param array $data 请求数据
     * @param bool $debug 是否开启调试模式
     * @return array
     */
    protected function sendRequest($method, $version, $data = [], $debug = false)
    {
        try {
            $timestamp = time() * 1000; // 毫秒级时间戳

            $requestData = [
                'app_key' => $this->appKey,
                'timestamp' => $timestamp,
                'method' => $method,
                'format' => 'json',
                'v' => $version ?? 'V1.1.0',
            ];

            $sign = $this->generateSign($requestData, $data);
            $requestData['sign'] = $sign;

            // URL需要拼接公共参数
            $url = $this->baseUrl . '?' . http_build_query($requestData);

            if ($debug) {
                Log::info('4PX API请求详情', [
                    'url' => $url,
                    'method' => $method,
                    'request_data' => $requestData,
                    'post_data' => $data,
                    'sign_string' => $this->buildSignString($requestData, $data)
                ]);
            }

            // 根据4PX API要求，使用JSON格式发送POST数据，并设置正确的字符编码
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json; charset=utf-8',
                    'Accept' => 'application/json; charset=utf-8',
                    // 'Accept-Charset' => 'utf-8'
                ])
                ->withBody(json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), 'application/json')
                ->post($url);
            // 处理响应编码问题
            $responseBody = $response->body();

            // 如果响应不是UTF-8编码，尝试转换
            if (!mb_check_encoding($responseBody, 'UTF-8')) {
                $responseBody = mb_convert_encoding($responseBody, 'UTF-8', 'auto');
            }

            $result = json_decode($responseBody, true);

            if ($debug) {
                Log::info('4PX API响应详情', [
                    'status_code' => $response->status(),
                    'response_headers' => $response->headers(),
                    'response' => $result,
                    'raw_response' => $responseBody
                ]);
            }

            if ($response->successful() && isset($result['result']) && $result['result'] == 1) {
                return !empty($result['data']) ? json_decode($result['data'], 256) : [];
            } else {
                $errorMessage = $result['msg'] ?? '未知错误';
                $errorCode = isset($result['errors']) && is_array($result['errors']) && !empty($result['errors'])
                    ? $result['errors'][0]['error_code'] ?? 'UNKNOWN'
                    : 'UNKNOWN';

                Log::error('4PX API请求失败', [
                    'method' => $method,
                    'error_code' => $errorCode,
                    'error_message' => $errorMessage,
                    'request_data' => $requestData,
                    'post_data' => $data,
                    'response' => $result
                ]);

                throw new \Exception("4PX API请求失败 [{$errorCode}]: {$errorMessage}");
            }
        } catch (\Exception $e) {
            Log::error('4PX API请求异常', [
                'method' => $method,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 构建签名字符串（用于调试）
     * 
     * @param array $requestData
     * @param array $data
     * @return string
     */
    private function buildSignString($requestData, $data)
    {
        // 移除不参与签名的参数
        $signParams = $requestData;
        unset($signParams['access_token']);
        unset($signParams['language']);
        unset($signParams['sign']);

        // 按首字母升序排序
        ksort($signParams);

        // 连接参数名与参数值
        $signStr = '';
        foreach ($signParams as $key => $value) {
            $signStr .= $key . $value;
        }

        // 添加body和appSecret
        $bodyJson = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        $signStr .= $bodyJson . $this->appSecret;

        return $signStr;
    }

    /**
     * 验证签名（用于调试）
     * 
     * @param array $requestData
     * @param array $data
     * @param string $expectedSign
     * @return array
     */
    public function verifySignature($requestData, $data, $expectedSign = null)
    {
        $signString = $this->buildSignString($requestData, $data);
        $calculatedSign = strtolower(md5($signString));

        return [
            'sign_string' => $signString,
            'calculated_sign' => $calculatedSign,
            'expected_sign' => $expectedSign,
            'is_valid' => $expectedSign ? ($calculatedSign === strtolower($expectedSign)) : null,
            'request_data' => $requestData,
            'post_data' => $data,
            'sign_algorithm' => '4PX官方文档标准算法',
            'algorithm_steps' => [
                '1. 移除access_token和language参数',
                '2. 按首字母升序排序',
                '3. 连接参数名与参数值',
                '4. 添加JSON body和appSecret',
                '5. MD5加密并转小写'
            ]
        ];
    }

    /**
     * 创建物流订单
     * 
     * @param array $orderData 订单数据
     * @param bool $debug 是否开启调试模式
     * @return array
     */
    public function createLogisticsOrder(array $orderData, $debug = false)
    {
        return $this->sendRequest('ds.xms.order.create', '1.1.0', $orderData, $debug);
    }

    /**
     * 查询直发委托单
     * 
     * @param string $requestNo 请求编号
     * @param bool $debug 是否开启调试模式
     * @return array
     */
    public function getLogisticsOrderInfo($requestNo, $debug = false)
    {
        return $this->sendRequest('ds.xms.order.get', '1.1.0', [
            'request_no' => $requestNo,
            'start_time_of_create_consignment' => null,
            'end_time_of_create_consignment' => null,
            'consignment_status' => null,
        ], $debug);
    }


    /**
     * 更新预报重量
     * 
     * @param string $requestNo 请求编号
     * @param float $weight 重量(克)
     * @param bool $debug 是否开启调试模式
     * @return array
     */
    public function updateWeight($requestNo, $weight, $debug = false)
    {
        return $this->sendRequest('ds.xms.order.updateweight', 'V1.1.0', [
            'request_no' => $requestNo,
            'weight' => $weight
        ], $debug);
    }

    /**
     * 取消直发委托单
     */
    public function cancelLogisticsOrder($requestNo, $cancel_reason, $debug = false)
    {
        return $this->sendRequest('ds.xms.order.cancel', 'V1.1.0', [
            'request_no' => $requestNo,
            'cancel_reason' => $cancel_reason
        ], $debug);
    }

    /**
     * 查询订单费用信息
     * 
     * @param string $requestNo 请求编号
     * @param bool $debug 是否开启调试模式
     * @return array
     */
    public function getFreight($requestNo, $debug = false)
    {
        return $this->sendRequest('ds.xms.order.getFreight', 'V1.1.0', [
            'request_no' => $requestNo
        ], $debug);
    }

    /**
     * 预估费用查询/运费试算
     * 
     * @param array $params 查询参数
     * @param bool $debug 是否开启调试模式
     * @return array
     */
    public function calculateFreight($params, $debug = true)
    {
        return $this->sendRequest('ds.xms.estimated_cost.get', 'V1.0.0', $params, $debug);
    }

    /**
     * 获取物流产品列表
     * 
     * @param array $params 查询参数
     * @param bool $debug 是否开启调试模式
     * @return array
     */
    public function getLogisticsProducts($params = [], $debug = false)
    {
        return $this->sendRequest('ds.xms.logistics_product.getlist', 'V1.0.0', $params, $debug);
    }

    /**
     * 获取国家列表
     * 
     * @param bool $debug 是否开启调试模式
     * @return array
     */
    public function getCountryList()
    {
        return $data = [
			[
				'name' => "热门国家",
				'country' => [
					[
						'name' => "HOT",
						'countrys' => [
							[
								'code' => "BE",
								'name' => "比利时",
								'ename' => "BELGIUM",
								'pinyin' => "bls",
								'isHot' => 1,
								'localeName' => "比利时",
								'display' => "比利时(BE)"
							],
							[
								'code' => "DE",
								'name' => "德国",
								'ename' => "GERMANY",
								'pinyin' => "dg",
								'isHot' => 1,
								'localeName' => "德国",
								'display' => "德国(DE)"
							],
							[
								'code' => "DK",
								'name' => "丹麦",
								'ename' => "DENMARK",
								'pinyin' => "dm",
								'isHot' => 1,
								'localeName' => "丹麦",
								'display' => "丹麦(DK)"
							],
							[
								'code' => "FR",
								'name' => "法国",
								'ename' => "FRANCE",
								'pinyin' => "fg",
								'isHot' => 1,
								'localeName' => "法国",
								'display' => "法国(FR)"
							],
							[
								'code' => "CA",
								'name' => "加拿大",
								'ename' => "CANADA",
								'pinyin' => "jnd",
								'isHot' => 1,
								'localeName' => "加拿大",
								'display' => "加拿大(CA)"
							],
							[
								'code' => "LU",
								'name' => "卢森堡",
								'ename' => "LUXEMBOURG",
								'pinyin' => "lsb",
								'isHot' => 1,
								'localeName' => "卢森堡",
								'display' => "卢森堡(LU)"
							],
							[
								'code' => "US",
								'name' => "美国",
								'ename' => "UNITED STATES",
								'pinyin' => "mg",
								'isHot' => 1,
								'localeName' => "美国",
								'display' => "美国(US)"
							],
							[
								'code' => "SE",
								'name' => "瑞典",
								'ename' => "SWEDEN",
								'pinyin' => "rd",
								'isHot' => 1,
								'localeName' => "瑞典",
								'display' => "瑞典(SE)"
							],
							[
								'code' => "ES",
								'name' => "西班牙",
								'ename' => "SPAIN",
								'pinyin' => "xby",
								'isHot' => 1,
								'localeName' => "西班牙",
								'display' => "西班牙(ES)"
							],
							[
								'code' => "IT",
								'name' => "意大利",
								'ename' => "ITALY",
								'pinyin' => "ydl",
								'isHot' => 1,
								'localeName' => "意大利",
								'display' => "意大利(IT)"
							],
							[
								'code' => "GB",
								'name' => "英国",
								'ename' => "UNITED KINGDOM",
								'pinyin' => "yg",
								'isHot' => 1,
								'localeName' => "英国",
								'display' => "英国(GB)"
							],
							[
								'code' => "IL",
								'name' => "以色列",
								'ename' => "ISRAEL",
								'pinyin' => "ysl",
								'isHot' => 1,
								'localeName' => "以色列",
								'display' => "以色列(IL)"
							],
						]
					]
				]
			],
			[
				'name' => "ABCDEF",
				'country' => [
					[
						'name' => "A",
						'countrys' => [
							[
								'code' => "AD",
								'name' => "安道尔",
								'ename' => "ANDORRA",
								'pinyin' => "ade",
								'isHot' => 0,
								'localeName' => "安道尔",
								'display' => "安道尔(AD)"
							],
							[
								'code' => "AT",
								'name' => "奥地利",
								'ename' => "AUSTRIA",
								'pinyin' => "adl",
								'isHot' => 0,
								'localeName' => "奥地利",
								'display' => "奥地利(AT)"
							],
							[
								'code' => "AU",
								'name' => "澳大利亚",
								'ename' => "AUSTRALIA",
								'pinyin' => "adly",
								'isHot' => 0,
								'localeName' => "澳大利亚",
								'display' => "澳大利亚(AU)"
							],
							[
								'code' => "AL",
								'name' => "阿尔巴尼亚",
								'ename' => "ALBANIA",
								'pinyin' => "aebny",
								'isHot' => 0,
								'localeName' => "阿尔巴尼亚",
								'display' => "阿尔巴尼亚(AL)"
							],
							[
								'code' => "XA",
								'name' => "奥尔德尼岛",
								'ename' => "Alderney",
								'pinyin' => "aednd",
								'isHot' => 0,
								'localeName' => "奥尔德尼岛",
								'display' => "奥尔德尼岛(XA)"
							],
							[
								'code' => "DZ",
								'name' => "阿尔及利亚",
								'ename' => "ALGERIA",
								'pinyin' => "aejly",
								'isHot' => 0,
								'localeName' => "阿尔及利亚",
								'display' => "阿尔及利亚(DZ)"
							],
							[
								'code' => "IE",
								'name' => "爱尔兰",
								'ename' => "IRELAND",
								'pinyin' => "ael",
								'isHot' => 0,
								'localeName' => "爱尔兰",
								'display' => "爱尔兰(IE)"
							],
							[
								'code' => "AF",
								'name' => "阿富汗",
								'ename' => "AFGHANISTAN",
								'pinyin' => "afh",
								'isHot' => 0,
								'localeName' => "阿富汗",
								'display' => "阿富汗(AF)"
							],
							[
								'code' => "AO",
								'name' => "安哥拉",
								'ename' => "ANGOLA",
								'pinyin' => "agl",
								'isHot' => 0,
								'localeName' => "安哥拉",
								'display' => "安哥拉(AO)"
							],
							[
								'code' => "AI",
								'name' => "安圭拉岛",
								'ename' => "ANGUILLA",
								'pinyin' => "agld",
								'isHot' => 0,
								'localeName' => "安圭拉岛",
								'display' => "安圭拉岛(AI)"
							],
							[
								'code' => "AR",
								'name' => "阿根廷",
								'ename' => "ARGENTINA",
								'pinyin' => "agt",
								'isHot' => 0,
								'localeName' => "阿根廷",
								'display' => "阿根廷(AR)"
							],
							[
								'code' => "EG",
								'name' => "埃及",
								'ename' => "EGYPT",
								'pinyin' => "aj",
								'isHot' => 0,
								'localeName' => "埃及",
								'display' => "埃及(EG)"
							],
							[
								'code' => "AW",
								'name' => "阿鲁巴岛",
								'ename' => "ARUBA",
								'pinyin' => "albd",
								'isHot' => 0,
								'localeName' => "阿鲁巴岛",
								'display' => "阿鲁巴岛(AW)"
							],
							[
								'code' => "AE",
								'name' => "阿拉伯联合酋长国",
								'ename' => "UNITED ARAB EMIRATES",
								'pinyin' => "alblhqzg",
								'isHot' => 0,
								'localeName' => "阿拉伯联合酋长国",
								'display' => "阿拉伯联合酋长国(AE)"
							],
							[
								'code' => "AX",
								'name' => "奥兰群岛",
								'ename' => "Aland Islands",
								'pinyin' => "alqd",
								'isHot' => 0,
								'localeName' => "奥兰群岛",
								'display' => "奥兰群岛(AX)"
							],
							[
								'code' => "MO",
								'name' => "中国 --- 澳门特别行政区",
								'ename' => "CHINA-Macao",
								'pinyin' => "am",
								'isHot' => 0,
								'localeName' => "中国 --- 澳门特别行政区",
								'display' => "中国 --- 澳门特别行政区(MO)"
							],
							[
								'code' => "OM",
								'name' => "阿曼",
								'ename' => "OMAN",
								'pinyin' => "am",
								'isHot' => 0,
								'localeName' => "阿曼",
								'display' => "阿曼(OM)"
							],
							[
								'code' => "AZ",
								'name' => "阿塞拜疆(独联体)",
								'ename' => "AZERBAIJAN",
								'pinyin' => "asbjdlt",
								'isHot' => 0,
								'localeName' => "阿塞拜疆(独联体)",
								'display' => "阿塞拜疆(独联体)(AZ)"
							],
							[
								'code' => "ET",
								'name' => "埃塞俄比亚",
								'ename' => "ETHIOPIA",
								'pinyin' => "aseby",
								'isHot' => 0,
								'localeName' => "埃塞俄比亚",
								'display' => "埃塞俄比亚(ET)"
							],
							[
								'code' => "EE",
								'name' => "爱沙尼亚",
								'ename' => "ESTONIA",
								'pinyin' => "asny",
								'isHot' => 0,
								'localeName' => "爱沙尼亚",
								'display' => "爱沙尼亚(EE)"
							],
							[
								'code' => "XD",
								'name' => "阿森松",
								'ename' => "ASCENSION",
								'pinyin' => "ass",
								'isHot' => 0,
								'localeName' => "阿森松",
								'display' => "阿森松(XD)"
							],
							[
								'code' => "AG",
								'name' => "安提瓜及巴布达",
								'ename' => "ANTIGUA AND BARBUDA",
								'pinyin' => "atgjbbd",
								'isHot' => 0,
								'localeName' => "安提瓜及巴布达",
								'display' => "安提瓜及巴布达(AG)"
							]
						]
					],
					[
						'name' => "B",
						'countrys' => [
							[
								'code' => "BB",
								'name' => "巴巴多斯",
								'ename' => "BARBADOS",
								'pinyin' => "bbds",
								'isHot' => 0,
								'localeName' => "巴巴多斯",
								'display' => "巴巴多斯(BB)"
							],
							[
								'code' => "PG",
								'name' => "巴布亚新几内亚",
								'ename' => "PAPUA NEW GUINEA",
								'pinyin' => "bbyxjny",
								'isHot' => 0,
								'localeName' => "巴布亚新几内亚",
								'display' => "巴布亚新几内亚(PG)"
							],
							[
								'code' => "BW",
								'name' => "博茨瓦纳",
								'ename' => "BOTSWANA",
								'pinyin' => "bcwn",
								'isHot' => 0,
								'localeName' => "博茨瓦纳",
								'display' => "博茨瓦纳(BW)"
							],
							[
								'code' => "BT",
								'name' => "不丹",
								'ename' => "BHUTAN",
								'pinyin' => "bd",
								'isHot' => 0,
								'localeName' => "不丹",
								'display' => "不丹(BT)"
							],
							[
								'code' => "IS",
								'name' => "冰岛",
								'ename' => "ICELAND",
								'pinyin' => "bd",
								'isHot' => 0,
								'localeName' => "冰岛",
								'display' => "冰岛(IS)"
							],
							[
								'code' => "PR",
								'name' => "波多黎各",
								'ename' => "PUERTO RICO",
								'pinyin' => "bdlg",
								'isHot' => 0,
								'localeName' => "波多黎各",
								'display' => "波多黎各(PR)"
							],
							[
								'code' => "BY",
								'name' => "白俄罗斯(独联体)",
								'ename' => "BELARUS",
								'pinyin' => "belsdlt",
								'isHot' => 0,
								'localeName' => "白俄罗斯(独联体)",
								'display' => "白俄罗斯(独联体)(BY)"
							],
							[
								'code' => "XG",
								'name' => "北非西班牙属土",
								'ename' => "SPANISH TERRITORIES OF N.AFRICA",
								'pinyin' => "bfxbyst",
								'isHot' => 0,
								'localeName' => "北非西班牙属土",
								'display' => "北非西班牙属土(XG)"
							],
							[
								'code' => "BS",
								'name' => "巴哈马",
								'ename' => "BAHAMAS",
								'pinyin' => "bhm",
								'isHot' => 0,
								'localeName' => "巴哈马",
								'display' => "巴哈马(BS)"
							],
							[
								'code' => "BG",
								'name' => "保加利亚",
								'ename' => "BULGARIA",
								'pinyin' => "bjly",
								'isHot' => 0,
								'localeName' => "保加利亚",
								'display' => "保加利亚(BG)"
							],
							[
								'code' => "BF",
								'name' => "布基纳法索",
								'ename' => "BURKINA FASO",
								'pinyin' => "bjnfs",
								'isHot' => 0,
								'localeName' => "布基纳法索",
								'display' => "布基纳法索(BF)"
							],
							[
								'code' => "PK",
								'name' => "巴基斯坦",
								'ename' => "PAKISTAN",
								'pinyin' => "bjst",
								'isHot' => 0,
								'localeName' => "巴基斯坦",
								'display' => "巴基斯坦(PK)"
							],
							[
								'code' => "BH",
								'name' => "巴林",
								'ename' => "BAHRAIN",
								'pinyin' => "bl",
								'isHot' => 0,
								'localeName' => "巴林",
								'display' => "巴林(BH)"
							],
							[
								'code' => "PL",
								'name' => "波兰",
								'ename' => "POLAND",
								'pinyin' => "bl",
								'isHot' => 0,
								'localeName' => "波兰",
								'display' => "波兰(PL)"
							],
							[
								'code' => "XJ",
								'name' => "巴利阿里群岛",
								'ename' => "BALEARIC ISLANDS",
								'pinyin' => "blalqd",
								'isHot' => 0,
								'localeName' => "巴利阿里群岛",
								'display' => "巴利阿里群岛(XJ)"
							],
							[
								'code' => "BI",
								'name' => "布隆迪",
								'ename' => "BURUNDI",
								'pinyin' => "bld",
								'isHot' => 0,
								'localeName' => "布隆迪",
								'display' => "布隆迪(BI)"
							],
							[
								'code' => "PY",
								'name' => "巴拉圭",
								'ename' => "PARAGUAY",
								'pinyin' => "blg",
								'isHot' => 0,
								'localeName' => "巴拉圭",
								'display' => "巴拉圭(PY)"
							],
							[
								'code' => "BE",
								'name' => "比利时",
								'ename' => "BELGIUM",
								'pinyin' => "bls",
								'isHot' => 1,
								'localeName' => "比利时",
								'display' => "比利时(BE)"
							],
							[
								'code' => "PS",
								'name' => "巴勒斯坦",
								'ename' => "PALESTINE",
								'pinyin' => "blst",
								'isHot' => 0,
								'localeName' => "巴勒斯坦",
								'display' => "巴勒斯坦(PS)"
							],
							[
								'code' => "BO",
								'name' => "玻利维亚",
								'ename' => "BOLIVIA",
								'pinyin' => "blwy",
								'isHot' => 0,
								'localeName' => "玻利维亚",
								'display' => "玻利维亚(BO)"
							],
							[
								'code' => "BZ",
								'name' => "伯利兹",
								'ename' => "BELIZE",
								'pinyin' => "blz",
								'isHot' => 0,
								'localeName' => "伯利兹",
								'display' => "伯利兹(BZ)"
							],
							[
								'code' => "BM",
								'name' => "百慕大",
								'ename' => "BERMUDA",
								'pinyin' => "bmd",
								'isHot' => 0,
								'localeName' => "百慕大",
								'display' => "百慕大(BM)"
							],
							[
								'code' => "BJ",
								'name' => "贝宁",
								'ename' => "BENIN",
								'pinyin' => "bn",
								'isHot' => 0,
								'localeName' => "贝宁",
								'display' => "贝宁(BJ)"
							],
							[
								'code' => "XB",
								'name' => "伯奈尔岛",
								'ename' => "BONAIRE",
								'pinyin' => "bned",
								'isHot' => 0,
								'localeName' => "伯奈尔岛",
								'display' => "伯奈尔岛(XB)"
							],
							[
								'code' => "PA",
								'name' => "巴拿马",
								'ename' => "PANAMA",
								'pinyin' => "bnm",
								'isHot' => 0,
								'localeName' => "巴拿马",
								'display' => "巴拿马(PA)"
							],
							[
								'code' => "BA",
								'name' => "波斯尼亚-黑塞哥维那共和国",
								'ename' => "BOSNIA AND HERZEGOVINA",
								'pinyin' => "bsnyhsgwnghg",
								'isHot' => 0,
								'localeName' => "波斯尼亚-黑塞哥维那共和国",
								'display' => "波斯尼亚-黑塞哥维那共和国(BA)"
							],
							[
								'code' => "BV",
								'name' => "布维岛",
								'ename' => "BOUVET ISLAND",
								'pinyin' => "bwd",
								'isHot' => 0,
								'localeName' => "布维岛",
								'display' => "布维岛(BV)"
							],
							[
								'code' => "BR",
								'name' => "巴西",
								'ename' => "BRAZIL",
								'pinyin' => "bx",
								'isHot' => 0,
								'localeName' => "巴西",
								'display' => "巴西(BR)"
							]
						]
					],
					[
						'name' => "C",
						'countrys' => [
							[
								'code' => "GQ",
								'name' => "赤道几内亚",
								'ename' => "EQUATORIAL GUINEA",
								'pinyin' => "cdjny",
								'isHot' => 0,
								'localeName' => "赤道几内亚",
								'display' => "赤道几内亚(GQ)"
							],
							[
								'code' => "KP",
								'name' => "朝鲜",
								'ename' => "NORTH KOREA\n",
								'pinyin' => "cx",
								'isHot' => 0,
								'localeName' => "朝鲜",
								'display' => "朝鲜(KP)"
							]
						]
					],
					[
						'name' => "D",
						'countrys' => [
							[
								'code' => "TL",
								'name' => "东帝汶",
								'ename' => "EAST TIMOR",
								'pinyin' => "ddw",
								'isHot' => 0,
								'localeName' => "东帝汶",
								'display' => "东帝汶(TL)"
							],
							[
								'code' => "TG",
								'name' => "多哥",
								'ename' => "TOGO",
								'pinyin' => "dg",
								'isHot' => 0,
								'localeName' => "多哥",
								'display' => "多哥(TG)"
							],
							[
								'code' => "DE",
								'name' => "德国",
								'ename' => "GERMANY",
								'pinyin' => "dg",
								'isHot' => 1,
								'localeName' => "德国",
								'display' => "德国(DE)"
							],
							[
								'code' => "DG",
								'name' => "迪戈加西亚岛",
								'ename' => "Diego Garcia",
								'pinyin' => "dgjxyd",
								'isHot' => 0,
								'localeName' => "迪戈加西亚岛",
								'display' => "迪戈加西亚岛(DG)"
							],
							[
								'code' => "DK",
								'name' => "丹麦",
								'ename' => "DENMARK",
								'pinyin' => "dm",
								'isHot' => 1,
								'localeName' => "丹麦",
								'display' => "丹麦(DK)"
							],
							[
								'code' => "DO",
								'name' => "多米尼加共合国",
								'ename' => "DOMINICAN REPUBLIC",
								'pinyin' => "dmnjghg",
								'isHot' => 0,
								'localeName' => "多米尼加共合国",
								'display' => "多米尼加共合国(DO)"
							],
							[
								'code' => "DM",
								'name' => "多米尼克",
								'ename' => "DOMINICA",
								'pinyin' => "dmnk",
								'isHot' => 0,
								'localeName' => "多米尼克",
								'display' => "多米尼克(DM)"
							]
						]
					],
					[
						'name' => "E",
						'countrys' => [
							[
								'code' => "EC",
								'name' => "厄瓜多尔",
								'ename' => "ECUADOR",
								'pinyin' => "egde",
								'isHot' => 0,
								'localeName' => "厄瓜多尔",
								'display' => "厄瓜多尔(EC)"
							],
							[
								'code' => "RU",
								'name' => "俄罗斯",
								'ename' => "RUSSIA",
								'pinyin' => "els",
								'isHot' => 0,
								'localeName' => "俄罗斯",
								'display' => "俄罗斯(RU)"
							],
							[
								'code' => "ER",
								'name' => "厄里特立亚",
								'ename' => "ERITREA",
								'pinyin' => "eltly",
								'isHot' => 0,
								'localeName' => "厄里特立亚",
								'display' => "厄里特立亚(ER)"
							]
						]
					],
					[
						'name' => "F",
						'countrys' => [
							[
								'code' => "VA",
								'name' => "梵蒂冈",
								'ename' => "VATICAN CITY",
								'pinyin' => "fdg",
								'isHot' => 0,
								'localeName' => "梵蒂冈",
								'display' => "梵蒂冈(VA)"
							],
							[
								'code' => "CV",
								'name' => "佛得角群岛",
								'ename' => "CAPE VERDE",
								'pinyin' => "fdjqd",
								'isHot' => 0,
								'localeName' => "佛得角群岛",
								'display' => "佛得角群岛(CV)"
							],
							[
								'code' => "FR",
								'name' => "法国",
								'ename' => "FRANCE",
								'pinyin' => "fg",
								'isHot' => 1,
								'localeName' => "法国",
								'display' => "法国(FR)"
							],
							[
								'code' => "FJ",
								'name' => "斐济",
								'ename' => "FIJI",
								'pinyin' => "fj",
								'isHot' => 0,
								'localeName' => "斐济",
								'display' => "斐济(FJ)"
							],
							[
								'code' => "FK",
								'name' => "福克兰群岛",
								'ename' => "FALKLAND ISLAND",
								'pinyin' => "fklqd",
								'isHot' => 0,
								'localeName' => "福克兰群岛",
								'display' => "福克兰群岛(FK)"
							],
							[
								'code' => "FI",
								'name' => "芬兰",
								'ename' => "FINLAND",
								'pinyin' => "fl",
								'isHot' => 0,
								'localeName' => "芬兰",
								'display' => "芬兰(FI)"
							],
							[
								'code' => "PH",
								'name' => "菲律宾",
								'ename' => "PHILIPPINES",
								'pinyin' => "flb",
								'isHot' => 0,
								'localeName' => "菲律宾",
								'display' => "菲律宾(PH)"
							],
							[
								'code' => "FO",
								'name' => "法罗群岛",
								'ename' => "FAROE ISLANDS",
								'pinyin' => "flqd",
								'isHot' => 0,
								'localeName' => "法罗群岛",
								'display' => "法罗群岛(FO)"
							],
							[
								'code' => "GF",
								'name' => "法属圭亚那",
								'ename' => "FRENCH GUIANA",
								'pinyin' => "fsgyn",
								'isHot' => 0,
								'localeName' => "法属圭亚那",
								'display' => "法属圭亚那(GF)"
							],
							[
								'code' => "FX",
								'name' => "法属美特罗波利坦",
								'ename' => "FRANCE, METROPOLITAN",
								'pinyin' => "fsmtlblt",
								'isHot' => 0,
								'localeName' => "法属美特罗波利坦",
								'display' => "法属美特罗波利坦(FX)"
							],
							[
								'code' => "TF",
								'name' => "法属南部领土",
								'ename' => "FRENCH SOUTHERN TERRITORIES",
								'pinyin' => "fsnblt",
								'isHot' => 0,
								'localeName' => "法属南部领土",
								'display' => "法属南部领土(TF)"
							]
						]
					]
				]
			],
			[
				'name' => "GHIJ",
				'country' => [
					[
						'name' => "G",
						'countrys' => [
							[
								'code' => "CU",
								'name' => "古巴",
								'ename' => "CUBA",
								'pinyin' => "gb",
								'isHot' => 0,
								'localeName' => "古巴",
								'display' => "古巴(CU)"
							],
							[
								'code' => "GM",
								'name' => "冈比亚",
								'ename' => "GAMBIA",
								'pinyin' => "gby",
								'isHot' => 0,
								'localeName' => "冈比亚",
								'display' => "冈比亚(GM)"
							],
							[
								'code' => "GU",
								'name' => "关岛",
								'ename' => "GUAM",
								'pinyin' => "gd",
								'isHot' => 0,
								'localeName' => "关岛",
								'display' => "关岛(GU)"
							],
							[
								'code' => "GP",
								'name' => "瓜德罗普",
								'ename' => "GUADELOUPE",
								'pinyin' => "gdlp",
								'isHot' => 0,
								'localeName' => "瓜德罗普",
								'display' => "瓜德罗普(GP)"
							],
							[
								'code' => "CG",
								'name' => "刚果",
								'ename' => "CONGO",
								'pinyin' => "gg",
								'isHot' => 0,
								'localeName' => "刚果",
								'display' => "刚果(CG)"
							],
							[
								'code' => "CD",
								'name' => "刚果民主共和国",
								'ename' => "CONGO REPUBLIC",
								'pinyin' => "ggmzghg",
								'isHot' => 0,
								'localeName' => "刚果民主共和国",
								'display' => "刚果民主共和国(CD)"
							],
							[
								'code' => "CO",
								'name' => "哥伦比亚",
								'ename' => "COLOMBIA",
								'pinyin' => "glby",
								'isHot' => 0,
								'localeName' => "哥伦比亚",
								'display' => "哥伦比亚(CO)"
							],
							[
								'code' => "GE",
								'name' => "格鲁吉亚",
								'ename' => "GEORGIA",
								'pinyin' => "gljy",
								'isHot' => 0,
								'localeName' => "格鲁吉亚",
								'display' => "格鲁吉亚(GE)"
							],
							[
								'code' => "GL",
								'name' => "格陵兰",
								'ename' => "GREENLAND",
								'pinyin' => "gll",
								'isHot' => 0,
								'localeName' => "格陵兰",
								'display' => "格陵兰(GL)"
							],
							[
								'code' => "GD",
								'name' => "格林纳达",
								'ename' => "GRENADA",
								'pinyin' => "glnd",
								'isHot' => 0,
								'localeName' => "格林纳达",
								'display' => "格林纳达(GD)"
							],
							[
								'code' => "CR",
								'name' => "哥斯达黎加",
								'ename' => "COSTA RICA",
								'pinyin' => "gsdlj",
								'isHot' => 0,
								'localeName' => "哥斯达黎加",
								'display' => "哥斯达黎加(CR)"
							],
							[
								'code' => "GG",
								'name' => "根西岛",
								'ename' => "GUERNSEY",
								'pinyin' => "gxd",
								'isHot' => 0,
								'localeName' => "根西岛",
								'display' => "根西岛(GG)"
							],
							[
								'code' => "GY",
								'name' => "圭亚那",
								'ename' => "GUYANA (BRITISH)",
								'pinyin' => "gyn",
								'isHot' => 0,
								'localeName' => "圭亚那",
								'display' => "圭亚那(GY)"
							]
						]
					],
					[
						'name' => "H",
						'countrys' => [
							[
								'code' => "HT",
								'name' => "海地",
								'ename' => "HAITI",
								'pinyin' => "hd",
								'isHot' => 0,
								'localeName' => "海地",
								'display' => "海地(HT)"
							],
							[
								'code' => "HM",
								'name' => "赫德岛和麦克唐纳岛",
								'ename' => "HEARD ISLAND AND MCDONALD ISLANDS",
								'pinyin' => "hddhmktnd",
								'isHot' => 0,
								'localeName' => "赫德岛和麦克唐纳岛",
								'display' => "赫德岛和麦克唐纳岛(HM)"
							],
							[
								'code' => "HN",
								'name' => "洪都拉斯",
								'ename' => "HONDURAS",
								'pinyin' => "hdls",
								'isHot' => 0,
								'localeName' => "洪都拉斯",
								'display' => "洪都拉斯(HN)"
							],
							[
								'code' => "KR",
								'name' => "韩国",
								'ename' => "SOUTH KOREA",
								'pinyin' => "hg",
								'isHot' => 0,
								'localeName' => "韩国",
								'display' => "韩国(KR)"
							],
							[
								'code' => "NL",
								'name' => "荷兰",
								'ename' => "NETHERLANDS",
								'pinyin' => "hl",
								'isHot' => 0,
								'localeName' => "荷兰",
								'display' => "荷兰(NL)"
							],
							[
								'code' => "AN",
								'name' => "荷属安的列斯群岛",
								'ename' => "NETHERLANDS ANTILLES",
								'pinyin' => "hsadlsqd",
								'isHot' => 0,
								'localeName' => "荷属安的列斯群岛",
								'display' => "荷属安的列斯群岛(AN)"
							],
							[
								'code' => "ME",
								'name' => "黑山共和国",
								'ename' => "MONTENEGRO",
								'pinyin' => "hsghg",
								'isHot' => 0,
								'localeName' => "黑山共和国",
								'display' => "黑山共和国(ME)"
							],
							[
								'code' => "KZ",
								'name' => "哈萨克斯坦",
								'ename' => "KAZAKHSTAN",
								'pinyin' => "hskst",
								'isHot' => 0,
								'localeName' => "哈萨克斯坦",
								'display' => "哈萨克斯坦(KZ)"
							],
							[
								'code' => "Y6",
								'name' => "惠州仓",
								'ename' => "HUIZHOU",
								'pinyin' => "hzc",
								'isHot' => 0,
								'localeName' => "惠州仓",
								'display' => "惠州仓(Y6)"
							]
						]
					],
					[
						'name' => "J",
						'countrys' => [
							[
								'code' => "ZW",
								'name' => "津巴布韦",
								'ename' => "ZIMBABWE",
								'pinyin' => "jbbw",
								'isHot' => 0,
								'localeName' => "津巴布韦",
								'display' => "津巴布韦(ZW)"
							],
							[
								'code' => "DJ",
								'name' => "吉布提",
								'ename' => "DJIBOUTI",
								'pinyin' => "jbt",
								'isHot' => 0,
								'localeName' => "吉布提",
								'display' => "吉布提(DJ)"
							],
							[
								'code' => "KG",
								'name' => "吉尔吉斯斯坦",
								'ename' => "KYRGYZSTAN",
								'pinyin' => "jejsst",
								'isHot' => 0,
								'localeName' => "吉尔吉斯斯坦",
								'display' => "吉尔吉斯斯坦(KG)"
							],
							[
								'code' => "CZ",
								'name' => "捷克共和国",
								'ename' => "CZECH REPUBLIC",
								'pinyin' => "jkghg",
								'isHot' => 0,
								'localeName' => "捷克共和国",
								'display' => "捷克共和国(CZ)"
							],
							[
								'code' => "BQ",
								'name' => "加勒比荷兰",
								'ename' => "Caribbean Netherlands",
								'pinyin' => "jlbhl",
								'isHot' => 0,
								'localeName' => "加勒比荷兰",
								'display' => "加勒比荷兰(BQ)"
							],
							[
								'code' => "KI",
								'name' => "基利巴斯共和国",
								'ename' => "KIRIBATI REPUBILC",
								'pinyin' => "jlbsghg",
								'isHot' => 0,
								'localeName' => "基利巴斯共和国",
								'display' => "基利巴斯共和国(KI)"
							],
							[
								'code' => "XK",
								'name' => "加罗林群岛",
								'ename' => "CAROLINE ISLANDS",
								'pinyin' => "jllqd",
								'isHot' => 0,
								'localeName' => "加罗林群岛",
								'display' => "加罗林群岛(XK)"
							],
							[
								'code' => "Y5",
								'name' => "江门仓",
								'ename' => "JIANGMEN",
								'pinyin' => "jmc",
								'isHot' => 0,
								'localeName' => "江门仓",
								'display' => "江门仓(Y5)"
							],
							[
								'code' => "GH",
								'name' => "加纳",
								'ename' => "GHANA",
								'pinyin' => "jn",
								'isHot' => 0,
								'localeName' => "加纳",
								'display' => "加纳(GH)"
							],
							[
								'code' => "CA",
								'name' => "加拿大",
								'ename' => "CANADA",
								'pinyin' => "jnd",
								'isHot' => 1,
								'localeName' => "加拿大",
								'display' => "加拿大(CA)"
							],
							[
								'code' => "IC",
								'name' => "加那利群岛",
								'ename' => "CANARY ISLANDS",
								'pinyin' => "jnlqd",
								'isHot' => 0,
								'localeName' => "加那利群岛",
								'display' => "加那利群岛(IC)"
							],
							[
								'code' => "GN",
								'name' => "几内亚",
								'ename' => "GUINEA",
								'pinyin' => "jny",
								'isHot' => 0,
								'localeName' => "几内亚",
								'display' => "几内亚(GN)"
							],
							[
								'code' => "GW",
								'name' => "几内亚比绍",
								'ename' => "GUINEA BISSAU",
								'pinyin' => "jnybs",
								'isHot' => 0,
								'localeName' => "几内亚比绍",
								'display' => "几内亚比绍(GW)"
							],
							[
								'code' => "GA",
								'name' => "加蓬",
								'ename' => "GABON",
								'pinyin' => "jp",
								'isHot' => 0,
								'localeName' => "加蓬",
								'display' => "加蓬(GA)"
							],
							[
								'code' => "KH",
								'name' => "柬埔寨",
								'ename' => "CAMBODIA",
								'pinyin' => "jpz",
								'isHot' => 0,
								'localeName' => "柬埔寨",
								'display' => "柬埔寨(KH)"
							]
						]
					]
				]
			],
			[
				'name' => "KLMNOP",
				'country' => [
					[
						'name' => "K",
						'countrys' => [
							[
								'code' => "CK",
								'name' => "库克群岛",
								'ename' => "COOK ISLANDS",
								'pinyin' => "kkqd",
								'isHot' => 0,
								'localeName' => "库克群岛",
								'display' => "库克群岛(CK)"
							],
							[
								'code' => "CC",
								'name' => "科科斯群岛",
								'ename' => "COCOS(KEELING)ISLANDS",
								'pinyin' => "kksqd",
								'isHot' => 0,
								'localeName' => "科科斯群岛",
								'display' => "科科斯群岛(CC)"
							],
							[
								'code' => "HR",
								'name' => "克罗地亚",
								'ename' => "CROATIA",
								'pinyin' => "kldy",
								'isHot' => 0,
								'localeName' => "克罗地亚",
								'display' => "克罗地亚(HR)"
							],
							[
								'code' => "XC",
								'name' => "库拉索岛(荷兰)",
								'ename' => "CURACAO",
								'pinyin' => "klsdhl",
								'isHot' => 0,
								'localeName' => "库拉索岛(荷兰)",
								'display' => "库拉索岛(荷兰)(XC)"
							],
							[
								'code' => "CM",
								'name' => "喀麦隆",
								'ename' => "CAMEROON",
								'pinyin' => "kml",
								'isHot' => 0,
								'localeName' => "喀麦隆",
								'display' => "喀麦隆(CM)"
							],
							[
								'code' => "KM",
								'name' => "科摩罗",
								'ename' => "COMOROS",
								'pinyin' => "kml",
								'isHot' => 0,
								'localeName' => "科摩罗",
								'display' => "科摩罗(KM)"
							],
							[
								'code' => "KY",
								'name' => "开曼群岛",
								'ename' => "CAYMAN ISLANDS",
								'pinyin' => "kmqd",
								'isHot' => 0,
								'localeName' => "开曼群岛",
								'display' => "开曼群岛(KY)"
							],
							[
								'code' => "KE",
								'name' => "肯尼亚",
								'ename' => "KENYA",
								'pinyin' => "kny",
								'isHot' => 0,
								'localeName' => "肯尼亚",
								'display' => "肯尼亚(KE)"
							],
							[
								'code' => "KV",
								'name' => "科索沃",
								'ename' => "KOSOVO",
								'pinyin' => "ksw",
								'isHot' => 0,
								'localeName' => "科索沃",
								'display' => "科索沃(KV)"
							],
							[
								'code' => "CI",
								'name' => "科特迪瓦(象牙海岸)",
								'ename' => "COTE D\'LVOIRE(IVORY)",
								'pinyin' => "ktdwxyha",
								'isHot' => 0,
								'localeName' => "科特迪瓦(象牙海岸)",
								'display' => "科特迪瓦(象牙海岸)(CI)"
							],
							[
								'code' => "QA",
								'name' => "卡塔尔",
								'ename' => "QATAR",
								'pinyin' => "kte",
								'isHot' => 0,
								'localeName' => "卡塔尔",
								'display' => "卡塔尔(QA)"
							],
							[
								'code' => "KW",
								'name' => "科威特",
								'ename' => "KUWAIT",
								'pinyin' => "kwt",
								'isHot' => 0,
								'localeName' => "科威特",
								'display' => "科威特(KW)"
							]
						]
					],
					[
						'name' => "L",
						'countrys' => [
							[
								'code' => "LR",
								'name' => "利比里亚",
								'ename' => "LIBERIA",
								'pinyin' => "lbly",
								'isHot' => 0,
								'localeName' => "利比里亚",
								'display' => "利比里亚(LR)"
							],
							[
								'code' => "LB",
								'name' => "黎巴嫩",
								'ename' => "LEBANON",
								'pinyin' => "lbn",
								'isHot' => 0,
								'localeName' => "黎巴嫩",
								'display' => "黎巴嫩(LB)"
							],
							[
								'code' => "LY",
								'name' => "利比亚",
								'ename' => "LIBYA",
								'pinyin' => "lby",
								'isHot' => 0,
								'localeName' => "利比亚",
								'display' => "利比亚(LY)"
							],
							[
								'code' => "RO",
								'name' => "罗马尼亚",
								'ename' => "ROMANIA",
								'pinyin' => "lmny",
								'isHot' => 0,
								'localeName' => "罗马尼亚",
								'display' => "罗马尼亚(RO)"
							],
							[
								'code' => "RE",
								'name' => "留尼汪岛",
								'ename' => "REUNION ISLAND",
								'pinyin' => "lnwd",
								'isHot' => 0,
								'localeName' => "留尼汪岛",
								'display' => "留尼汪岛(RE)"
							],
							[
								'code' => "LU",
								'name' => "卢森堡",
								'ename' => "LUXEMBOURG",
								'pinyin' => "lsb",
								'isHot' => 1,
								'localeName' => "卢森堡",
								'display' => "卢森堡(LU)"
							],
							[
								'code' => "LS",
								'name' => "莱索托",
								'ename' => "LESOTHO",
								'pinyin' => "lst",
								'isHot' => 0,
								'localeName' => "莱索托",
								'display' => "莱索托(LS)"
							],
							[
								'code' => "LT",
								'name' => "立陶宛",
								'ename' => "LITHUANIA",
								'pinyin' => "ltw",
								'isHot' => 0,
								'localeName' => "立陶宛",
								'display' => "立陶宛(LT)"
							],
							[
								'code' => "LV",
								'name' => "拉脱维亚",
								'ename' => "LATVIA",
								'pinyin' => "ltwy",
								'isHot' => 0,
								'localeName' => "拉脱维亚",
								'display' => "拉脱维亚(LV)"
							],
							[
								'code' => "LA",
								'name' => "老挝",
								'ename' => "LAOS",
								'pinyin' => "lw",
								'isHot' => 0,
								'localeName' => "老挝",
								'display' => "老挝(LA)"
							],
							[
								'code' => "RW",
								'name' => "卢旺达",
								'ename' => "RWANDA",
								'pinyin' => "lwd",
								'isHot' => 0,
								'localeName' => "卢旺达",
								'display' => "卢旺达(RW)"
							],
							[
								'code' => "LI",
								'name' => "列支敦士登",
								'ename' => "LIECHTENSTEIN",
								'pinyin' => "lzdsd",
								'isHot' => 0,
								'localeName' => "列支敦士登",
								'display' => "列支敦士登(LI)"
							]
						]
					],
					[
						'name' => "M",
						'countrys' => [
							[
								'code' => "MM",
								'name' => "缅甸",
								'ename' => "MYANMAR",
								'pinyin' => "md",
								'isHot' => 0,
								'localeName' => "缅甸",
								'display' => "缅甸(MM)"
							],
							[
								'code' => "MG",
								'name' => "马达加斯加",
								'ename' => "MADAGASCAR",
								'pinyin' => "mdjsj",
								'isHot' => 0,
								'localeName' => "马达加斯加",
								'display' => "马达加斯加(MG)"
							],
							[
								'code' => "XI",
								'name' => "马德拉岛",
								'ename' => "MADEIRA",
								'pinyin' => "mdld",
								'isHot' => 0,
								'localeName' => "马德拉岛",
								'display' => "马德拉岛(XI)"
							],
							[
								'code' => "MV",
								'name' => "马尔代夫",
								'ename' => "MALDIVES",
								'pinyin' => "medf",
								'isHot' => 0,
								'localeName' => "马尔代夫",
								'display' => "马尔代夫(MV)"
							],
							[
								'code' => "MD",
								'name' => "摩尔多瓦",
								'ename' => "MOLDOVA",
								'pinyin' => "medw",
								'isHot' => 0,
								'localeName' => "摩尔多瓦",
								'display' => "摩尔多瓦(MD)"
							],
							[
								'code' => "IM",
								'name' => "马恩岛(英)",
								'ename' => "Isle of Man",
								'pinyin' => "medy",
								'isHot' => 0,
								'localeName' => "马恩岛(英)",
								'display' => "马恩岛(英)(IM)"
							],
							[
								'code' => "MT",
								'name' => "马耳他",
								'ename' => "MALTA",
								'pinyin' => "met",
								'isHot' => 0,
								'localeName' => "马耳他",
								'display' => "马耳他(MT)"
							],
							[
								'code' => "US",
								'name' => "美国",
								'ename' => "UNITED STATES",
								'pinyin' => "mg",
								'isHot' => 1,
								'localeName' => "美国",
								'display' => "美国(US)"
							],
							[
								'code' => "MN",
								'name' => "蒙古",
								'ename' => "MONGOLIA",
								'pinyin' => "mg",
								'isHot' => 0,
								'localeName' => "蒙古",
								'display' => "蒙古(MN)"
							],
							[
								'code' => "UM",
								'name' => "美国本土外小岛屿",
								'ename' => "UNITED STATES MINOR OUTLYING ISLANDS",
								'pinyin' => "mgbtwxdy",
								'isHot' => 0,
								'localeName' => "美国本土外小岛屿",
								'display' => "美国本土外小岛屿(UM)"
							],
							[
								'code' => "BD",
								'name' => "孟加拉国",
								'ename' => "BANGLADESH",
								'pinyin' => "mjlg",
								'isHot' => 0,
								'localeName' => "孟加拉国",
								'display' => "孟加拉国(BD)"
							],
							[
								'code' => "FM",
								'name' => "密克罗尼西亚",
								'ename' => "MICRONESIA",
								'pinyin' => "mklnxy",
								'isHot' => 0,
								'localeName' => "密克罗尼西亚",
								'display' => "密克罗尼西亚(FM)"
							],
							[
								'code' => "PE",
								'name' => "秘鲁",
								'ename' => "PERU",
								'pinyin' => "ml",
								'isHot' => 0,
								'localeName' => "秘鲁",
								'display' => "秘鲁(PE)"
							],
							[
								'code' => "ML",
								'name' => "马里",
								'ename' => "MALI",
								'pinyin' => "ml",
								'isHot' => 0,
								'localeName' => "马里",
								'display' => "马里(ML)"
							],
							[
								'code' => "MA",
								'name' => "摩洛哥",
								'ename' => "MOROCCO",
								'pinyin' => "mlg",
								'isHot' => 0,
								'localeName' => "摩洛哥",
								'display' => "摩洛哥(MA)"
							],
							[
								'code' => "MU",
								'name' => "毛里求斯",
								'ename' => "MAURITIUS",
								'pinyin' => "mlqs",
								'isHot' => 0,
								'localeName' => "毛里求斯",
								'display' => "毛里求斯(MU)"
							],
							[
								'code' => "MR",
								'name' => "毛里塔尼亚",
								'ename' => "MAURITANIA",
								'pinyin' => "mltny",
								'isHot' => 0,
								'localeName' => "毛里塔尼亚",
								'display' => "毛里塔尼亚(MR)"
							],
							[
								'code' => "MW",
								'name' => "马拉维",
								'ename' => "MALAWI",
								'pinyin' => "mlw",
								'isHot' => 0,
								'localeName' => "马拉维",
								'display' => "马拉维(MW)"
							],
							[
								'code' => "MY",
								'name' => "马来西亚",
								'ename' => "MALAYSIA",
								'pinyin' => "mlxy",
								'isHot' => 0,
								'localeName' => "马来西亚",
								'display' => "马来西亚(MY)"
							],
							[
								'code' => "MC",
								'name' => "摩纳哥",
								'ename' => "MONACO",
								'pinyin' => "mng",
								'isHot' => 0,
								'localeName' => "摩纳哥",
								'display' => "摩纳哥(MC)"
							],
							[
								'code' => "MK",
								'name' => "马其顿",
								'ename' => "MACEDONIA",
								'pinyin' => "mqd",
								'isHot' => 0,
								'localeName' => "马其顿",
								'display' => "马其顿(MK)"
							],
							[
								'code' => "MZ",
								'name' => "莫桑比克",
								'ename' => "MOZAMBIQUE",
								'pinyin' => "msbk",
								'isHot' => 0,
								'localeName' => "莫桑比克",
								'display' => "莫桑比克(MZ)"
							],
							[
								'code' => "MH",
								'name' => "马绍尔群岛",
								'ename' => "MARSHALL ISLANDS",
								'pinyin' => "mseqd",
								'isHot' => 0,
								'localeName' => "马绍尔群岛",
								'display' => "马绍尔群岛(MH)"
							],
							[
								'code' => "AS",
								'name' => "美属萨摩亚群岛",
								'ename' => "AMERICAN SAMOA",
								'pinyin' => "mssmyqd",
								'isHot' => 0,
								'localeName' => "美属萨摩亚群岛",
								'display' => "美属萨摩亚群岛(AS)"
							],
							[
								'code' => "VI",
								'name' => "美属维尔京群岛",
								'ename' => "VIRGIN ISLAND (US)",
								'pinyin' => "mswejqd",
								'isHot' => 0,
								'localeName' => "美属维尔京群岛",
								'display' => "美属维尔京群岛(VI)"
							],
							[
								'code' => "MQ",
								'name' => "马提尼克岛",
								'ename' => "MARTINIQUE",
								'pinyin' => "mtnkd",
								'isHot' => 0,
								'localeName' => "马提尼克岛",
								'display' => "马提尼克岛(MQ)"
							],
							[
								'code' => "MS",
								'name' => "蒙特塞拉岛",
								'ename' => "MONTSERRAT",
								'pinyin' => "mtsld",
								'isHot' => 0,
								'localeName' => "蒙特塞拉岛",
								'display' => "蒙特塞拉岛(MS)"
							],
							[
								'code' => "MX",
								'name' => "墨西哥",
								'ename' => "MEXICO",
								'pinyin' => "mxg",
								'isHot' => 0,
								'localeName' => "墨西哥",
								'display' => "墨西哥(MX)"
							],
							[
								'code' => "YT",
								'name' => "马约特",
								'ename' => "MAYOTTE",
								'pinyin' => "myt",
								'isHot' => 0,
								'localeName' => "马约特",
								'display' => "马约特(YT)"
							]
						]
					],
					[
						'name' => "N",
						'countrys' => [
							[
								'code' => "NU",
								'name' => "纽埃岛",
								'ename' => "NIUE",
								'pinyin' => "nad",
								'isHot' => 0,
								'localeName' => "纽埃岛",
								'display' => "纽埃岛(NU)"
							],
							[
								'code' => "NP",
								'name' => "尼泊尔",
								'ename' => "NEPAL",
								'pinyin' => "nbe",
								'isHot' => 0,
								'localeName' => "尼泊尔",
								'display' => "尼泊尔(NP)"
							],
							[
								'code' => "ZA",
								'name' => "南非",
								'ename' => "SOUTH AFRICA",
								'pinyin' => "nf",
								'isHot' => 0,
								'localeName' => "南非",
								'display' => "南非(ZA)"
							],
							[
								'code' => "NF",
								'name' => "诺褔克岛",
								'ename' => "NORFOLK ISLAND",
								'pinyin' => "nfkd",
								'isHot' => 0,
								'localeName' => "诺褔克岛",
								'display' => "诺褔克岛(NF)"
							],
							[
								'code' => "AQ",
								'name' => "南极",
								'ename' => "Antarctica",
								'pinyin' => "nj",
								'isHot' => 0,
								'localeName' => "南极",
								'display' => "南极(AQ)"
							],
							[
								'code' => "NI",
								'name' => "尼加拉瓜",
								'ename' => "NICARAGUA",
								'pinyin' => "njlg",
								'isHot' => 0,
								'localeName' => "尼加拉瓜",
								'display' => "尼加拉瓜(NI)"
							],
							[
								'code' => "NR",
								'name' => "瑙鲁共和国",
								'ename' => "NAURU REPUBLIC",
								'pinyin' => "nlghg",
								'isHot' => 0,
								'localeName' => "瑙鲁共和国",
								'display' => "瑙鲁共和国(NR)"
							],
							[
								'code' => "NA",
								'name' => "纳米比亚",
								'ename' => "NAMIBIA",
								'pinyin' => "nmby",
								'isHot' => 0,
								'localeName' => "纳米比亚",
								'display' => "纳米比亚(NA)"
							],
							[
								'code' => "GS",
								'name' => "南乔治亚岛和南桑威奇群岛",
								'ename' => "SOUTH GEORGIA AND THE SOUTH SANDWICH ISL",
								'pinyin' => "nqzydhnswqqd",
								'isHot' => 0,
								'localeName' => "南乔治亚岛和南桑威奇群岛",
								'display' => "南乔治亚岛和南桑威奇群岛(GS)"
							],
							[
								'code' => "NE",
								'name' => "尼日尔",
								'ename' => "NIGER",
								'pinyin' => "nre",
								'isHot' => 0,
								'localeName' => "尼日尔",
								'display' => "尼日尔(NE)"
							],
							[
								'code' => "NG",
								'name' => "尼日利亚",
								'ename' => "NIGERIA",
								'pinyin' => "nrly",
								'isHot' => 0,
								'localeName' => "尼日利亚",
								'display' => "尼日利亚(NG)"
							],
							[
								'code' => "SS",
								'name' => "南苏丹共和国",
								'ename' => "SOUTH SUDAN",
								'pinyin' => "nsdghg",
								'isHot' => 0,
								'localeName' => "南苏丹共和国",
								'display' => "南苏丹共和国(SS)"
							],
							[
								'code' => "JU",
								'name' => "南斯拉夫",
								'ename' => "YUGOSLAVIA",
								'pinyin' => "nslf",
								'isHot' => 0,
								'localeName' => "南斯拉夫",
								'display' => "南斯拉夫(JU)"
							],
							[
								'code' => "NO",
								'name' => "挪威",
								'ename' => "NORWAY",
								'pinyin' => "nw",
								'isHot' => 0,
								'localeName' => "挪威",
								'display' => "挪威(NO)"
							],
							[
								'code' => "XN",
								'name' => "尼维斯岛",
								'ename' => "NEVIS",
								'pinyin' => "nwsd",
								'isHot' => 0,
								'localeName' => "尼维斯岛",
								'display' => "尼维斯岛(XN)"
							]
						]
					],
					[
						'name' => "P",
						'countrys' => [
							[
								'code' => "PW",
								'name' => "帕劳",
								'ename' => "PALAU",
								'pinyin' => "pl",
								'isHot' => 0,
								'localeName' => "帕劳",
								'display' => "帕劳(PW)"
							],
							[
								'code' => "Y7",
								'name' => "莆田仓",
								'ename' => "PUTIAN",
								'pinyin' => "ptc",
								'isHot' => 0,
								'localeName' => "莆田仓",
								'display' => "莆田仓(Y7)"
							],
							[
								'code' => "PN",
								'name' => "皮特凯恩群岛",
								'ename' => "PITCAIRN ISLANDS",
								'pinyin' => "ptkeqd",
								'isHot' => 0,
								'localeName' => "皮特凯恩群岛",
								'display' => "皮特凯恩群岛(PN)"
							],
							[
								'code' => "PT",
								'name' => "葡萄牙",
								'ename' => "PORTUGAL",
								'pinyin' => "pty",
								'isHot' => 0,
								'localeName' => "葡萄牙",
								'display' => "葡萄牙(PT)"
							]
						]
					]
				]
			],
			[
				'name' => "QRSTUV",
				'country' => [
					[
						'name' => "R",
						'countrys' => [
							[
								'code' => "JP",
								'name' => "日本",
								'ename' => "JAPAN",
								'pinyin' => "rb",
								'isHot' => 0,
								'localeName' => "日本",
								'display' => "日本(JP)"
							],
							[
								'code' => "SE",
								'name' => "瑞典",
								'ename' => "SWEDEN",
								'pinyin' => "rd",
								'isHot' => 1,
								'localeName' => "瑞典",
								'display' => "瑞典(SE)"
							],
							[
								'code' => "CH",
								'name' => "瑞士",
								'ename' => "SWITZERLAND",
								'pinyin' => "rs",
								'isHot' => 0,
								'localeName' => "瑞士",
								'display' => "瑞士(CH)"
							]
						]
					],
					[
						'name' => "S",
						'countrys' => [
							[
								'code' => "MP",
								'name' => "塞班岛",
								'ename' => "SAIPAN",
								'pinyin' => "sbd",
								'isHot' => 0,
								'localeName' => "塞班岛",
								'display' => "塞班岛(MP)"
							],
							[
								'code' => "BL",
								'name' => "圣巴特勒米",
								'ename' => "Saint Barthelemy",
								'pinyin' => "sbtlm",
								'isHot' => 0,
								'localeName' => "圣巴特勒米",
								'display' => "圣巴特勒米(BL)"
							],
							[
								'code' => "XY",
								'name' => "圣巴特勒米岛",
								'ename' => "ST. BARTHELEMY",
								'pinyin' => "sbtlmd",
								'isHot' => 0,
								'localeName' => "圣巴特勒米岛",
								'display' => "圣巴特勒米岛(XY)"
							],
							[
								'code' => "SD",
								'name' => "苏丹",
								'ename' => "SUDAN",
								'pinyin' => "sd",
								'isHot' => 0,
								'localeName' => "苏丹",
								'display' => "苏丹(SD)"
							],
							[
								'code' => "CX",
								'name' => "圣诞岛",
								'ename' => "CHRISTMAS ISLAND",
								'pinyin' => "sdd",
								'isHot' => 0,
								'localeName' => "圣诞岛",
								'display' => "圣诞岛(CX)"
							],
							[
								'code' => "ST",
								'name' => "圣多美和普林西比",
								'ename' => "SAO TOME AND PRINCIPE",
								'pinyin' => "sdmhplxb",
								'isHot' => 0,
								'localeName' => "圣多美和普林西比",
								'display' => "圣多美和普林西比(ST)"
							],
							[
								'code' => "SV",
								'name' => "萨尔瓦多",
								'ename' => "EL SALVADOR",
								'pinyin' => "sewd",
								'isHot' => 0,
								'localeName' => "萨尔瓦多",
								'display' => "萨尔瓦多(SV)"
							],
							[
								'code' => "RS",
								'name' => "塞尔维亚共和国",
								'ename' => "SERBIA, REPUBLIC OF",
								'pinyin' => "sewyghg",
								'isHot' => 0,
								'localeName' => "塞尔维亚共和国",
								'display' => "塞尔维亚共和国(RS)"
							],
							[
								'code' => "SH",
								'name' => "圣赫勒拿岛",
								'ename' => "ST HELENA",
								'pinyin' => "shlnd",
								'isHot' => 0,
								'localeName' => "圣赫勒拿岛",
								'display' => "圣赫勒拿岛(SH)"
							],
							[
								'code' => "XZ",
								'name' => "桑给巴尔",
								'ename' => "Zanzibar",
								'pinyin' => "sjbe",
								'isHot' => 0,
								'localeName' => "桑给巴尔",
								'display' => "桑给巴尔(XZ)"
							],
							[
								'code' => "KN",
								'name' => "圣基茨",
								'ename' => "SAINT KITTS",
								'pinyin' => "sjc",
								'isHot' => 0,
								'localeName' => "圣基茨",
								'display' => "圣基茨(KN)"
							],
							[
								'code' => "SK",
								'name' => "斯洛伐克共和国",
								'ename' => "SLOVAKIA REPUBLIC",
								'pinyin' => "slfkghg",
								'isHot' => 0,
								'localeName' => "斯洛伐克共和国",
								'display' => "斯洛伐克共和国(SK)"
							],
							[
								'code' => "SL",
								'name' => "塞拉里昂",
								'ename' => "SIERRA LEONE",
								'pinyin' => "slla",
								'isHot' => 0,
								'localeName' => "塞拉里昂",
								'display' => "塞拉里昂(SL)"
							],
							[
								'code' => "LK",
								'name' => "斯里兰卡",
								'ename' => "SRI LANKA",
								'pinyin' => "sllk",
								'isHot' => 0,
								'localeName' => "斯里兰卡",
								'display' => "斯里兰卡(LK)"
							],
							[
								'code' => "SB",
								'name' => "所罗门群岛",
								'ename' => "SOLOMON ISLANDS",
								'pinyin' => "slmqd",
								'isHot' => 0,
								'localeName' => "所罗门群岛",
								'display' => "所罗门群岛(SB)"
							],
							[
								'code' => "SR",
								'name' => "苏里南",
								'ename' => "SURINAME",
								'pinyin' => "sln",
								'isHot' => 0,
								'localeName' => "苏里南",
								'display' => "苏里南(SR)"
							],
							[
								'code' => "SI",
								'name' => "斯洛文尼亚",
								'ename' => "SLOVENIA",
								'pinyin' => "slwny",
								'isHot' => 0,
								'localeName' => "斯洛文尼亚",
								'display' => "斯洛文尼亚(SI)"
							],
							[
								'code' => "LC",
								'name' => "圣卢西亚",
								'ename' => "ST. LUCIA",
								'pinyin' => "slxy",
								'isHot' => 0,
								'localeName' => "圣卢西亚",
								'display' => "圣卢西亚(LC)"
							],
							[
								'code' => "MF",
								'name' => "圣马丁(法属)",
								'ename' => "Saint Martin（France）",
								'pinyin' => "smdh",
								'isHot' => 0,
								'localeName' => "圣马丁(法属)",
								'display' => "圣马丁(法属)(MF)"
							],
							[
								'code' => "SO",
								'name' => "索马里",
								'ename' => "SOMALIA",
								'pinyin' => "sml",
								'isHot' => 0,
								'localeName' => "索马里",
								'display' => "索马里(SO)"
							],
							[
								'code' => "XS",
								'name' => "索马里共和国",
								'ename' => "SOMALILAND",
								'pinyin' => "smlghg",
								'isHot' => 0,
								'localeName' => "索马里共和国",
								'display' => "索马里共和国(XS)"
							],
							[
								'code' => "SM",
								'name' => "圣马力诺",
								'ename' => "SAN MARINO",
								'pinyin' => "smln",
								'isHot' => 0,
								'localeName' => "圣马力诺",
								'display' => "圣马力诺(SM)"
							],
							[
								'code' => "XM",
								'name' => "圣马丁(荷属)",
								'ename' => "Sint Maarten (Netherlands)",
								'pinyin' => "smtd",
								'isHot' => 0,
								'localeName' => "圣马丁(荷属)",
								'display' => "圣马丁(荷属)(XM)"
							],
							[
								'code' => "SN",
								'name' => "塞内加尔",
								'ename' => "SENEGAL",
								'pinyin' => "snje",
								'isHot' => 0,
								'localeName' => "塞内加尔",
								'display' => "塞内加尔(SN)"
							],
							[
								'code' => "PM",
								'name' => "圣皮埃尔和密克隆群岛",
								'ename' => "SAINT PIERRE AND MIQUELON",
								'pinyin' => "spaehmklqd",
								'isHot' => 0,
								'localeName' => "圣皮埃尔和密克隆群岛",
								'display' => "圣皮埃尔和密克隆群岛(PM)"
							],
							[
								'code' => "CY",
								'name' => "塞浦路斯",
								'ename' => "CYPRUS",
								'pinyin' => "spls",
								'isHot' => 0,
								'localeName' => "塞浦路斯",
								'display' => "塞浦路斯(CY)"
							],
							[
								'code' => "SC",
								'name' => "塞舌尔",
								'ename' => "SEYCHELLES",
								'pinyin' => "sse",
								'isHot' => 0,
								'localeName' => "塞舌尔",
								'display' => "塞舌尔(SC)"
							],
							[
								'code' => "SA",
								'name' => "沙特阿拉伯",
								'ename' => "SAUDI ARABIA",
								'pinyin' => "stalb",
								'isHot' => 0,
								'localeName' => "沙特阿拉伯",
								'display' => "沙特阿拉伯(SA)"
							],
							[
								'code' => "SJ",
								'name' => "斯瓦尔巴岛和扬马延岛",
								'ename' => "SVALBARD AND JAN MAYEN",
								'pinyin' => "swebdhymyd",
								'isHot' => 0,
								'localeName' => "斯瓦尔巴岛和扬马延岛",
								'display' => "斯瓦尔巴岛和扬马延岛(SJ)"
							],
							[
								'code' => "SZ",
								'name' => "斯威士兰",
								'ename' => "SWAZILAND",
								'pinyin' => "swsl",
								'isHot' => 0,
								'localeName' => "斯威士兰",
								'display' => "斯威士兰(SZ)"
							],
							[
								'code' => "VC",
								'name' => "圣文森特和格林纳丁斯岛",
								'ename' => "SAINT VINCENT AND THE GRENADINES",
								'pinyin' => "swsthglndsd",
								'isHot' => 0,
								'localeName' => "圣文森特和格林纳丁斯岛",
								'display' => "圣文森特和格林纳丁斯岛(VC)"
							],
							[
								'code' => "XE",
								'name' => "圣尤斯塔提马斯岛",
								'ename' => "ST. EUSTATIUS",
								'pinyin' => "systtmsd",
								'isHot' => 0,
								'localeName' => "圣尤斯塔提马斯岛",
								'display' => "圣尤斯塔提马斯岛(XE)"
							]
						]
					],
					[
						'name' => "T",
						'countrys' => [
							[
								'code' => "TR",
								'name' => "土耳其",
								'ename' => "TURKEY",
								'pinyin' => "teq",
								'isHot' => 0,
								'localeName' => "土耳其",
								'display' => "土耳其(TR)"
							],
							[
								'code' => "TH",
								'name' => "泰国",
								'ename' => "THAILAND",
								'pinyin' => "tg",
								'isHot' => 0,
								'localeName' => "泰国",
								'display' => "泰国(TH)"
							],
							[
								'code' => "TO",
								'name' => "汤加",
								'ename' => "TONGA",
								'pinyin' => "tj",
								'isHot' => 0,
								'localeName' => "汤加",
								'display' => "汤加(TO)"
							],
							[
								'code' => "TJ",
								'name' => "塔吉克斯坦",
								'ename' => "TAJIKISTAN",
								'pinyin' => "tjkst",
								'isHot' => 0,
								'localeName' => "塔吉克斯坦",
								'display' => "塔吉克斯坦(TJ)"
							],
							[
								'code' => "TK",
								'name' => "托克劳",
								'ename' => "TOKELAU",
								'pinyin' => "tkl",
								'isHot' => 0,
								'localeName' => "托克劳",
								'display' => "托克劳(TK)"
							],
							[
								'code' => "TM",
								'name' => "土库曼斯坦",
								'ename' => "TURKMENISTAN",
								'pinyin' => "tkmst",
								'isHot' => 0,
								'localeName' => "土库曼斯坦",
								'display' => "土库曼斯坦(TM)"
							],
							[
								'code' => "TC",
								'name' => "特克斯和凯科斯群岛",
								'ename' => "TURKS AND CAICOS ISLANDS",
								'pinyin' => "tkshkksqd",
								'isHot' => 0,
								'localeName' => "特克斯和凯科斯群岛",
								'display' => "特克斯和凯科斯群岛(TC)"
							],
							[
								'code' => "TT",
								'name' => "特立尼达和多巴哥",
								'ename' => "TRINIDAD AND TOBAGO",
								'pinyin' => "tlndhdbg",
								'isHot' => 0,
								'localeName' => "特立尼达和多巴哥",
								'display' => "特立尼达和多巴哥(TT)"
							],
							[
								'code' => "TA",
								'name' => "特里斯坦",
								'ename' => "TRISTAN DA CUNBA",
								'pinyin' => "tlst",
								'isHot' => 0,
								'localeName' => "特里斯坦",
								'display' => "特里斯坦(TA)"
							],
							[
								'code' => "TN",
								'name' => "突尼斯",
								'ename' => "TUNISIA",
								'pinyin' => "tns",
								'isHot' => 0,
								'localeName' => "突尼斯",
								'display' => "突尼斯(TN)"
							],
							[
								'code' => "TZ",
								'name' => "坦桑尼亚",
								'ename' => "TANZANIA",
								'pinyin' => "tsny",
								'isHot' => 0,
								'localeName' => "坦桑尼亚",
								'display' => "坦桑尼亚(TZ)"
							],
							[
								'code' => "TW",
								'name' => "中国 --- 台湾省",
								'ename' => "CHINA-TAIWAN",
								'pinyin' => "tw",
								'isHot' => 0,
								'localeName' => "中国 --- 台湾省",
								'display' => "中国 --- 台湾省(TW)"
							],
							[
								'code' => "TV",
								'name' => "图瓦卢",
								'ename' => "TUVALU",
								'pinyin' => "twl",
								'isHot' => 0,
								'localeName' => "图瓦卢",
								'display' => "图瓦卢(TV)"
							],
							[
								'code' => "PF",
								'name' => "塔希堤岛(法属波利尼西亚)",
								'ename' => "FRENCH POLYNESIA",
								'pinyin' => "txddfsblnxy",
								'isHot' => 0,
								'localeName' => "塔希堤岛(法属波利尼西亚)",
								'display' => "塔希堤岛(法属波利尼西亚)(PF)"
							]
						]
					]
				]
			],
			[
				'name' => "WXYZ",
				'country' => [
					[
						'name' => "W",
						'countrys' => [
							[
								'code' => "GT",
								'name' => "危地马拉",
								'ename' => "GUATEMALA",
								'pinyin' => "wdml",
								'isHot' => 0,
								'localeName' => "危地马拉",
								'display' => "危地马拉(GT)"
							],
							[
								'code' => "UG",
								'name' => "乌干达",
								'ename' => "UGANDA",
								'pinyin' => "wgd",
								'isHot' => 0,
								'localeName' => "乌干达",
								'display' => "乌干达(UG)"
							],
							[
								'code' => "UA",
								'name' => "乌克兰",
								'ename' => "UKRAINE",
								'pinyin' => "wkl",
								'isHot' => 0,
								'localeName' => "乌克兰",
								'display' => "乌克兰(UA)"
							],
							[
								'code' => "BN",
								'name' => "文莱",
								'ename' => "BRUNEI",
								'pinyin' => "wl",
								'isHot' => 0,
								'localeName' => "文莱",
								'display' => "文莱(BN)"
							],
							[
								'code' => "UY",
								'name' => "乌拉圭",
								'ename' => "URUGUAY",
								'pinyin' => "wlg",
								'isHot' => 0,
								'localeName' => "乌拉圭",
								'display' => "乌拉圭(UY)"
							],
							[
								'code' => "WF",
								'name' => "瓦利斯群岛和富图纳群岛",
								'ename' => "WALLIS AND FUTUNA ISLANDS",
								'pinyin' => "wlsqdhftnqd",
								'isHot' => 0,
								'localeName' => "瓦利斯群岛和富图纳群岛",
								'display' => "瓦利斯群岛和富图纳群岛(WF)"
							],
							[
								'code' => "VU",
								'name' => "瓦努阿图",
								'ename' => "VANUATU",
								'pinyin' => "wnat",
								'isHot' => 0,
								'localeName' => "瓦努阿图",
								'display' => "瓦努阿图(VU)"
							],
							[
								'code' => "VE",
								'name' => "委内瑞拉",
								'ename' => "VENEZUELA",
								'pinyin' => "wnrl",
								'isHot' => 0,
								'localeName' => "委内瑞拉",
								'display' => "委内瑞拉(VE)"
							],
							[
								'code' => "UZ",
								'name' => "乌兹别克斯坦",
								'ename' => "UZBEKISTAN",
								'pinyin' => "wzbkst",
								'isHot' => 0,
								'localeName' => "乌兹别克斯坦",
								'display' => "乌兹别克斯坦(UZ)"
							]
						]
					],
					[
						'name' => "X",
						'countrys' => [
							[
								'code' => "ES",
								'name' => "西班牙",
								'ename' => "SPAIN",
								'pinyin' => "xby",
								'isHot' => 1,
								'localeName' => "西班牙",
								'display' => "西班牙(ES)"
							],
							[
								'code' => "HK",
								'name' => "中国 --- 香港特别行政区",
								'ename' => "CHINA-HONG KONG",
								'pinyin' => "xg",
								'isHot' => 0,
								'localeName' => "中国 --- 香港特别行政区",
								'display' => "中国 --- 香港特别行政区(HK)"
							],
							[
								'code' => "Y1",
								'name' => "香港仓",
								'ename' => "HONGKONG",
								'pinyin' => "xgc",
								'isHot' => 0,
								'localeName' => "香港仓",
								'display' => "香港仓(Y1)"
							],
							[
								'code' => "SG",
								'name' => "新加坡",
								'ename' => "SINGAPORE",
								'pinyin' => "xjp",
								'isHot' => 0,
								'localeName' => "新加坡",
								'display' => "新加坡(SG)"
							],
							[
								'code' => "NC",
								'name' => "新喀里多尼亚",
								'ename' => "NEW CALEDONIA",
								'pinyin' => "xkldny",
								'isHot' => 0,
								'localeName' => "新喀里多尼亚",
								'display' => "新喀里多尼亚(NC)"
							],
							[
								'code' => "GR",
								'name' => "希腊",
								'ename' => "GREECE",
								'pinyin' => "xl",
								'isHot' => 0,
								'localeName' => "希腊",
								'display' => "希腊(GR)"
							],
							[
								'code' => "SY",
								'name' => "叙利亚",
								'ename' => "SYRIA",
								'pinyin' => "xly",
								'isHot' => 0,
								'localeName' => "叙利亚",
								'display' => "叙利亚(SY)"
							],
							[
								'code' => "EH",
								'name' => "西撒哈拉",
								'ename' => "WESTERN SAHARA",
								'pinyin' => "xshl",
								'isHot' => 0,
								'localeName' => "西撒哈拉",
								'display' => "西撒哈拉(EH)"
							],
							[
								'code' => "WS",
								'name' => "西萨摩亚",
								'ename' => "WESTERN SAMOA",
								'pinyin' => "xsmy",
								'isHot' => 0,
								'localeName' => "西萨摩亚",
								'display' => "西萨摩亚(WS)"
							],
							[
								'code' => "NZ",
								'name' => "新西兰",
								'ename' => "NEW ZEALAND",
								'pinyin' => "xxl",
								'isHot' => 0,
								'localeName' => "新西兰",
								'display' => "新西兰(NZ)"
							],
							[
								'code' => "HU",
								'name' => "匈牙利",
								'ename' => "HUNGARY",
								'pinyin' => "xyl",
								'isHot' => 0,
								'localeName' => "匈牙利",
								'display' => "匈牙利(HU)"
							]
						]
					],
					[
						'name' => "Y",
						'countrys' => [
							[
								'code' => "JO",
								'name' => "约旦",
								'ename' => "JORDAN",
								'pinyin' => "yd",
								'isHot' => 0,
								'localeName' => "约旦",
								'display' => "约旦(JO)"
							],
							[
								'code' => "IN",
								'name' => "印度",
								'ename' => "INDIA",
								'pinyin' => "yd",
								'isHot' => 0,
								'localeName' => "印度",
								'display' => "印度(IN)"
							],
							[
								'code' => "IT",
								'name' => "意大利",
								'ename' => "ITALY",
								'pinyin' => "ydl",
								'isHot' => 1,
								'localeName' => "意大利",
								'display' => "意大利(IT)"
							],
							[
								'code' => "ID",
								'name' => "印度尼西亚",
								'ename' => "INDONESIA",
								'pinyin' => "ydnxy",
								'isHot' => 0,
								'localeName' => "印度尼西亚",
								'display' => "印度尼西亚(ID)"
							],
							[
								'code' => "GB",
								'name' => "英国",
								'ename' => "UNITED KINGDOM",
								'pinyin' => "yg",
								'isHot' => 1,
								'localeName' => "英国",
								'display' => "英国(GB)"
							],
							[
								'code' => "IR",
								'name' => "伊朗",
								'ename' => "IRAN (ISLAMIC REPUBLIC OF)",
								'pinyin' => "yl",
								'isHot' => 0,
								'localeName' => "伊朗",
								'display' => "伊朗(IR)"
							],
							[
								'code' => "IQ",
								'name' => "伊拉克",
								'ename' => "IRAQ",
								'pinyin' => "ylk",
								'isHot' => 0,
								'localeName' => "伊拉克",
								'display' => "伊拉克(IQ)"
							],
							[
								'code' => "YE",
								'name' => "也门阿拉伯共合国",
								'ename' => "YEMEN, REPUBLIC OF",
								'pinyin' => "ymalbghg",
								'isHot' => 0,
								'localeName' => "也门阿拉伯共合国",
								'display' => "也门阿拉伯共合国(YE)"
							],
							[
								'code' => "JM",
								'name' => "牙买加",
								'ename' => "JAMAICA",
								'pinyin' => "ymj",
								'isHot' => 0,
								'localeName' => "牙买加",
								'display' => "牙买加(JM)"
							],
							[
								'code' => "AM",
								'name' => "亚美尼亚",
								'ename' => "ARMENIA",
								'pinyin' => "ymny",
								'isHot' => 0,
								'localeName' => "亚美尼亚",
								'display' => "亚美尼亚(AM)"
							],
							[
								'code' => "VN",
								'name' => "越南",
								'ename' => "VIETNAM",
								'pinyin' => "yn",
								'isHot' => 0,
								'localeName' => "越南",
								'display' => "越南(VN)"
							],
							[
								'code' => "XH",
								'name' => "亚速尔群岛",
								'ename' => "AZORES",
								'pinyin' => "yseqd",
								'isHot' => 0,
								'localeName' => "亚速尔群岛",
								'display' => "亚速尔群岛(XH)"
							],
							[
								'code' => "IL",
								'name' => "以色列",
								'ename' => "ISRAEL",
								'pinyin' => "ysl",
								'isHot' => 1,
								'localeName' => "以色列",
								'display' => "以色列(IL)"
							],
							[
								'code' => "VG",
								'name' => "英属维尔京群岛",
								'ename' => "VIRGIN ISLAND (GB)",
								'pinyin' => "yswejqd",
								'isHot' => 0,
								'localeName' => "英属维尔京群岛",
								'display' => "英属维尔京群岛(VG)"
							],
							[
								'code' => "IO",
								'name' => "英属印度洋地区(查各群岛)",
								'ename' => "BRITISH INDIAN OCEAN TERRITORY",
								'pinyin' => "ysydydqcgqd",
								'isHot' => 0,
								'localeName' => "英属印度洋地区(查各群岛)",
								'display' => "英属印度洋地区(查各群岛)(IO)"
							]
						]
					],
					[
						'name' => "Z",
						'countrys' => [
							[
								'code' => "GI",
								'name' => "直布罗陀",
								'ename' => "GIBRALTAR",
								'pinyin' => "zblt",
								'isHot' => 0,
								'localeName' => "直布罗陀",
								'display' => "直布罗陀(GI)"
							],
							[
								'code' => "ZM",
								'name' => "赞比亚",
								'ename' => "ZAMBIA",
								'pinyin' => "zby",
								'isHot' => 0,
								'localeName' => "赞比亚",
								'display' => "赞比亚(ZM)"
							],
							[
								'code' => "TD",
								'name' => "乍得",
								'ename' => "CHAD",
								'pinyin' => "zd",
								'isHot' => 0,
								'localeName' => "乍得",
								'display' => "乍得(TD)"
							],
							[
								'code' => "CF",
								'name' => "中非共和国",
								'ename' => "CENTRAL AFRICAN REPUBLIC",
								'pinyin' => "zfghg",
								'isHot' => 0,
								'localeName' => "中非共和国",
								'display' => "中非共和国(CF)"
							],
							[
								'code' => "CN",
								'name' => "中国",
								'ename' => "CHINA",
								'pinyin' => "zg",
								'isHot' => 0,
								'localeName' => "中国",
								'display' => "中国(CN)"
							],
							[
								'code' => "Y3",
								'name' => "珠海仓",
								'ename' => "ZHUHAI",
								'pinyin' => "zhc",
								'isHot' => 0,
								'localeName' => "珠海仓",
								'display' => "珠海仓(Y3)"
							],
							[
								'code' => "CL",
								'name' => "智利",
								'ename' => "CHILE",
								'pinyin' => "zl",
								'isHot' => 0,
								'localeName' => "智利",
								'display' => "智利(CL)"
							],
							[
								'code' => "Y2",
								'name' => "中山仓",
								'ename' => "ZHONGSHAN",
								'pinyin' => "zsc",
								'isHot' => 0,
								'localeName' => "中山仓",
								'display' => "中山仓(Y2)"
							],
							[
								'code' => "Y4",
								'name' => "长沙仓",
								'ename' => "CHANGSHA",
								'pinyin' => "zsc",
								'isHot' => 0,
								'localeName' => "长沙仓",
								'display' => "长沙仓(Y4)"
							],
							[
								'code' => "HI",
								'name' => "中途岛(美)",
								'ename' => "Midway Island",
								'pinyin' => "ztdm",
								'isHot' => 0,
								'localeName' => "中途岛(美)",
								'display' => "中途岛(美)(HI)"
							],
							[
								'code' => "JE",
								'name' => "泽西岛(英属)",
								'ename' => "JERSEY",
								'pinyin' => "zxdys",
								'isHot' => 0,
								'localeName' => "泽西岛(英属)",
								'display' => "泽西岛(英属)(JE)"
							],
							[
								'code' => "ZR",
								'name' => "扎伊尔",
								'ename' => "ZAIRE",
								'pinyin' => "zye",
								'isHot' => 0,
								'localeName' => "扎伊尔",
								'display' => "扎伊尔(ZR)"
							]
						]
					]
				]
			]
		];
    }

    /**
     * 获取物流产品详情
     * 
     * @param string $productCode 物流产品代码
     * @param bool $debug 是否开启调试模式
     * @return array
     */
    public function getLogisticsProductDetail($productCode, $debug = false)
    {
        return $this->sendRequest('ds.xms.logistics.product.detail', 'V1.0.0', [
            'logistics_product_code' => $productCode
        ], $debug);
    }

    /**
     * 获取多个物流选项的价格（智能选择版本）
     * 
     * @param array $params 查询参数
     * @param string $userCurrency 用户币种
     * @return array
     * @throws \Exception
     */
    public function getShippingOptions($params, $userCurrency = 'USD')
    {
        // 调用预估费用查询接口，不指定具体产品代码，获取所有可用选项
        $result = $this->calculateFreight($params, false);
        if (empty($result) || !is_array($result)) {
            throw new \Exception('物流服务返回数据为空');
        }

        // 确保结果是数组格式
        $freightOptions = is_array($result[0] ?? null) ? $result : [$result];

        if (empty($freightOptions)) {
            throw new \Exception('没有可用的物流选项');
        }

        // 智能选择三种物流方式
        $selectedOptions = $this->selectOptimalShippingOptions($freightOptions, $userCurrency);

        return $selectedOptions;
    }

    /**
     * 币种转换（使用实时汇率API）
     * 
     * @param float $amount 金额
     * @param string $fromCurrency 源币种
     * @param string $toCurrency 目标币种
     * @return float
     */
    private function convertCurrency($amount, $fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        try {
            $currencyService = app(\App\Services\CurrencyService::class);
            return $currencyService->convertCurrency($amount, $fromCurrency, $toCurrency);
        } catch (\Exception $e) {
            Log::warning('汇率转换失败，使用备用汇率', [
                'amount' => $amount,
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'error' => $e->getMessage()
            ]);

            // 备用汇率
            $fallbackRates = [
                'CNY_USD' => 0.14,
                'CNY_EUR' => 0.13,
                'CNY_GBP' => 0.11,
            ];

            $rateKey = $fromCurrency . '_' . $toCurrency;
            $rate = $fallbackRates[$rateKey] ?? 0.14;

            return round($amount * $rate, 2);
        }
    }

    /**
     * 从所有物流选项中智能选择最优的三种
     * 
     * @param array $freightOptions 所有物流选项
     * @param string $userCurrency 用户币种
     * @return array
     */
    private function selectOptimalShippingOptions($freightOptions, $userCurrency)
    {
        $processedOptions = [];

        // 处理每个物流选项（基于实际API响应格式）
        foreach ($freightOptions as $option) {
            // 检查必要字段（基于实际API响应）
            if (empty($option['lump_sum_fee']) || empty($option['logistics_product_code']) || $option['lump_sum_fee'] == '0.00') {
                continue;
            }

            // 转换费用（lump_sum_fee是人民币）
            $cost = $this->convertCurrency($option['lump_sum_fee'], 'CNY', $userCurrency);

            $processedOptions[] = [
                'code' => $option['logistics_product_code'],
                'cost' => $cost,
                'original_cost' => $option['lump_sum_fee'],
                'currency' => $userCurrency,
                'original_currency' => 'CNY',
                'estimated_days' => $option['estimated_time'] ?? '10-20',
                'charge_weight' => $option['charge_weight'] ?? '',
                'is_trackable' => ($option['is_show_track'] ?? 'N') === 'Y',
                'remarks' => $option['remarks'] ?? '',
                'raw_data' => $option
            ];
        }
        if (empty($processedOptions)) {
            throw new \Exception('无法处理物流选项数据');
        }

        // 按不同维度排序选择最优选项
        $express = $this->selectExpressOption($processedOptions);    // 时间最短
        $economy = $this->selectEconomyOption($processedOptions);    // 最便宜
        $standard = $this->selectStandardOption($processedOptions, $express, $economy); // 性价比高
        //如果standard的cost高于express，那么则不显示standard
        if ($standard && $standard['cost'] > $express['cost']) {
            $standard = null;
        }
        $result = [];
        if ($express) $result[] = $express;
        if ($standard) $result[] = $standard;
        if ($economy) $result[] = $economy;

        // // 如果选择的选项少于3个，补充其他选项
        // if (count($result) < 3) {
        //     $result = $this->fillMissingOptions($result, $processedOptions);
        // }

        return array_slice($result, 0, 3); // 最多返回3个选项
    }

    /**
     * 选择快递选项（时间最短）
     */
    private function selectExpressOption($options)
    {
        // 按预估天数排序，选择最快的
        usort($options, function ($a, $b) {
            $daysA = $this->parseEstimatedDays($a['estimated_days']);
            $daysB = $this->parseEstimatedDays($b['estimated_days']);
            
            // 如果天数相同，按价格排序（便宜的在前）
            if ($daysA === $daysB) {
                return $a['cost'] <=> $b['cost'];
            }
            
            return $daysA <=> $daysB;
        });

        $option = $options[0];
        return [
            'type' => 'express',
            'name' => 'Express Shipping',
            'code' => $option['code'],
            'cost' => $option['cost'],
            'currency' => $option['currency'],
            'original_cost' => $option['original_cost'],
            'original_currency' => $option['original_currency'],
            'estimated_days' => $option['estimated_days'],
            'description' => 'Fastest delivery with tracking',
            'charge_weight' => $option['charge_weight'],
            'is_trackable' => $option['is_trackable'],
            'remarks' => $option['remarks']
        ];
    }

    /**
     * 选择经济选项（最便宜）
     */
    private function selectEconomyOption($options)
    {
        // 按价格排序，选择最便宜的
        usort($options, function ($a, $b) {
            return $a['cost'] <=> $b['cost'];
        });

        $option = $options[0];
        return [
            'type' => 'economy',
            'name' => 'Economy Shipping',
            'code' => $option['code'],
            'cost' => $option['cost'],
            'currency' => $option['currency'],
            'original_cost' => $option['original_cost'],
            'original_currency' => $option['original_currency'],
            'estimated_days' => $option['estimated_days'],
            'description' => 'Most affordable option',
            'charge_weight' => $option['charge_weight'],
            'is_trackable' => $option['is_trackable'],
            'remarks' => $option['remarks']
        ];
    }

    /**
     * 选择标准选项（性价比高）
     */
    private function selectStandardOption($options, $express, $economy)
    {
        // 计算性价比：时间/价格比值，选择中等的选项
        $scoredOptions = [];

        foreach ($options as $option) {
            // 跳过已选择的选项
            if ($option['code'] === $express['code'] || $option['code'] === $economy['code']) {
                continue;
            }

            $days = $this->parseEstimatedDays($option['estimated_days']);
            $cost = $option['cost'];

            // 性价比分数：天数越少越好，价格越低越好
            $score = $cost > 0 ? $days / $cost : PHP_FLOAT_MAX;

            $scoredOptions[] = [
                'option' => $option,
                'score' => $score
            ];
        }

        if (empty($scoredOptions)) {
            // 如果没有其他选项，从所有选项中选择中等价格的
            usort($options, function ($a, $b) {
                return $a['cost'] <=> $b['cost'];
            });
            $middleIndex = intval(count($options) / 2);
            $option = $options[$middleIndex];
        } else {
            // 按性价比排序，选择最优的
            usort($scoredOptions, function ($a, $b) {
                return $a['score'] <=> $b['score'];
            });
            $option = $scoredOptions[0]['option'];
        }

        return [
            'type' => 'standard',
            'name' => 'Standard Shipping',
            'code' => $option['code'],
            'cost' => $option['cost'],
            'currency' => $option['currency'],
            'original_cost' => $option['original_cost'],
            'original_currency' => $option['original_currency'],
            'estimated_days' => $option['estimated_days'],
            'description' => 'Good balance of speed and cost',
            'charge_weight' => $option['charge_weight'],
            'is_trackable' => $option['is_trackable'],
            'remarks' => $option['remarks']
        ];
    }

    /**
     * 补充缺失的选项
     */
    private function fillMissingOptions($selectedOptions, $allOptions)
    {
        $selectedCodes = array_column($selectedOptions, 'code');

        foreach ($allOptions as $option) {
            if (count($selectedOptions) >= 3) {
                break;
            }

            if (!in_array($option['code'], $selectedCodes)) {
                $selectedOptions[] = [
                    'type' => 'standard',
                    'name' => 'Standard Shipping',
                    'code' => $option['code'],
                    'cost' => $option['cost'],
                    'currency' => $option['currency'],
                    'original_cost' => $option['original_cost'],
                    'original_currency' => $option['original_currency'],
                    'estimated_days' => $option['estimated_days'],
                    'description' => 'Alternative shipping option',
                    'charge_weight' => $option['charge_weight'],
                    'is_trackable' => $option['is_trackable']
                ];
                $selectedCodes[] = $option['code'];
            }
        }

        return $selectedOptions;
    }



    /**
     * 解析预估天数为数字（用于排序）
     */
    private function parseEstimatedDays($estimatedDays)
    {
        if (preg_match('/(\d+)-(\d+)/', $estimatedDays, $matches)) {
            return (intval($matches[1]) + intval($matches[2])) / 2; // 取平均值
        } elseif (preg_match('/(\d+)/', $estimatedDays, $matches)) {
            return intval($matches[1]);
        }

        return 15; // 默认15天
    }
}
