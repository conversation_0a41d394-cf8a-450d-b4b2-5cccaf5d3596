<?php

namespace App\Services;

// 临时提高内存限制以处理大图像
ini_set('memory_limit', '512M');

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ImageProcessor
{
    /**
     * 将文本添加到图像
     *
     * @param string $imageUrl 图像URL
     * @param array $textElements 文本元素配置
     * @return string 处理后的图像URL
     */
    public function addTextToImage($imageUrl, $textElements)
    {
        try {
            // 使用GD库加载图像
            $image = $this->loadImage($imageUrl);
            
            if (!$image) {
                Log::error('添加文本到图像失败: 无法加载图片', ['image_url' => $imageUrl]);
                throw new \Exception('无法加载图片: ' . $imageUrl);
            }
            
            // 获取图像尺寸
            $width = imagesx($image);
            $height = imagesy($image);
            
            Log::info('开始添加文本到图像', [
                'image_dimensions' => "{$width}x{$height}",
                'text_elements_count' => count($textElements)
            ]);
            
            // 处理每个文本元素
            foreach ($textElements as $elementIndex => $element) {
                $text = $element['text'] ?? $element['defaultText'] ?? '';
                if (empty($text)) {
                    Log::info('跳过空文本元素', ['element_index' => $elementIndex]);
                    continue;
                }
                
                // 设置字体大小和位置
                $fontSize = $element['fontSize'] ?? 16;
                $fontFamily = $element['font'] ?? 'sans-serif';
                
                // 获取字体路径 - 使用detectBestFont根据文本内容自动选择合适的字体
                $fontPath = $this->detectBestFont($text, $fontFamily);
                
                // 检查字体文件是否存在
                if (!file_exists($fontPath)) {
                    Log::warning('字体文件不存在，尝试使用默认字体', [
                        'font_family' => $fontFamily,
                        'detected_font_path' => $fontPath,
                        'element_index' => $elementIndex
                    ]);
                    
                    // 回退到基本的getFontPath方法
                    $fontPath = $this->getFontPath('sans-serif');
                    
                    // 如果仍然无法找到字体文件，记录错误并尝试使用系统字体
                    if (!file_exists($fontPath)) {
                        Log::error('无法找到任何可用字体文件，尝试使用系统字体');
                        
                        // 判断字符串中是否包含中文
                        $containsChinese = preg_match('/[\x{4e00}-\x{9fa5}]/u', $text);
                        
                        // 根据操作系统选择适当的系统字体
                        if (PHP_OS_FAMILY === 'Darwin') { // macOS
                            $fontPath = $containsChinese 
                                ? '/System/Library/Fonts/PingFang.ttc' 
                                : '/System/Library/Fonts/Helvetica.ttc';
                        } elseif (PHP_OS_FAMILY === 'Windows') {
                            $fontPath = $containsChinese 
                                ? 'C:\\Windows\\Fonts\\simhei.ttf' 
                                : 'C:\\Windows\\Fonts\\arial.ttf';
                        } else { // Linux
                            $fontPath = $containsChinese 
                                ? '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc' 
                                : '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf';
                        }
                        
                        // 检查系统字体是否存在
                        if (!file_exists($fontPath)) {
                            Log::error('无法找到系统字体，将回退到GD内置字体', [
                                'system_font_path' => $fontPath
                            ]);
                        }
                    }
                }
                
                // 获取位置坐标
                $x = $element['x'] ?? 0;
                $y = $element['y'] ?? 0;
                
                // 设置颜色
                $color = $element['color'] ?? '#000000';
                // 解析颜色
                $rgb = $this->hexToRgb($color);
                $textColor = imagecolorallocate($image, $rgb['r'], $rgb['g'], $rgb['b']);
                
                // 获取文本旋转角度（如果有）
                $angle = $element['angle'] ?? 0;
                
                // 获取文本对齐方式
                $align = strtolower($element['align'] ?? 'left');
                
                // 如果需要计算文本宽度进行对齐
                if ($align !== 'left' && function_exists('imagettfbbox') && file_exists($fontPath)) {
                    $box = imagettfbbox($fontSize, $angle, $fontPath, $text);
                    if ($box) {
                        $textWidth = abs($box[4] - $box[0]);
                        
                        if ($align === 'center') {
                            $x -= $textWidth / 2;
                        } elseif ($align === 'right') {
                            $x -= $textWidth;
                        }
                    } else {
                        Log::warning('无法计算文本边界，使用原始坐标', [
                            'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                            'align' => $align
                        ]);
                    }
                }
                
                // 添加文本 - 优先使用TrueType
                if (function_exists('imagettftext') && file_exists($fontPath)) {
                    // 使用指定的字体
                    try {
                        $result = imagettftext($image, $fontSize, $angle, $x, $y + $fontSize, $textColor, $fontPath, $text);
                        
                        if ($result === false) {
                            Log::error('imagettftext调用失败，回退使用内置字体', [
                                'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                                'font_path' => $fontPath,
                                'font_family' => $fontFamily
                            ]);
                            // 回退使用内置字体
                            imagestring($image, 5, $x, $y, $text, $textColor);
                        } else {
                            Log::info('成功添加TTF文本', [
                                'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                                'element_index' => $elementIndex,
                                'font_family' => $fontFamily
                            ]);
                        }
                    } catch (\Exception $fontEx) {
                        Log::error('字体渲染异常，回退使用内置字体', [
                            'error' => $fontEx->getMessage(),
                            'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                            'font_path' => $fontPath,
                            'font_family' => $fontFamily
                        ]);
                        // 回退使用内置字体
                        imagestring($image, 5, $x, $y, $text, $textColor);
                    }
                } else {
                    // 内置字体
                    Log::warning('无法使用TrueType字体，回退使用内置字体', [
                        'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                        'font_path' => $fontPath,
                        'font_family' => $fontFamily,
                        'exists' => file_exists($fontPath),
                        'ttf_supported' => function_exists('imagettftext')
                    ]);
                    
                    // 警告：GD内置字体不支持中文和许多特殊字符
                    if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $text)) {
                        Log::warning('内置字体不支持中文字符，文本可能无法正确显示', [
                            'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : '')
                        ]);
                    }
                    
                    imagestring($image, 5, $x, $y, $text, $textColor);
                }
            }
            
            // 保存处理后的图像
            $outputPath = 'processed/' . uniqid() . '.jpg';
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'img_');
            
            // 保存为JPEG到临时文件
            $saveResult = imagejpeg($image, $tempFile, 90);
            
            if (!$saveResult) {
                Log::error('无法保存处理后的图像', ['output_path' => $outputPath]);
                throw new \Exception('无法保存处理后的图像');
            }
            
            // 读取临时文件内容
            $imageContent = file_get_contents($tempFile);
            
            // 上传到S3 - 使用picbook存储
            Storage::picbook()->put($outputPath, $imageContent);
            
            // 删除临时文件
            @unlink($tempFile);
            
            // 释放资源
            $this->destroyImage($image);
            
            Log::info('成功添加文本到图像并保存到S3', ['output_path' => $outputPath]);
            
            // 返回S3上的图像URL
            return Storage::picbook()->url($outputPath);
        } catch (\Exception $e) {
            Log::error('添加文本到图像失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'image_url' => $imageUrl
            ]);
            return $imageUrl; // 失败时返回原始图像URL
        }
    }

    
    /**
     * 处理图像蒙版
     *
     * @param string $imageUrl 图像URL
     * @param string $maskUrl 蒙版URL
     * @return string 处理后的图像URL
     */
    public function applyMask($imageUrl, $maskUrl)
    {
        try {
            // 使用GD库加载图像
            $image = $this->loadImage($imageUrl);
            if (!$image) {
                throw new \Exception('无法加载图片: ' . $imageUrl);
            }
            
            // 加载蒙版图像
            $mask = $this->loadImage($maskUrl);
            if (!$mask) {
                throw new \Exception('无法加载蒙版图片: ' . $maskUrl);
            }
            
            // 获取图像尺寸
            $width = imagesx($image);
            $height = imagesy($image);
            $maskWidth = imagesx($mask);
            $maskHeight = imagesy($mask);
            
            // 调整蒙版大小以匹配原始图像
            if ($width != $maskWidth || $height != $maskHeight) {
                $resizedMask = imagecreatetruecolor($width, $height);
                
                // 保持透明度
                imagealphablending($resizedMask, false);
                imagesavealpha($resizedMask, true);
                $transparent = imagecolorallocatealpha($resizedMask, 0, 0, 0, 127);
                imagefilledrectangle($resizedMask, 0, 0, $width, $height, $transparent);
                
                // 缩放蒙版
                imagecopyresampled($resizedMask, $mask, 0, 0, 0, 0, $width, $height, $maskWidth, $maskHeight);
                
                // 释放原始蒙版资源
                $this->destroyImage($mask);
                $mask = $resizedMask;
            }
            
            // 创建新图像用于应用蒙版
            $result = imagecreatetruecolor($width, $height);
            
            // 保持透明度
            imagealphablending($result, false);
            imagesavealpha($result, true);
            $transparent = imagecolorallocatealpha($result, 0, 0, 0, 127);
            imagefilledrectangle($result, 0, 0, $width, $height, $transparent);
            
            // 应用蒙版：逐像素处理
            for ($x = 0; $x < $width; $x++) {
                for ($y = 0; $y < $height; $y++) {
                    // 获取蒙版像素颜色
                    $maskColor = imagecolorat($mask, $x, $y);
                    $maskAlpha = ($maskColor >> 24) & 0x7F; // 提取Alpha值
                    
                    // 如果蒙版像素不完全透明
                    if ($maskAlpha < 127) {
                        // 计算透明度百分比（0-100%）
                        $alpha = ($maskAlpha / 127) * 100;
                        $opacity = 100 - $alpha; // 不透明度
                        
                        // 获取原图像素
                        $color = imagecolorat($image, $x, $y);
                        $r = ($color >> 16) & 0xFF;
                        $g = ($color >> 8) & 0xFF;
                        $b = $color & 0xFF;
                        $a = ($color >> 24) & 0x7F;
                        
                        // 应用不透明度
                        $newAlpha = min(127, $a + ($opacity / 100) * (127 - $a));
                        
                        // 设置像素
                        $newColor = imagecolorallocatealpha($result, $r, $g, $b, (int)$newAlpha);
                        imagesetpixel($result, $x, $y, $newColor);
                    }
                }
            }
            
            // 保存处理后的图像
            $outputPath = 'processed/' . uniqid() . '.png';
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'mask_');
            
            // 保存为PNG以保持透明度
            imagepng($result, $tempFile, 9);
            
            // 读取临时文件内容
            $imageContent = file_get_contents($tempFile);
            
            // 上传到S3 - 使用picbook存储
            Storage::picbook()->put($outputPath, $imageContent);
            
            // 删除临时文件
            @unlink($tempFile);
            
            // 释放资源
            $this->destroyImage($image);
            $this->destroyImage($resizedMask);
            $this->destroyImage($mask);
            $this->destroyImage($result);
            
            Log::info('成功应用蒙版并保存到S3', ['output_path' => $outputPath]);
            
            // 返回S3上的图像URL
            return Storage::picbook()->url($outputPath);
        } catch (\Exception $e) {
            Log::error('应用蒙版到图像失败: ' . $e->getMessage());
            return $imageUrl; // 失败时返回原始图像URL
        }
    }
    
    /**
     * 生成不同分辨率的图像
     *
     * @param string $imageUrl 原始图像URL
     * @param array $resolutions 分辨率配置
     * @return array 处理后的图像URL数组
     */
    public function generateResolutions($imageUrl, $resolutions)
    {
        $result = [];
        
        try {
            // 使用GD库加载图像
            $image = $this->loadImage($imageUrl);
            if (!$image) {
                throw new \Exception('无法加载图片: ' . $imageUrl);
            }
            
            // 获取原始尺寸
            $origWidth = imagesx($image);
            $origHeight = imagesy($image);
            
            // 原始分辨率
            $result['original'] = $imageUrl;
            
            // 生成不同分辨率
            foreach ($resolutions as $name => $size) {
                $width = $size['width'] ?? null;
                $height = $size['height'] ?? null;
                
                // 计算新尺寸
                if ($width && $height) {
                    // 两者都提供，计算等比例缩放
                    $ratio = min($width / $origWidth, $height / $origHeight);
                    $newWidth = (int)($origWidth * $ratio);
                    $newHeight = (int)($origHeight * $ratio);
                } elseif ($width) {
                    // 只提供宽度
                    $ratio = $width / $origWidth;
                    $newWidth = $width;
                    $newHeight = (int)($origHeight * $ratio);
                } elseif ($height) {
                    // 只提供高度
                    $ratio = $height / $origHeight;
                    $newWidth = (int)($origWidth * $ratio);
                    $newHeight = $height;
                } else {
                    // 都没提供，使用原始尺寸
                    $newWidth = $origWidth;
                    $newHeight = $origHeight;
                }
                
                // 创建新图像
                $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
                
                // 保持透明度
                imagealphablending($resizedImage, false);
                imagesavealpha($resizedImage, true);
                $transparent = imagecolorallocatealpha($resizedImage, 0, 0, 0, 127);
                imagefilledrectangle($resizedImage, 0, 0, $newWidth, $newHeight, $transparent);
                
                // 缩放图像
                imagecopyresampled($resizedImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $origWidth, $origHeight);
                
                // 创建临时文件
                $tempFile = tempnam(sys_get_temp_dir(), 'res_');
                
                // 保存为JPEG
                imagejpeg($resizedImage, $tempFile, 90);
                
                // 保存处理后的图像到S3
                $outputPath = 'processed/' . uniqid() . '_' . $name . '.jpg';
                
                // 读取临时文件内容
                $imageContent = file_get_contents($tempFile);
                
                // 上传到S3 - 使用picbook存储
                Storage::picbook()->put($outputPath, $imageContent);
                
                // 删除临时文件
                @unlink($tempFile);
                
                // 释放资源
                $this->destroyImage($resizedImage);
                
                $result[$name] = Storage::picbook()->url($outputPath);
            }
            
            // 释放原始图像资源
            $this->destroyImage($image);
            
        } catch (\Exception $e) {
            Log::error('生成不同分辨率图像失败: ' . $e->getMessage());
        }
        
        return $result;
    }
    
    /**
     * 加载图像并返回资源
     *
     * @param string $imageUrl 图像URL
     * @return mixed 图像资源或失败时返回false
     */
    public function loadImage($imageUrl)
    {
        try {
            // 检查输入是否为资源
            if (is_resource($imageUrl)) {
                return $imageUrl;
            }
            
            // 记录原始图像URL
            Log::info('尝试加载图像', ['original_url' => $imageUrl]);
            
            // 检查URL格式
            if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                // 如果是外部URL，下载图像
                $imageString = @file_get_contents($imageUrl);
                if ($imageString === false) {
                    Log::warning('无法从外部URL加载图像', ['url' => $imageUrl]);
                    return false;
                }
                return imagecreatefromstring($imageString);
            }
            
            // 本地文件处理 - 更详细的路径解析
            $filePath = null;
            
            // 直接尝试绝对路径
            if (file_exists($imageUrl)) {
                $filePath = $imageUrl;
                Log::info('图像作为绝对路径存在', ['path' => $filePath]);
            } 
            // 尝试public目录下的路径
            elseif (file_exists(public_path($imageUrl))) {
                $filePath = public_path($imageUrl);
                Log::info('在public目录找到图像', ['path' => $filePath]);
            }
            // 处理public/imgs路径
            elseif (strpos($imageUrl, 'public/imgs/') === 0) {
                $filePath = base_path($imageUrl);
                Log::info('处理public/imgs/路径', ['path' => $filePath]);
                
                if (!file_exists($filePath)) {
                    // 尝试不带public前缀的路径
                    $withoutPublic = str_replace('public/', '', $imageUrl);
                    $altPath = public_path($withoutPublic);
                    if (file_exists($altPath)) {
                        $filePath = $altPath;
                        Log::info('使用不带public前缀的路径', ['path' => $filePath]);
                    }
                }
            }
            // 处理imgs路径
            elseif (strpos($imageUrl, 'imgs/') === 0) {
                $filePath = public_path($imageUrl);
                Log::info('处理imgs/路径', ['path' => $filePath]);
            }
            // 检查storage路径
            elseif (strpos($imageUrl, 'storage/') === 0) {
                $filePath = storage_path('app/public/' . substr($imageUrl, 8));
                Log::info('处理storage/路径', ['path' => $filePath]);
            }
            // 检查admin_uploads路径
            elseif (strpos($imageUrl, 'admin_uploads/') === 0) {
                $filePath = storage_path('app/public/' . $imageUrl);
                Log::info('处理admin_uploads/路径', ['path' => $filePath]);
            }
            // 默认解析为storage路径
            else {
                $filePath = storage_path('app/public/' . $imageUrl);
                Log::info('默认解析为storage路径', ['path' => $filePath]);
            }
            
            // 尝试常见的可能路径
            if (!file_exists($filePath)) {
                Log::warning('主路径不存在，尝试替代路径', [
                    'attempted_path' => $filePath,
                    'original_url' => $imageUrl
                ]);
                
                $alternativePaths = [
                    public_path($imageUrl),
                    base_path($imageUrl),
                    public_path('imgs/' . basename($imageUrl)),
                    storage_path('app/public/' . $imageUrl),
                    storage_path('app/' . $imageUrl),
                    public_path('storage/' . $imageUrl)
                ];
                
                foreach ($alternativePaths as $path) {
                    if (file_exists($path)) {
                        $filePath = $path;
                        Log::info('找到有效的图像替代路径', ['valid_path' => $path]);
                        break;
                    }
                }
            }
            
            // 最后的尝试，查找与文件名匹配的任何文件
            if (!file_exists($filePath)) {
                $fileName = basename($imageUrl);
                Log::warning('尝试查找与文件名匹配的任何文件', ['file_name' => $fileName]);
                
                $foundFile = shell_exec("find public -name \"$fileName\" 2>/dev/null | head -1");
                if ($foundFile) {
                    $filePath = trim($foundFile);
                    if (file_exists($filePath)) {
                        Log::info('通过查找命令找到文件', ['path' => $filePath]);
                    }
                }
            }
            
            // 验证文件存在
            if (!file_exists($filePath)) {
                Log::warning('图像文件不存在，尝试了所有可能的路径', [
                    'original_url' => $imageUrl,
                    'final_path' => $filePath
                ]);
                return false;
            }
            
            Log::info('成功找到图像文件', [
                'original_url' => $imageUrl,
                'file_path' => $filePath
            ]);
            
            // 根据文件类型创建图像
            $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
            
            // 更详细的错误处理和日志记录
            switch ($extension) {
                case 'jpg':
                case 'jpeg':
                    $image = @imagecreatefromjpeg($filePath);
                    if (!$image) {
                        Log::error('无法从JPEG创建图像', ['path' => $filePath]);
                    }
                    return $image;
                case 'png':
                    $image = @imagecreatefrompng($filePath);
                    if (!$image) {
                        Log::error('无法从PNG创建图像', ['path' => $filePath]);
                    }
                    return $image;
                case 'gif':
                    $image = @imagecreatefromgif($filePath);
                    if (!$image) {
                        Log::error('无法从GIF创建图像', ['path' => $filePath]);
                    }
                    return $image;
                case 'webp':
                    $image = @imagecreatefromwebp($filePath);
                    if (!$image) {
                        Log::error('无法从WebP创建图像', ['path' => $filePath]);
                    }
                    return $image;
                default:
                    Log::warning('不支持的图像格式', ['extension' => $extension, 'path' => $filePath]);
                    
                    // 尝试使用GD自动检测类型
                    $image = @imagecreatefromstring(file_get_contents($filePath));
                    if ($image) {
                        Log::info('使用imagecreatefromstring成功创建图像', ['path' => $filePath]);
                        return $image;
                    }
                    
                    Log::error('无法加载任何格式的图像', ['path' => $filePath]);
                    return false;
            }
        } catch (\Exception $e) {
            Log::error('加载图像失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'imageUrl' => $imageUrl
            ]);
            return false;
        }
    }
    
    /**
     * 克隆图像
     *
     * @param \GdImage|resource $image 原始图像资源
     * @return \GdImage|resource 克隆的图像资源
     */
    public function cloneImage($image)
    {
        // 获取图像尺寸
        $width = imagesx($image);
        $height = imagesy($image);
        
        // 创建新图像
        $newImage = imagecreatetruecolor($width, $height);
        
        // 保持透明度
        imageAlphaBlending($newImage, true);
        imageSaveAlpha($newImage, true);
        
        // 复制原图到新图
        imagecopy($newImage, $image, 0, 0, 0, 0, $width, $height);
        
        return $newImage;
    }
    
    /**
     * 应用蒙版并合并图像
     *
     * @param \GdImage|resource $destinationImage 目标图像资源
     * @param \GdImage|resource $sourceImage 源图像资源
     * @param \GdImage|resource $maskImage 蒙版图像资源
     */
    public function applyMaskAndMerge($destinationImage, $sourceImage, $maskImage)
    {
        // 获取图像尺寸
        $width = imagesx($destinationImage);
        $height = imagesy($destinationImage);
        
        // 创建临时画布
        $tempCanvas = imagecreatetruecolor($width, $height);
        
        // 保持透明度
        imageAlphaBlending($tempCanvas, true);
        imageSaveAlpha($tempCanvas, true);
        
        // 将源图像复制到临时画布
        imagecopy($tempCanvas, $sourceImage, 0, 0, 0, 0, $width, $height);
        
        // 应用蒙版，将源图像的合适部分复制到目标图像
        for ($x = 0; $x < $width; $x++) {
            for ($y = 0; $y < $height; $y++) {
                // 获取蒙版像素的颜色
                $maskColor = imagecolorat($maskImage, $x, $y);
                
                // 从颜色中提取alpha值（0-127，0=不透明，127=完全透明）
                $alpha = ($maskColor >> 24) & 0x7F;
                
                // 如果蒙版像素是较低的透明度（表示应该显示），则复制源图像像素
                if ($alpha < 64) { // 可以根据需要调整阈值
                    $sourceColor = imagecolorat($tempCanvas, $x, $y);
                    imagesetpixel($destinationImage, $x, $y, $sourceColor);
                }
            }
        }
        
        // 释放临时画布资源
        imagedestroy($tempCanvas);
    }
    
    /**
     * 释放图像资源
     *
     * @param \GdImage|resource $image 图像资源
     */
    public function destroyImage($image)
    {
        if ($image) {
            imagedestroy($image);
        }
    }
    
    /**
     * 保存图像
     *
     * @param \GdImage|resource $image 图像资源
     * @param string $directory 保存目录
     * @param string $filename 文件名
     * @param int $quality 图像质量（0-100）
     * @return string 保存的文件路径
     */
    public function saveImage($image, $directory, $filename, $quality = 90)
    {
        // 确保目录存在
        $fullPath = public_path($directory);
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0755, true);
        }
        
        // 完整文件路径
        $outputFile = $fullPath . '/' . $filename;
        
        // 保存图像
        imagejpeg($image, $outputFile, $quality);
        
        // 返回相对路径
        return $directory . '/' . $filename;
    }
    
    /**
     * 获取字体路径
     *
     * @param string $fontFamily 字体名称
     * @return string 字体路径
     */
    private function getFontPath($fontFamily)
    {
        // 字体映射表，优先查找项目字体目录中的字体
        $fontMap = [
            // 英文字体
            'sans-serif' => public_path('fonts/NotoSans-Regular.ttf'),
            'sans-serif-bold' => public_path('fonts/NotoSans-Bold.ttf'),
            'philosopher-bold' => public_path('fonts/Philosopher-Bold.ttf'),
            'philosopher-bold-italic' => public_path('fonts/Philosopher-BoldItalic.ttf'),
            'philosopher-italic' => public_path('fonts/Philosopher-Italic.ttf'),
            'philosopher-regular' => public_path('fonts/Philosopher-Regular.ttf'),
            
            // 中文字体
            'noto-sans-sc' => public_path('fonts/NotoSansSC-Regular.otf'),
            'noto-sans-sc-bold' => public_path('fonts/NotoSansSC-Bold.otf'),
        ];
        
        // 系统字体备用路径（根据操作系统类型）
        $systemFonts = [
            // macOS系统字体
            'mac' => [
                'sans-serif' => '/System/Library/Fonts/Helvetica.ttc',
                'serif' => '/System/Library/Fonts/Times.ttc',
                'chinese' => '/System/Library/Fonts/PingFang.ttc',
            ],
            // Linux系统字体
            'linux' => [
                'sans-serif' => '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                'serif' => '/usr/share/fonts/truetype/dejavu/DejaVuSerif.ttf',
                'chinese' => '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
            ],
            // Windows系统字体
            'windows' => [
                'sans-serif' => 'C:\\Windows\\Fonts\\arial.ttf',
                'serif' => 'C:\\Windows\\Fonts\\times.ttf',
                'chinese' => 'C:\\Windows\\Fonts\\simhei.ttf',
            ],
        ];
        
        $requestedFont = strtolower($fontFamily);
        Log::info('请求字体', ['font_family' => $requestedFont]);
        
        // 1. 检查请求的字体是否在映射表中且文件存在
        if (isset($fontMap[$requestedFont]) && file_exists($fontMap[$requestedFont])) {
            Log::info('使用项目自带字体', [
                'font_family' => $requestedFont, 
                'font_path' => $fontMap[$requestedFont]
            ]);
            return $fontMap[$requestedFont];
        }
        
        // 2. 检查是否请求的是"任意可用字体"
        if ($requestedFont === 'any' || $requestedFont === '*' || $requestedFont === 'default') {
            // 尝试使用第一个可用的项目字体
            foreach ($fontMap as $fontName => $fontPath) {
                if (file_exists($fontPath)) {
                    Log::info('请求任意字体，使用首个可用项目字体', [
                        'requested' => $requestedFont,
                        'provided' => $fontName,
                        'path' => $fontPath
                    ]);
                    return $fontPath;
                }
            }
        }
        
        // 3. 尝试直接使用传入的路径（可能是绝对路径）
        if (file_exists($fontFamily)) {
            Log::info('直接使用传入的字体路径', ['font_path' => $fontFamily]);
            return $fontFamily;
        }
        
        // 4. 检查系统字体
        $osType = PHP_OS_FAMILY === 'Darwin' ? 'mac' : (PHP_OS_FAMILY === 'Windows' ? 'windows' : 'linux');
        
        // 根据请求的字体名判断是中文还是西文
        $isChinese = preg_match('/chinese|zh|cn|sc|tc|pingfang|noto.*?sc|noto.*?tc|heiti|songti|kaiti|simhei|simsun/i', $requestedFont);
        $fontType = $isChinese ? 'chinese' : (strpos($requestedFont, 'serif') !== false ? 'serif' : 'sans-serif');
        
        if (isset($systemFonts[$osType][$fontType]) && file_exists($systemFonts[$osType][$fontType])) {
            Log::info('使用系统备用字体', [
                'requested' => $requestedFont,
                'os_type' => $osType,
                'font_type' => $fontType,
                'provided' => $systemFonts[$osType][$fontType]
            ]);
            return $systemFonts[$osType][$fontType];
        }
        
        // 5. 最后回退到项目默认字体
        $defaultFont = $fontMap['sans-serif'];
        if (file_exists($defaultFont)) {
            Log::warning('未找到请求字体，使用项目默认sans-serif字体', [
                'requested' => $fontFamily,
                'provided' => $defaultFont
            ]);
            return $defaultFont;
        }
        
        // 6. 如果上述所有方法都失败，尝试找到任何可用字体
        foreach ($fontMap as $fontPath) {
            if (file_exists($fontPath)) {
                Log::warning('未找到请求字体和默认字体，使用首个可用字体', [
                    'requested' => $fontFamily,
                    'provided' => $fontPath
                ]);
                return $fontPath;
            }
        }
        
        // 7. 极端情况：记录错误并返回一个可能不存在的路径（调用方应处理此情况）
        Log::error('严重错误：未找到任何可用字体', [
            'requested' => $fontFamily,
            'font_map' => array_keys($fontMap)
        ]);
        
        // 返回默认字体路径，即使它不存在（调用方需要处理不存在的情况）
        return $fontMap['sans-serif'];
    }
    
    /**
     * 根据文本内容检测最合适的字体
     * 
     * @param string $text 要渲染的文本
     * @param string $preferredFont 首选字体（如果提供）
     * @return string 最佳字体的路径
     */
    private function detectBestFont($text, $preferredFont = null)
    {
        // 如果提供了首选字体，先尝试使用它
        if ($preferredFont) {
            $fontPath = $this->getFontPath($preferredFont);
            if (file_exists($fontPath)) {
                return $fontPath;
            }
        }
        
        // 检测文本中包含的语言类型
        $containsChinese = preg_match('/[\x{4e00}-\x{9fa5}]/u', $text);
        $containsJapanese = preg_match('/[\x{3040}-\x{309F}\x{30A0}-\x{30FF}\x{4E00}-\x{9FBF}]/u', $text);
        $containsKorean = preg_match('/[\x{AC00}-\x{D7AF}\x{1100}-\x{11FF}\x{3130}-\x{318F}]/u', $text);
        $containsCyrillic = preg_match('/[\x{0400}-\x{04FF}]/u', $text);
        $containsArabic = preg_match('/[\x{0600}-\x{06FF}]/u', $text);
        
        // 记录检测到的语言特征
        Log::info('检测文本字符类型', [
            'text_sample' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
            'contains_chinese' => $containsChinese,
            'contains_japanese' => $containsJapanese,
            'contains_korean' => $containsKorean,
            'contains_cyrillic' => $containsCyrillic,
            'contains_arabic' => $containsArabic
        ]);
        
        // 根据检测结果选择最佳字体
        if ($containsChinese) {
            // 中文优先使用NotoSansSC
            $fontPath = $this->getFontPath('noto-sans-sc');
            if (file_exists($fontPath)) {
                return $fontPath;
            }
            
            // 备用中文系统字体
            if (PHP_OS_FAMILY === 'Darwin') { // macOS
                $systemFont = '/System/Library/Fonts/PingFang.ttc';
            } elseif (PHP_OS_FAMILY === 'Windows') {
                $systemFont = 'C:\\Windows\\Fonts\\simhei.ttf';
            } else { // Linux或其他
                $systemFont = '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc';
            }
            
            if (file_exists($systemFont)) {
                return $systemFont;
            }
        } elseif ($containsJapanese || $containsKorean) {
            // 日韩文本可以考虑使用Noto Sans系列
            // 检查是否有noto sans jp/kr，假设项目中可能有这些字体
            $asianFontPath = public_path('fonts/NotoSansCJK-Regular.ttc');
            if (file_exists($asianFontPath)) {
                return $asianFontPath;
            }
            
            // 如果没有专门的字体，回退到通用字体
            return $this->getFontPath('sans-serif');
        } elseif ($containsCyrillic) {
            // 西里尔字母可以使用Noto Sans
            return $this->getFontPath('sans-serif');
        } elseif ($containsArabic) {
            // 阿拉伯文需要特殊的字体支持，如果项目中有可以添加
            $arabicFontPath = public_path('fonts/NotoSansArabic-Regular.ttf');
            if (file_exists($arabicFontPath)) {
                return $arabicFontPath;
            }
            
            // 如果没有专门的字体，回退到通用字体
            return $this->getFontPath('sans-serif');
        }
        
        // 其他情况使用默认的sans-serif字体
        return $this->getFontPath('sans-serif');
    }
    
    /**
     * 将十六进制颜色转换为RGB数组
     *
     * @param string $hex 十六进制颜色代码
     * @return array RGB颜色数组
     */
    private function hexToRgb($hex)
    {
        // 默认黑色
        $defaultRgb = ['r' => 0, 'g' => 0, 'b' => 0];
        
        // 处理空值或无效值
        if (empty($hex)) {
            Log::warning('传入hexToRgb的十六进制颜色代码为空，使用默认黑色');
            return $defaultRgb;
        }
        
        // 删除 # 前缀和所有空白字符
        $hex = ltrim(trim($hex), '#');
        
        // 验证十六进制格式
        if (!preg_match('/^[0-9A-Fa-f]{3}(?:[0-9A-Fa-f]{3})?$/', $hex)) {
            Log::warning('无效的十六进制颜色代码', ['hex' => $hex]);
            return $defaultRgb;
        }
        
        try {
            // 处理3位十六进制 (例如 FFF)
            if (strlen($hex) === 3) {
                $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
                $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
                $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
            } 
            // 处理6位十六进制 (例如 FFFFFF)
            else if (strlen($hex) === 6) {
                $r = hexdec(substr($hex, 0, 2));
                $g = hexdec(substr($hex, 2, 2));
                $b = hexdec(substr($hex, 4, 2));
            } 
            // 其他情况，使用默认值
            else {
                Log::warning('不支持的十六进制颜色长度', ['hex' => $hex, 'length' => strlen($hex)]);
                return $defaultRgb;
            }
            
            // 确保RGB值在有效范围内 (0-255)
            $r = max(0, min(255, $r));
            $g = max(0, min(255, $g));
            $b = max(0, min(255, $b));
            
            return ['r' => $r, 'g' => $g, 'b' => $b];
        } catch (\Exception $e) {
            Log::error('解析十六进制颜色时发生错误', [
                'hex' => $hex,
                'error' => $e->getMessage()
            ]);
            return $defaultRgb;
        }
    }

    /**
     * 将无肤色图片合并到有肤色图片上
     * 
     * @param \GdImage|resource $skinImage 有肤色的图像资源
     * @param string $noSkinImageUrl 无肤色图片URL
     * @param array $characterMasks 角色蒙版URL数组
     * @return \GdImage|resource 图像资源
     */
    public function mergeNoSkinImageOntoSkinImage($skinImage, $noSkinImageUrl, $characterMasks)
    {
        try {
            Log::info('合并无肤色图片到肤色图片', [
                'skinImage' => $skinImage ? 'valid' : 'null',
                'no_skin_image' => $noSkinImageUrl,
                'characterMasks' => $characterMasks,
                'masks_count' => count($characterMasks)
            ]);
            
            if (!$skinImage) {
                Log::error('皮肤图像为空，无法合并');
                return null;
            }
            
            // 加载无肤色图像
            $noSkinImage = $this->loadImage($noSkinImageUrl);
            if (!$noSkinImage) {
                Log::error('无法加载无肤色图片', [
                    'image_url' => $noSkinImageUrl,
                    'full_path' => public_path(parse_url($noSkinImageUrl, PHP_URL_PATH))
                ]);
                return $skinImage; // 返回原始的肤色图像
            }
            
            // 获取图像尺寸
            $width = imagesx($noSkinImage);
            $height = imagesy($noSkinImage);
            
            // 以无肤色图像为基础创建结果图像
            $result = $this->cloneImage($noSkinImage);
            if (!$result) {
                Log::error('无法克隆无肤色图像');
                $this->destroyImage($noSkinImage);
                return $skinImage;
            }
            
            // 如果没有提供蒙版，直接将有肤色图像合并到无肤色图像上
            if (empty($characterMasks)) {
                Log::info('未提供蒙版，直接合并图像');
                
                // 直接将有肤色图像完全覆盖到结果图像上
                imagecopy($result, $skinImage, 0, 0, 0, 0, $width, $height);
                
                // 清理原始无肤色图像资源
                $this->destroyImage($noSkinImage);
                
                return $result;
            }
            // 创建一个临时画布
            $test1 = imagecreatetruecolor($width, $height);
            imagealphablending($test1, true);
            imagesavealpha($test1, true);
            $transparent = imagecolorallocatealpha($test1, 0, 0, 0, 127);
            imagefill($test1, 0, 0, $transparent);
            // 处理每个蒙版
            foreach ($characterMasks as $maskUrl) {
                if (empty($maskUrl)) continue;
                
                // 使用loadImage方法加载蒙版图像，而不是直接使用imagecreatefrompng
                $maskImage = $this->loadImage($maskUrl);
                if (!$maskImage) {
                    Log::warning('无法加载蒙版图片', [
                        'mask_url' => $maskUrl,
                        'full_path' => public_path(parse_url($maskUrl, PHP_URL_PATH))
                    ]);
                    continue;
                }
                // 将肤色蒙版图片复制到临时画布上
                imagecopy($test1, $maskImage, 0, 0, 0, 0, $width, $height);
                // 清理资源
                $this->destroyImage($maskImage);
            }
            // 将有肤色图像复制到临时画布
            imagecopy($test1, $skinImage, 0, 0, 0, 0, $width, $height);
            
            // 清理原始无肤色图像资源
            $this->destroyImage($noSkinImage);
            $this->destroyImage($result);
            
            // 记录完成信息
            Log::info('已成功合并图像');
            
            return $test1;
        } catch (\Exception $e) {
            Log::error('合并图像时发生错误: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return $skinImage; // 发生错误时返回原始的肤色图像
        }
    }
    
    /**
     * 保存处理后的图像
     *
     * @param \GdImage|resource $image 图像资源
     * @param string $filenamePrefix 文件名前缀
     * @param string $format 图像格式（jpg或png）
     * @return string 保存的图像URL
     */
    public function saveProcessedImage($image, $filenamePrefix, $format = 'png')
    {
        try {
            // 生成唯一文件名
            $filename = $filenamePrefix . '_' . uniqid() . '.' . $format;
            $outputPath = 'processed/' . $filename;
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'img_');
            
            // 根据格式保存图像到临时文件
            if ($format === 'png') {
                // 保存为PNG以保持透明度
                imagepng($image, $tempFile, 9);
            } else {
                // 保存为JPEG
                imagejpeg($image, $tempFile, 90);
            }
            
            // 读取临时文件内容
            $imageContent = file_get_contents($tempFile);
            
            // 上传到S3 - 使用picbook存储
            Storage::picbook()->put($outputPath, $imageContent);
            
            // 删除临时文件
            @unlink($tempFile);
            
            // 返回S3上的图像URL
            return Storage::picbook()->url($outputPath);
        } catch (\Exception $e) {
            Log::error('保存处理后的图像到S3失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 应用AI换脸
     *
     * @param string $imageUrl 原始图像URL
     * @param string $faceImageUrl 面部图像URL 
     * @param array $config 配置信息
     * @return array 处理结果
     */
    public function applyFaceSwap($imageUrl, $faceImageUrl, $config = [])
    {
        Log::info('开始处理AI换脸', [
            'image_url' => $imageUrl,
            // 'face_url' => $faceImageUrl,
            'config' => $config
        ]);
        
        try {
            // 检查文件路径格式
            Log::info('检查图像路径', [
                'raw_image_url' => $imageUrl,
                'mask_url' => $config['mask_url'] ?? 'null'
            ]);

            // 提取本地文件路径，文件目前是在public下的
            if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                $inputImagePath = storage_path('app/public/' . basename($imageUrl));
            } elseif (strpos($imageUrl, 'storage/') === 0) {
                $inputImagePath = storage_path('app/public/' . substr($imageUrl, 8));
            } elseif (strpos($imageUrl, 'admin_uploads/') === 0) {
                $inputImagePath = storage_path('app/public/' . $imageUrl);
            } elseif (strpos($imageUrl, 'public/imgs/') === 0) {
                // 直接从public目录加载
                $inputImagePath = base_path($imageUrl);
                if (!file_exists($inputImagePath)) {
                    // 可能是相对于public目录的路径，去掉public/前缀尝试
                    $withoutPublic = str_replace('public/', '', $imageUrl);
                    $altPath = public_path($withoutPublic);
                    if (file_exists($altPath)) {
                        $inputImagePath = $altPath;
                        Log::info('在public目录找到图像(移除public/前缀)', ['path' => $inputImagePath]);
                    }
                }
            } elseif (strpos($imageUrl, 'imgs/') === 0) {
                // 处理imgs开头的路径
                $inputImagePath = public_path($imageUrl);
            } else {
                $inputImagePath = storage_path('app/public/' . $imageUrl);
            }
            
            // 检查是否base64编码的人脸图像
            if (preg_match('/^data:image\/(\w+);base64,/', $faceImageUrl)) {
                $faceImagePath = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $faceImageUrl));
                
                // 保存人脸图像到临时文件
                $tempFaceFile = storage_path('app/temp_face_' . uniqid() . '.jpg');
                file_put_contents($tempFaceFile, $faceImagePath);
            } else {
                // 如果不是base64，尝试各种可能的路径解析
                $faceFilePaths = [
                    public_path(parse_url($faceImageUrl, PHP_URL_PATH)), // 标准URL路径
                    storage_path('app/public/' . $faceImageUrl), // storage相对路径
                    storage_path('app/public/' . basename($faceImageUrl)), // 仅文件名
                    public_path($faceImageUrl), // 相对于public的路径
                    $faceImageUrl, // 直接使用提供的路径
                ];
                
                // 检查文件是否存在
                $tempFaceFile = null;
                foreach ($faceFilePaths as $path) {
                    if (file_exists($path)) {
                        $tempFaceFile = $path;
                        Log::info('找到有效的人脸图像路径', ['path' => $path]);
                        break;
                    }
                }
                
                // 如果所有尝试都失败，记录详细信息并使用默认图像
                if ($tempFaceFile === null) {
                    Log::warning('无法找到人脸图像文件，尝试的路径有:', [
                        'original_url' => $faceImageUrl,
                        'attempted_paths' => $faceFilePaths
                    ]);
                    
                    // 使用默认测试图像
                    $tempFaceFile = storage_path('app/public/test_face.jpg');
                    if (!file_exists($tempFaceFile)) {
                        Log::warning('默认测试人脸图像也不存在，使用硬编码的替代URL');
                        $tempFaceFile = public_path('storage/test_face.jpg');
                    }
                }
            }
            
            // 获取蒙版路径
            $variantId = $config['variant_id'] ?? null;
            if ($variantId) {
                $variant = \App\Models\PicbookPageVariant::find($variantId);
                if ($variant) {
                    $maskUrl = $variant->face_config['mask_url'] ?? null;
                }
            }
            $maskImagePath = null;
            if ($maskUrl) {
                if (filter_var($maskUrl, FILTER_VALIDATE_URL)) {
                    $maskImagePath = storage_path('app/public/' . basename($maskUrl));
                } elseif (strpos($maskUrl, 'storage/') === 0) {
                    $maskImagePath = storage_path('app/public/' . substr($maskUrl, 8));
                } elseif (strpos($maskUrl, 'admin_uploads/') === 0) {
                    $maskImagePath = storage_path('app/public/' . $maskUrl);
                } elseif (strpos($maskUrl, 'public/imgs/') === 0) {
                    // 直接从public目录加载
                    $maskImagePath = base_path($maskUrl);
                    if (!file_exists($maskImagePath)) {
                        // 可能是相对于public目录的路径，去掉public/前缀尝试
                        $withoutPublic = str_replace('public/', '', $maskUrl);
                        $altPath = public_path($withoutPublic);
                        if (file_exists($altPath)) {
                            $maskImagePath = $altPath;
                            Log::info('在public目录找到蒙版图像(移除public/前缀)', ['path' => $maskImagePath]);
                        }
                    }
                } elseif (strpos($maskUrl, 'imgs/') === 0) {
                    // 处理imgs开头的路径
                    $maskImagePath = public_path($maskUrl);
                } else {
                    $maskImagePath = storage_path('app/public/' . $maskUrl);
                }
            }else{
                Log::info('没有蒙版图像');
            }
            
            Log::info('解析后的文件路径', [
                'input_image_path' => $inputImagePath,
                'mask_image_path' => $maskImagePath
            ]);
            
            // 尝试找到文件，如果主路径不存在
            if (!file_exists($inputImagePath)) {
                $alternativePaths = [
                    storage_path('app/' . $imageUrl),
                    public_path($imageUrl),
                    public_path('storage/' . $imageUrl),
                    storage_path('app/public/admin_uploads/' . basename($imageUrl)),
                    public_path('imgs/' . basename($imageUrl))
                ];
                
                foreach ($alternativePaths as $path) {
                    if (file_exists($path)) {
                        $inputImagePath = $path;
                        Log::info('找到有效的替代路径', ['valid_path' => $path]);
                        break;
                    }
                }
                
                if (!file_exists($inputImagePath)) {
                    // 无法找到图像时直接返回失败
                    Log::warning('找不到输入图像', [
                        'attempted_path' => $inputImagePath,
                        'original_url' => $imageUrl
                    ]);
                    return [
                        'success' => false,
                        'error' => '找不到输入图像: ' . $imageUrl,
                        'original_image_url' => $imageUrl
                    ];
                }
            }
            
            // 获取任务相关信息
            $batchId = $config['batch_id'] ?? null;
            $taskIndex = $config['task_index'] ?? null;
            $userId = $config['user_id'] ?? null;
            
            // 检查是否是批处理任务 
            $task = null;
            if ($batchId && $taskIndex !== null) {
                // 查找已存在的任务记录
                $task = \App\Models\AiFaceTask::where('batch_id', $batchId)
                    ->where('task_index', $taskIndex)
                    ->where('type', 'task')
                    ->first();
                
                if ($task) {
                    Log::info('找到现有AI换脸批次任务', [
                        'task_id' => $task->id,
                        'batch_id' => $batchId,
                        'task_index' => $taskIndex
                    ]);
                } else {
                    Log::warning('无法找到对应的AI换脸批次任务记录', [
                        'batch_id' => $batchId,
                        'task_index' => $taskIndex
                    ]);
                    return [
                        'success' => false,
                        'error' => '无法找到对应的任务记录',
                        'original_image_url' => $imageUrl
                    ];
                }
            } else if ($userId !== null) {
                // 只有在非批处理情况下且有有效用户ID时才创建新任务
                try {
                    $task = \App\Models\AiFaceTask::create([
                        'input_image' => $imageUrl,
                        'face_image' => 'temp_face_' . basename($tempFaceFile),
                        'mask_image' => $maskUrl ?? null,
                        'status' => 'pending',
                        'character_sequence' => $config['character_sequence'] ?? [],
                        'user_id' => $userId
                    ]);
                    
                    Log::info('成功创建AI换脸任务', [
                        'task_id' => $task->id,
                        'user_id' => $userId
                    ]);
                } catch (\Exception $dbException) {
                    Log::error('创建AI换脸任务记录失败', [
                        'error' => $dbException->getMessage()
                    ]);
                }
            }
            
            // 实际API调用
            try {
                // 获取API密钥
                $keyModel = \App\Models\AiApiKey::getAvailableKey();
                $apiKey = $keyModel->api_key;
                // 增加当前任务计数
                $keyModel->incrementTasks();
                
                Log::info('获取到API密钥，准备调用AI换脸API', [
                    'api_key_masked' => substr($apiKey, 0, 5) . '****' . substr($apiKey, -5),
                    'key_id' => $keyModel->id
                ]);
                
                // 上传输入图片
                $inputResponse = \Illuminate\Support\Facades\Http::attach(
                    'file', 
                    file_get_contents($inputImagePath), 
                    basename($inputImagePath)
                )->post('https://www.runninghub.cn/task/openapi/upload', [
                    'apiKey' => $apiKey,
                    'fileType' => 'image'
                ]);
                
                if (!$inputResponse->successful()) {
                    throw new \Exception('上传输入图片失败：' . $inputResponse->json('msg'));
                }
                $inputFileName = $inputResponse->json('data.fileName');
                
                // 上传人脸图片
                $faceResponse = \Illuminate\Support\Facades\Http::attach(
                    'file', 
                    file_get_contents($tempFaceFile), 
                    basename($tempFaceFile)
                )->post('https://www.runninghub.cn/task/openapi/upload', [
                    'apiKey' => $apiKey,
                    'fileType' => 'image'
                ]);
                
                if (!$faceResponse->successful()) {
                    throw new \Exception('上传人脸图片失败：' . $faceResponse->json('msg'));
                }
                $faceFileName = $faceResponse->json('data.fileName');
                
                // 处理蒙版图片
                $maskFileName = null;
                if ($maskImagePath && file_exists($maskImagePath)) {
                    $maskResponse = \Illuminate\Support\Facades\Http::attach(
                        'file', 
                        file_get_contents($maskImagePath), 
                        basename($maskImagePath)
                    )->post('https://www.runninghub.cn/task/openapi/upload', [
                        'apiKey' => $apiKey,
                        'fileType' => 'image'
                    ]);
                    
                    if (!$maskResponse->successful()) {
                        throw new \Exception('上传蒙版图片失败：' . $maskResponse->json('msg'));
                    }
                    $maskFileName = $maskResponse->json('data.fileName');
                }
                
                // 如果需要蒙版但没有找到，返回错误
                if (!$maskFileName && $maskUrl) {
                    return [
                        'success' => false,
                        'error' => '找不到蒙版图像',
                        'original_image_url' => $imageUrl
                    ];
                }
                
                // 创建任务请求参数
                $requestData = [
                    'workflowId' => config('services.ai_face.workflow_id'),
                    'apiKey' => $apiKey,
                    'nodeInfoList' => [
                        [
                            'nodeId' => "238",
                            'fieldName' => "image",
                            'fieldValue' => $inputFileName
                        ],
                        [
                            'nodeId' => "239",
                            'fieldName' => "image",
                            'fieldValue' => $maskFileName ?? $inputFileName // 如果没有蒙版，使用输入图像
                        ],
                        [
                            'nodeId' => "240",
                            'fieldName' => "image",
                            'fieldValue' => $faceFileName
                        ]
                    ]
                ];
                
                // 记录请求内容
                Log::info('AI换脸请求内容', [
                    'request_data' => array_merge($requestData, [
                        'apiKey' => substr($apiKey, 0, 4) . '****' . substr($apiKey, -4)
                    ])
                ]);
                
                // 发送创建任务请求
                $response = \Illuminate\Support\Facades\Http::post('https://www.runninghub.cn/task/openapi/create', $requestData);
                
                // 记录API响应
                Log::info('AI换脸API响应', [
                    'status' => $response->status(),
                    'body' => $response->json()
                ]);
                
                if ($response->successful() && $response->json('code') === 0) {
                    $thirdPartyTaskId = $response->json('data.taskId');
                    
                    // 更新任务记录
                    if ($task) {
                        // 存储API密钥ID，方便后续查询使用相同的API密钥
                        $task->update([
                            'task_id' => $thirdPartyTaskId,
                            'status' => 'processing',
                            'api_key_id' => $keyModel->id,
                            'target_image_url' => $imageUrl,
                            'face_image_url' => $faceImageUrl,
                            'mask_image_url' => $maskUrl
                        ]);
                        
                        Log::info('已更新AI换脸任务状态为处理中', [
                            'task_id' => $task->id,
                            'third_party_task_id' => $thirdPartyTaskId
                        ]);
                        
                        // 分发任务检查作业
                        \App\Jobs\CheckAiFaceTaskResult::dispatch($task)
                            ->delay(now()->addSeconds(5))
                            ->onQueue('polling');
                            
                        Log::info('已分发任务检查作业', [
                            'task_id' => $task->id,
                            'third_party_task_id' => $thirdPartyTaskId
                        ]);
                    }
                    
                    // 清理临时文件
                    if (isset($tempFaceFile) && strpos($tempFaceFile, 'temp_face_') !== false) {
                        @unlink($tempFaceFile);
                    }
                    
                    // 创建一个唯一的输出路径，表示处理中的图像
                    $outputPath = 'processed/face_swap_' . uniqid() . '.jpg';
                    
                    return [
                        'success' => true,
                        'processed_image_url' => Storage::faceswap()->url($outputPath),
                        'original_image_url' => $imageUrl,
                        'task_id' => $thirdPartyTaskId,
                        'status' => 'processing'
                    ];
                } else {
                    // 减少任务计数，因为任务创建失败
                    $keyModel->decrementTasks();
                    throw new \Exception($response->json('msg') ?? '创建AI换脸任务失败');
                }
            } catch (\Exception $apiException) {
                // 减少任务计数，因为API调用过程中出错
                if (isset($keyModel)) {
                    $keyModel->decrementTasks();
                }
                
                Log::error('API调用失败', [
                    'error' => $apiException->getMessage()
                ]);
                
                // 如果存在任务记录，更新其状态为失败
                if ($task) {
                    $task->update([
                        'status' => 'failed',
                        'error_message' => 'API调用失败: ' . $apiException->getMessage(),
                        'completed_at' => now()
                    ]);
                }
                
                // 直接返回失败
                return [
                    'success' => false,
                    'error' => $apiException->getMessage(),
                    'original_image_url' => $imageUrl
                ];
            }
        } catch (\Exception $e) {
            Log::error('AI换脸处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 如果创建了任务记录，更新其状态
            if (isset($task)) {
                $task->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                    'completed_at' => now()
                ]);
            }
            
            // 清理临时文件
            if (isset($tempFaceFile) && strpos($tempFaceFile, 'temp_face_') !== false) {
                @unlink($tempFaceFile);
            }
            
            // 直接返回失败
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'original_image_url' => $imageUrl
            ];
        }
    }
    
    /**
     * 将图像URL转换为Base64编码
     *
     * @param string $imageUrl 图像URL
     * @return string Base64编码的图像数据
     */
    private function getImageAsBase64($imageUrl)
    {
        // 判断是否已经是base64编码
        if (preg_match('/^data:image\/(\w+);base64,/', $imageUrl)) {
            return $imageUrl;
        }
        
        // 获取图像内容
        $imageContent = file_get_contents($imageUrl);
        if (!$imageContent) {
            throw new \Exception("无法获取图像内容: {$imageUrl}");
        }
        
        // 转换为Base64
        $base64 = base64_encode($imageContent);
        $mime = $this->getMimeType($imageUrl);
        
        return "data:{$mime};base64,{$base64}";
    }
    
    /**
     * 获取文件的MIME类型
     *
     * @param string $fileName 文件名或URL
     * @return string MIME类型
     */
    private function getMimeType($fileName)
    {
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $extension = strtolower($extension);
        
        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'bmp' => 'image/bmp',
            'webp' => 'image/webp'
        ];
        
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
    
    /**
     * 为变体执行AI换脸处理
     *
     * @param int $pageId 页面ID
     * @param int $variantId 变体ID
     * @param int|null $userId 用户ID
     * @param string|null $faceImage 人脸图像URL
     * @param string|null $batchId 批次ID
     * @return array 处理结果
     */
    public function swapFaceForVariant($pageId, $variantId, $userId = null, $faceImage = null, $batchId = null)
    {
        try {
            Log::info('开始为变体执行换脸', [
                'page_id' => $pageId,
                'variant_id' => $variantId,
                'user_id' => $userId,
                'has_face_image' => !empty($faceImage),
                'batch_id' => $batchId
            ]);
            
            // 尝试从数据库获取变体信息
            try {
                $variant = \App\Models\PicbookPageVariant::find($variantId);
                if ($variant) {
                    // 使用实际变体数据
                    $imageUrl = $variant->image_url;
                    $faceConfig = $variant->face_config;
                    
                    Log::info('成功获取变体信息', [
                        'variant_id' => $variantId,
                        'image_url' => $imageUrl
                    ]);
                } else {
                    // 变体不存在，返回失败
                    Log::warning('变体不存在', [
                        'page_id' => $pageId,
                        'variant_id' => $variantId
                    ]);
                    return [
                        'success' => false,
                        'error' => '找不到指定的变体: ' . $variantId
                    ];
                }
            } catch (\Exception $e) {
                // 数据库查询失败，返回失败
                Log::warning('获取变体数据失败', [
                    'error' => $e->getMessage(),
                    'page_id' => $pageId,
                    'variant_id' => $variantId
                ]);
                return [
                    'success' => false,
                    'error' => '获取变体数据失败: ' . $e->getMessage()
                ];
            }
            
            // 获取人脸图像
            $characterPhotoUrl = $faceImage;
            
            // 如果没有提供人脸图像，返回失败
            if (empty($characterPhotoUrl)) {
                Log::warning('未提供人脸图像', [
                    'page_id' => $pageId,
                    'variant_id' => $variantId
                ]);
                return [
                    'success' => false,
                    'error' => '未提供人脸图像'
                ];
            }
            
            // 准备处理参数
            $config = [
                'user_id' => $userId,
                'variant_id' => $variantId,
                'page_id' => $pageId
            ];
            
            // 如果有face_config，处理蒙版配置
            if (isset($faceConfig)) {
                // 如果存在蒙版URL，添加到配置中
                if (is_array($faceConfig) && isset($faceConfig['mask_url'])) {
                    $config['mask_url'] = $faceConfig['mask_url'];
                    Log::info('找到蒙版URL', [
                        'mask_url' => $faceConfig['mask_url']
                    ]);
                } else {
                    Log::info('面部配置中未找到蒙版URL', [
                        'face_config_type' => gettype($faceConfig),
                        'has_mask_url' => is_array($faceConfig) && isset($faceConfig['mask_url'])
                    ]);
                }
            } else {
                Log::warning('没有可用的face_config', [
                    'variant_id' => $variantId
                ]);
            }
            
            // 调用AI换脸处理
            $result = $this->applyFaceSwap($imageUrl, $characterPhotoUrl, $config);
            
            if ($result['success']) {
                Log::info('成功完成变体换脸处理', [
                    'variant_id' => $variantId,
                    'result_url' => $result['processed_image_url']
                ]);
                
                // 更新缓存结果，而不是直接更新变体
                if ($batchId) {
                    $cacheKey = "face_swap:result:{$batchId}:{$variantId}";
                    \Illuminate\Support\Facades\Cache::put(
                        $cacheKey, 
                        $result['processed_image_url'], 
                        now()->addDays(1)
                    );
                    
                    Log::info('已将换脸结果存入缓存', [
                        'cache_key' => $cacheKey,
                        'image_url' => $result['processed_image_url']
                    ]);
                }
                
                // 注意：不再更新变体记录，结果将通过广播机制传递给用户
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('执行变体换脸处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'page_id' => $pageId,
                'variant_id' => $variantId
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 合并两个图像
     *
     * @param string $baseImageUrl 基础图像URL
     * @param string $overlayImageUrl 叠加图像URL
     * @param int $x 叠加位置x坐标
     * @param int $y 叠加位置y坐标
     * @param float $opacity 不透明度 (0-1)
     * @return string 处理后的图像URL
     */
    public function mergeImages($baseImageUrl, $overlayImageUrl, $x = 0, $y = 0, $opacity = 1.0)
    {
        try {
            // 使用GD库加载基础图像
            $baseImage = $this->loadImage($baseImageUrl);
            if (!$baseImage) {
                throw new \Exception('无法加载基础图片: ' . $baseImageUrl);
            }
            
            // 加载叠加图像
            $overlayImage = $this->loadImage($overlayImageUrl);
            if (!$overlayImage) {
                throw new \Exception('无法加载叠加图片: ' . $overlayImageUrl);
            }
            
            // 获取图像尺寸
            $baseWidth = imagesx($baseImage);
            $baseHeight = imagesy($baseImage);
            $overlayWidth = imagesx($overlayImage);
            $overlayHeight = imagesy($overlayImage);
            
            // 创建新图像用于合并
            $result = imagecreatetruecolor($baseWidth, $baseHeight);
            
            // 保持透明度
            imagealphablending($result, false);
            imagesavealpha($result, true);
            $transparent = imagecolorallocatealpha($result, 0, 0, 0, 127);
            imagefilledrectangle($result, 0, 0, $baseWidth, $baseHeight, $transparent);
            
            // 复制基础图像
            imagecopy($result, $baseImage, 0, 0, 0, 0, $baseWidth, $baseHeight);
            
            // 设置叠加图像的不透明度
            if ($opacity < 1.0) {
                // 创建临时图像来应用不透明度
                $temp = imagecreatetruecolor($overlayWidth, $overlayHeight);
                imagealphablending($temp, false);
                imagesavealpha($temp, true);
                imagefilledrectangle($temp, 0, 0, $overlayWidth, $overlayHeight, $transparent);
                
                // 应用不透明度：逐像素处理
                for ($i = 0; $i < $overlayWidth; $i++) {
                    for ($j = 0; $j < $overlayHeight; $j++) {
                        $color = imagecolorat($overlayImage, $i, $j);
                        $alpha = ($color >> 24) & 0x7F; // 提取Alpha值
                        
                        // 应用不透明度
                        $newAlpha = min(127, $alpha + (1 - $opacity) * (127 - $alpha));
                        
                        $r = ($color >> 16) & 0xFF;
                        $g = ($color >> 8) & 0xFF;
                        $b = $color & 0xFF;
                        
                        $newColor = imagecolorallocatealpha($temp, $r, $g, $b, (int)$newAlpha);
                        imagesetpixel($temp, $i, $j, $newColor);
                    }
                }
                
                // 使用临时图像作为叠加图像
                $this->destroyImage($overlayImage);
                $overlayImage = $temp;
            }
            
            // 启用alpha混合来正确合并图像
            imagealphablending($result, true);
            
            // 叠加图像
            imagecopy($result, $overlayImage, $x, $y, 0, 0, $overlayWidth, $overlayHeight);
            
            // 保存处理后的图像
            $outputPath = 'processed/' . uniqid() . '_merged.png';
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'merge_');
            
            // 保存为PNG以保持透明度
            imagepng($result, $tempFile, 9);
            
            // 读取临时文件内容
            $imageContent = file_get_contents($tempFile);
            
            // 上传到S3 - 使用faceswap存储
            Storage::faceswap()->put($outputPath, $imageContent);
            
            // 删除临时文件
            @unlink($tempFile);
            
            // 释放资源
            $this->destroyImage($baseImage);
            $this->destroyImage($overlayImage);
            $this->destroyImage($result);
            
            Log::info('成功合并图像', ['output_path' => $outputPath]);
            
            // 返回处理后的图像URL
            return Storage::faceswap()->url($outputPath);
        } catch (\Exception $e) {
            Log::error('合并图像失败: ' . $e->getMessage());
            return $baseImageUrl; // 失败时返回原始图像URL
        }
    }
} 