<?php
/**
 * WebSocket 广播认证测试脚本
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use App\Models\User;

try {
    // 创建应用实例
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    
    echo "=== WebSocket 广播认证测试 ===\n\n";
    
    // 测试用户
    $userId = 7;
    $user = User::find($userId);
    
    if (!$user) {
        echo "错误: 找不到用户 ID {$userId}\n";
        exit(1);
    }
    
    echo "用户信息:\n";
    echo "- ID: {$user->id}\n";
    echo "- 邮箱: {$user->email}\n";
    echo "- 姓名: {".$user->name ?? 'N/A'."}\n\n";
    
    // 创建用户 token
    $token = $user->createToken('test-auth')->plainTextToken;
    echo "生成的 Token: {$token}\n\n";
    
    // 测试频道
    $channels = [
        "private-user.{$userId}",
        "user.{$userId}",
        "face-swap.{$userId}",
        "orders.{$userId}"
    ];
    
    foreach ($channels as $channelName) {
        echo "测试频道: {$channelName}\n";
        
        // 创建认证请求
        $request = Request::create('/api/broadcasting/auth', 'POST', [
            'channel_name' => $channelName
        ], [], [], [
            'HTTP_AUTHORIZATION' => 'Bearer ' . $token,
            'CONTENT_TYPE' => 'application/json',
            'HTTP_ACCEPT' => 'application/json'
        ]);
        
        // 处理请求
        $response = $kernel->handle($request);
        
        echo "- 状态码: {$response->getStatusCode()}\n";
        echo "- 响应内容: {$response->getContent()}\n";
        
        if ($response->getStatusCode() === 200) {
            echo "✅ 认证成功\n";
        } else {
            echo "❌ 认证失败\n";
        }
        
        echo "\n";
        
        // 重置应用状态
        $kernel->terminate($request, $response);
        $app = require_once __DIR__ . '/bootstrap/app.php';
        $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    }
    
    // 清理测试 token
    $user->tokens()->delete();
    echo "已清理测试 token\n";
    
    echo "=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "追踪:\n" . $e->getTraceAsString() . "\n";
}