<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\CartItem;
use App\Models\PicbookPreview;
use App\Models\Picbook;
use App\Models\Order;
use App\Models\OrderItem;
use App\Services\StripePaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;

class OrderTransactionTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $picbook;
    protected $preview;
    protected $cartItem;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create();
        
        // 创建测试绘本
        $this->picbook = Picbook::factory()->create([
            'price' => 29.99
        ]);
        
        // 创建测试预览
        $this->preview = PicbookPreview::factory()->create([
            'user_id' => $this->user->id,
            'picbook_id' => $this->picbook->id,
            'status' => 'completed'
        ]);
        
        // 创建测试购物车项
        $this->cartItem = CartItem::factory()->create([
            'user_id' => $this->user->id,
            'preview_id' => $this->preview->id,
            'quantity' => 1,
            'price' => 29.99,
            'total_price' => 29.99
        ]);
    }

    /**
     * 测试支付创建失败时的事务回滚
     */
    public function test_order_creation_rollback_when_payment_fails()
    {
        // 记录初始状态
        $initialOrderCount = Order::count();
        $initialOrderItemCount = OrderItem::count();
        $initialCartItemCount = CartItem::count();
        
        // Mock StripePaymentService 使其抛出异常
        $this->mock(StripePaymentService::class, function ($mock) {
            $mock->shouldReceive('createPaymentIntent')
                 ->andThrow(new \Exception('Stripe API Error: Invalid API key'));
        });

        // 认证用户
        $this->actingAs($this->user);

        // 发送订单创建请求
        $response = $this->postJson('/api/order/create', [
            'cart_item_ids' => [$this->cartItem->id],
            'payment_method' => 'stripe',
            'shipping_address' => [
                'first_name' => 'John',
                'phone' => '+1234567890',
                'country' => 'US',
                'city' => 'New York',
                'street' => '123 Main St'
            ]
        ]);

        // 验证响应
        $response->assertStatus(500);
        $response->assertJson([
            'success' => false,
            'message' => 'Stripe API Error: Invalid API key'
        ]);

        // 验证数据库状态 - 应该回滚到初始状态
        $this->assertEquals($initialOrderCount, Order::count(), '订单应该被回滚');
        $this->assertEquals($initialOrderItemCount, OrderItem::count(), '订单项应该被回滚');
        $this->assertEquals($initialCartItemCount, CartItem::count(), '购物车数据应该被保留');
        
        // 验证购物车项仍然存在
        $this->assertDatabaseHas('cart_items', [
            'id' => $this->cartItem->id,
            'user_id' => $this->user->id
        ]);
    }

    /**
     * 测试订单创建成功时购物车数据被正确删除
     */
    public function test_cart_items_deleted_when_order_creation_succeeds()
    {
        // Mock StripePaymentService 使其成功返回
        $mockPaymentIntent = (object) [
            'id' => 'pi_test_123',
            'client_secret' => 'pi_test_123_secret',
            'amount' => 2999,
            'currency' => 'usd',
            'status' => 'requires_payment_method'
        ];

        $this->mock(StripePaymentService::class, function ($mock) use ($mockPaymentIntent) {
            $mock->shouldReceive('createPaymentIntent')
                 ->andReturn($mockPaymentIntent);
        });

        // 认证用户
        $this->actingAs($this->user);

        // 发送订单创建请求
        $response = $this->postJson('/api/order/create', [
            'cart_item_ids' => [$this->cartItem->id],
            'payment_method' => 'stripe',
            'shipping_address' => [
                'first_name' => 'John',
                'phone' => '+1234567890',
                'country' => 'US',
                'city' => 'New York',
                'street' => '123 Main St'
            ]
        ]);

        // 验证响应
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);

        // 验证订单和订单项被创建
        $this->assertEquals(1, Order::count(), '应该创建一个订单');
        $this->assertEquals(1, OrderItem::count(), '应该创建一个订单项');
        
        // 验证购物车项被删除
        $this->assertEquals(0, CartItem::count(), '购物车应该被清空');
        $this->assertDatabaseMissing('cart_items', [
            'id' => $this->cartItem->id
        ]);
    }

    /**
     * 测试部分购物车项的事务处理
     */
    public function test_partial_cart_items_transaction_rollback()
    {
        // 创建第二个购物车项
        $cartItem2 = CartItem::factory()->create([
            'user_id' => $this->user->id,
            'preview_id' => $this->preview->id,
            'quantity' => 2,
            'price' => 29.99,
            'total_price' => 59.98
        ]);

        $initialCartItemCount = CartItem::count(); // 应该是2

        // Mock StripePaymentService 使其失败
        $this->mock(StripePaymentService::class, function ($mock) {
            $mock->shouldReceive('createPaymentIntent')
                 ->andThrow(new \Exception('Payment processing failed'));
        });

        // 认证用户
        $this->actingAs($this->user);

        // 只选择第一个购物车项创建订单
        $response = $this->postJson('/api/order/create', [
            'cart_item_ids' => [$this->cartItem->id], // 只选择第一个
            'payment_method' => 'stripe',
            'shipping_address' => [
                'first_name' => 'John',
                'phone' => '+1234567890',
                'country' => 'US',
                'city' => 'New York',
                'street' => '123 Main St'
            ]
        ]);

        // 验证响应
        $response->assertStatus(500);

        // 验证两个购物车项都还在
        $this->assertEquals($initialCartItemCount, CartItem::count(), '所有购物车项都应该被保留');
        $this->assertDatabaseHas('cart_items', ['id' => $this->cartItem->id]);
        $this->assertDatabaseHas('cart_items', ['id' => $cartItem2->id]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
