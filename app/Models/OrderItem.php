<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrderItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'order_id',
        'preview_id',
        'picbook_id',
        'quantity',
        'price',
        'cover_price',
        'binding_price',
        'gift_box_price',
        'total_price',
        'recipient_name',
        'message',
        'cover_type',
        'binding_type',
        'gift_box',
        'face_image',
        'result_images',
        'face_swap_batch_id',
        'processing_progress',
        'status'
    ];

    protected $casts = [
        'price' => 'float',
        'cover_price' => 'float',
        'binding_price' => 'float',
        'gift_box_price' => 'float',
        'total_price' => 'float',
        'quantity' => 'integer',
        'gift_box' => 'boolean',
        'result_images' => 'array',
        'processing_progress' => 'integer'
    ];

    /**
     * 订单项状态常量
     */
    const STATUS_PENDING = 'pending';       // 待处理
    const STATUS_PROCESSING = 'processing'; // 处理中
    // const STATUS_PRINTED = 'printed';       // 已打印
    // const STATUS_PACKED = 'packed';         // 已包装
    const STATUS_SHIPPED = 'shipped';       // 已发货
    const STATUS_COMPLETED = 'completed';   // 已完成
    const STATUS_CANCELLED = 'cancelled';   // 已取消
    const STATUS_RETURNED = 'returned';     // 已退货

    /**
     * 关联订单模型
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 关联绘本模型
     */
    public function picbook()
    {
        return $this->belongsTo(Picbook::class);
    }

    /**
     * 关联预览模型
     */
    public function preview()
    {
        return $this->belongsTo(PicbookPreview::class, 'preview_id');
    }

    /**
     * 计算总价
     */
    public function calculateTotal()
    {
        $this->total_price = ($this->price + $this->cover_price + $this->binding_price + $this->gift_box_price) * $this->quantity;
        $this->save();
        return $this->total_price;
    }

    /**
     * 从购物车项创建订单项
     *
     * @param CartItem $cartItem
     * @param int $orderId
     * @return OrderItem
     */
    public static function createFromCartItem(CartItem $cartItem, int $orderId)
    {
        $preview = $cartItem->preview;
        
        $orderItem = new self([
            'order_id' => $orderId,
            'preview_id' => $cartItem->preview_id,
            'picbook_id' => $preview->picbook_id,
            'quantity' => $cartItem->quantity,
            'price' => $cartItem->price,
            'cover_price' => $cartItem->cover_price,
            'binding_price' => $cartItem->binding_price,
            'gift_box_price' => $cartItem->gift_box_price,
            'total_price' => $cartItem->total_price,
            'recipient_name' => $preview->recipient_name,
            'message' => $preview->message,
            'cover_type' => $preview->cover_type,
            'binding_type' => $preview->binding_type,
            'gift_box' => $preview->gift_box,
            'face_image' => $preview->face_image,
            'result_images' => $preview->result_images,
            'status' => self::STATUS_PENDING
        ]);
        
        $orderItem->save();
        return $orderItem;
    }

    /**
     * 设置订单项状态
     * 
     * @param string $status
     * @return void
     */
    public function setStatus($status)
    {
        $this->status = $status;
        $this->save();
    }
} 