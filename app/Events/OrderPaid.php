<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\Order;

class OrderPaid implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 订单对象
     */
    public $order;

    /**
     * 创建新的事件实例
     *
     * @param Order $order 订单对象
     * @return void
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * 获取事件应该广播的频道
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('orders.' . $this->order->user_id);
    }
    
    /**
     * 获取广播事件的名称
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'order.paid';
    }
    
    /**
     * 获取广播数据
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'total' => $this->order->total,
            'status' => $this->order->status,
            'payment_status' => $this->order->payment_status,
            'paid_at' => $this->order->paid_at ? $this->order->paid_at->toDateTimeString() : null
        ];
    }
} 