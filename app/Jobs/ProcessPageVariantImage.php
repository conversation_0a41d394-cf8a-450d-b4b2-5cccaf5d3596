<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\PicbookPage;
use App\Models\PicbookPageVariant;
use App\Services\ImageService;
use App\Services\PicbookImageProcessor;

class ProcessPageVariantImage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $variantId;
    protected $baseImageUrl;
    protected $characterMasks;
    protected $skinColorCodes;
    protected $picbookId;
    protected $pageId;
    protected $batchId;
    
    /**
     * 重试次数
     */
    public $tries = 3;
    
    /**
     * 超时时间（秒）
     */
    public $timeout = 300;

    /**
     * 创建新的任务实例
     * 
     * @param int $variantId 变体ID
     * @param string $baseImageUrl 基础图片URL
     * @param array $characterMasks 角色蒙版配置
     * @param array $skinColorCodes 肤色代码
     * @param int $picbookId 绘本ID
     * @param int $pageId 页面ID
     * @param string $batchId 批次ID
     */
    public function __construct(
        $variantId,
        $baseImageUrl,
        $characterMasks,
        $skinColorCodes,
        $picbookId,
        $pageId,
        $batchId
    ) {
        $this->variantId = $variantId;
        $this->baseImageUrl = $baseImageUrl;
        $this->characterMasks = $characterMasks;
        $this->skinColorCodes = $skinColorCodes;
        $this->picbookId = $picbookId;
        $this->pageId = $pageId;
        $this->batchId = $batchId;
        
        Log::debug('任务构造函数已执行', [
            'variant_id' => $this->variantId,
            'batch_id' => $this->batchId
        ]);
    }

    /**
     * 执行任务
     */
    public function handle()
    {
        $startTime = microtime(true);
        
        try {
            // 记录接收到的参数
            Log::debug('开始处理变体图片任务', [
                'variant_id' => $this->variantId,
                'batch_id' => $this->batchId,
                'picbook_id' => $this->picbookId,
                'page_id' => $this->pageId,
                'skin_color_codes' => $this->skinColorCodes,
                'has_character_masks' => !empty($this->characterMasks)
            ]);
            
            $variant = PicbookPageVariant::find($this->variantId);
            if (!$variant) {
                Log::error('处理变体图片失败: 找不到指定的变体', [
                    'variant_id' => $this->variantId,
                    'batch_id' => $this->batchId
                ]);
                return;
            }
            
            // 获取页面
            $page = PicbookPage::find($variant->page_id);
            if (!$page) {
                Log::error('处理变体图片失败: 找不到指定的页面', [
                    'variant_id' => $this->variantId,
                    'page_id' => $variant->page_id,
                    'batch_id' => $this->batchId
                ]);
                return;
            }
            // 获取图像服务
            $this->imageService = app(ImageService::class);
            
            // 步骤1: 加载并处理肤色图像
            Log::info('步骤1: 加载肤色图像', [
                'variant_id' => $this->variantId,
                'image_url' => $variant->skin_mask_url
            ]);
            
            // 使用新的ImageService加载肤色图像
            $skinColorImage = $this->imageService->loadImage($variant->skin_mask_url);
            if (!$skinColorImage) {
                Log::error('处理变体图片失败: 无法加载肤色图像', [
                    'variant_id' => $this->variantId,
                    'image_url' => $variant->skin_mask_url,
                    'batch_id' => $this->batchId
                ]);
                $this->updateBatchProgress(false);
                return;
            }
            
            // 记录肤色图像尺寸
            $skinWidth = imagesx($skinColorImage);
            $skinHeight = imagesy($skinColorImage);
            Log::info('肤色图像加载成功', [
                'variant_id' => $this->variantId,
                'dimensions' => "{$skinWidth}x{$skinHeight}"
            ]);
            
            // 步骤2: 使用新的ImageService合并图像
            Log::info('步骤2: 合并无肤色图像', [
                'variant_id' => $this->variantId,
                'no_skin_url' => $this->baseImageUrl,
                'masks_count' => count($this->characterMasks)
            ]);
            
            // 使用新的合并方法
            $startMergeTime = microtime(true);
            $mergedImage = $this->imageService->mergeNoSkinImageOntoSkinImage(
                $skinColorImage,
                $this->baseImageUrl,
                $this->characterMasks
            );
            $mergeTime = round((microtime(true) - $startMergeTime) * 1000, 2);
            
            if (!$mergedImage) {
                // 释放资源
                if ($skinColorImage) {
                    $this->imageService->destroyImage($skinColorImage);
                }
                
                Log::error('处理变体图片失败: 合并图像失败', [
                    'variant_id' => $this->variantId,
                    'batch_id' => $this->batchId
                ]);
                $this->updateBatchProgress(false);
                return;
            }
            
            // 记录合并后图像尺寸和处理时间
            $mergedWidth = imagesx($mergedImage);
            $mergedHeight = imagesy($mergedImage);
            Log::info('图像合并成功', [
                'variant_id' => $this->variantId,
                'dimensions' => "{$mergedWidth}x{$mergedHeight}",
                'merge_time_ms' => $mergeTime
            ]);
            
            // 步骤3: 保存处理后的图片
            Log::info('步骤3: 保存处理后的图像', [
                'variant_id' => $this->variantId
            ]);
            
            // 保存处理后的图片
            $outputPath = $this->imageService->saveProcessedImage($mergedImage, "variant_{$this->variantId}");
            
            // 释放资源
            $this->imageService->destroyImage($skinColorImage);
            $this->imageService->destroyImage($mergedImage);
            
            if (!$outputPath) {
                Log::error('处理变体图片失败: 保存图像失败', [
                    'variant_id' => $this->variantId,
                    'batch_id' => $this->batchId
                ]);
                $this->updateBatchProgress(false);
                return;
            }
            
            // 更新变体记录
            $variant->image_url = $outputPath;
            $variant->processed_image_url = $outputPath;
            $variant->save();
            
            // 记录处理时间
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);
            
            Log::info('成功处理变体图片', [
                'variant_id' => $this->variantId,
                'output_path' => $outputPath,
                'processing_time_ms' => $processingTime,
                'batch_id' => $this->batchId
            ]);
            
            // 更新批次进度
            $this->updateBatchProgress(true);
        } catch (\Exception $e) {
            Log::error('处理变体图片出现异常', [
                'variant_id' => $this->variantId,
                'batch_id' => $this->batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 更新批次进度
            $this->updateBatchProgress(false);
        }
    }
    
    /**
     * 更新批次处理进度
     */
    private function updateBatchProgress($isSuccess)
    {
        if (!$this->batchId) {
            return;
        }
        
        try {
            // 获取批次信息
            $batchInfo = \Illuminate\Support\Facades\Cache::get("picbook:batch:{$this->batchId}", null);
            if (!$batchInfo) {
                Log::warning('无法更新批次进度：找不到批次信息', [
                    'batch_id' => $this->batchId
                ]);
                return;
            }
            
            // 更新完成计数
            $completed = \Illuminate\Support\Facades\Cache::increment("picbook:batch:{$this->batchId}:completed");
            $total = $batchInfo['total_tasks'] ?? 0;
            $completedSuccessfully = $isSuccess 
                ? \Illuminate\Support\Facades\Cache::increment("picbook:batch:{$this->batchId}:succeeded")
                : $batchInfo['succeeded'] ?? 0;
            
            // 计算进度百分比
            $progress = $total > 0 ? round(($completed / $total) * 100) : 0;
            
            // 更新批次状态
            \Illuminate\Support\Facades\Cache::put("picbook:batch:{$this->batchId}:progress", $progress, now()->addDay());
            \Illuminate\Support\Facades\Cache::put("picbook:batch:{$this->batchId}:status", ($completed >= $total) ? 'completed' : 'processing', now()->addDay());
            
            Log::info('更新批次进度', [
                'batch_id' => $this->batchId,
                'completed' => $completed,
                'total' => $total,
                'progress' => $progress,
                'succeeded' => $completedSuccessfully,
                'current_variant' => $this->variantId
            ]);
            
            // 检查是否所有任务都已完成
            if ($completed >= $total) {
                Log::info('批次处理完成', [
                    'batch_id' => $this->batchId,
                    'total_tasks' => $total,
                    'succeeded_tasks' => $completedSuccessfully
                ]);
                
                // 发布批次完成事件
                if (class_exists('\App\Events\PicbookBatchCompleted')) {
                    event(new \App\Events\PicbookBatchCompleted($this->batchId, $completedSuccessfully, $total));
                }
            }
        } catch (\Exception $e) {
            Log::error('更新批次进度失败', [
                'batch_id' => $this->batchId,
                'error' => $e->getMessage()
            ]);
        }
    }
} 