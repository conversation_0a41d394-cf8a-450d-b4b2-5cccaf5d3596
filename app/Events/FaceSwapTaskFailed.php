<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\AiFaceTask;

class FaceSwapTaskFailed implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $task;
    public $userId;

    /**
     * Create a new event instance.
     */
    public function __construct(AiFaceTask $task)
    {
        $this->task = $task;
        $this->userId = $task->user_id;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->userId)
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'face-swap.task.failed';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'type' => 'face_swap_task_failed',
            'batch_id' => $this->task->batch_id,
            'task_id' => $this->task->id,
            'page_id' => $this->task->page_id,
            'variant_id' => $this->task->variant_id,
            'error_message' => $this->task->error_message,
            'timestamp' => now()->toISOString()
        ];
    }
}