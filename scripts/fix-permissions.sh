#!/bin/bash

# Laravel 权限修复脚本
set -e

echo "🔧 修复 Laravel 文件权限..."

# 获取当前用户和 Web 服务器用户
CURRENT_USER=$(whoami)
WEB_USER="www"

# 检查是否为 root 用户
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  检测到 root 用户，将使用 sudo 命令"
    SUDO_CMD=""
else
    echo "👤 当前用户: $CURRENT_USER，将使用 sudo 命令"
    SUDO_CMD="sudo"
fi

# 修复 storage 目录权限
echo "📁 修复 storage/ 目录权限..."
$SUDO_CMD chown -R $WEB_USER:$WEB_USER storage/
$SUDO_CMD chmod -R 775 storage/

# 修复 bootstrap/cache 目录权限
echo "📁 修复 bootstrap/cache/ 目录权限..."
$SUDO_CMD chown -R $WEB_USER:$WEB_USER bootstrap/cache/
$SUDO_CMD chmod -R 775 bootstrap/cache/

# 修复 public 目录权限（如果存在上传文件）
if [ -d "public/storage" ]; then
    echo "📁 修复 public/storage/ 目录权限..."
    $SUDO_CMD chown -R $WEB_USER:$WEB_USER public/storage/
    $SUDO_CMD chmod -R 775 public/storage/
fi

# 修复 public/processed 目录权限（换脸处理后的图片）
if [ -d "public/processed" ]; then
    echo "📁 修复 public/processed/ 目录权限..."
    $SUDO_CMD chown -R $WEB_USER:$WEB_USER public/processed/
    $SUDO_CMD chmod -R 775 public/processed/
fi

# 确保日志文件存在且有正确权限
echo "📝 确保日志文件权限..."
if [ ! -f "storage/logs/laravel.log" ]; then
    $SUDO_CMD touch storage/logs/laravel.log
fi
$SUDO_CMD chown $WEB_USER:$WEB_USER storage/logs/laravel.log
$SUDO_CMD chmod 664 storage/logs/laravel.log

# 确保缓存文件有正确权限
echo "💾 确保缓存文件权限..."
if [ -f "bootstrap/cache/config.php" ]; then
    $SUDO_CMD chown $WEB_USER:$WEB_USER bootstrap/cache/config.php
    $SUDO_CMD chmod 664 bootstrap/cache/config.php
fi

if [ -f "bootstrap/cache/routes.php" ]; then
    $SUDO_CMD chown $WEB_USER:$WEB_USER bootstrap/cache/routes.php
    $SUDO_CMD chmod 664 bootstrap/cache/routes.php
fi

if [ -f "bootstrap/cache/views.php" ]; then
    $SUDO_CMD chown $WEB_USER:$WEB_USER bootstrap/cache/views.php
    $SUDO_CMD chmod 664 bootstrap/cache/views.php
fi

echo "✅ 权限修复完成！"

# 显示关键目录的权限状态
echo ""
echo "📊 当前权限状态："
echo "storage/:"
ls -la storage/ | head -5

echo ""
echo "bootstrap/cache/:"
ls -la bootstrap/cache/

echo ""
echo "🎉 权限修复脚本执行完成！"