<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\SimpleFaceSwapService;

class ProcessSimpleFaceSwapTask implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务尝试次数
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 300;

    /**
     * 任务ID
     */
    protected $taskId;

    /**
     * 创建任务实例
     * 
     * @param int $taskId 任务ID
     */
    public function __construct(int $taskId)
    {
        $this->taskId = $taskId;
    }

    /**
     * 执行任务
     * 
     * @param SimpleFaceSwapService $faceSwapService 换脸服务
     */
    public function handle(SimpleFaceSwapService $faceSwapService)
    {
        try {
            Log::info('开始处理简化版AI换脸任务', [
                'task_id' => $this->taskId
            ]);

            // 处理任务
            $result = $faceSwapService->processTask($this->taskId);

            Log::info('简化版AI换脸任务处理完成', [
                'task_id' => $this->taskId,
                'success' => $result['success']
            ]);
            
        } catch (\Exception $e) {
            Log::error('处理简化版AI换脸任务失败', [
                'task_id' => $this->taskId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
} 