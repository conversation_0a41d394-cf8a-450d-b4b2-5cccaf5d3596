<?php

namespace App\Services;

use App\Models\Picbook;
use App\Models\PicbookPreview;
use App\Events\AiFaceTaskCompleted;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;

class PreviewService
{
    /**
     * 图像处理相关服务
     */
    protected $picbookImageProcessor;
    protected $imageService;
    protected $faceSwapService;
    
    /**
     * 构造函数
     */
    public function __construct(
        PicbookImageProcessor $picbookImageProcessor, 
        ImageService $imageService,
        FaceSwapService $faceSwapService
    ) {
        $this->picbookImageProcessor = $picbookImageProcessor;
        $this->imageService = $imageService;
        $this->faceSwapService = $faceSwapService;
    }

    /**
     * 创建预览 - 统一处理预览和换脸逻辑
     *
     * @param Picbook $picbook 绘本对象
     * @param \Illuminate\Database\Eloquent\Collection $previewPages 预览页面集合
     * @param array $characters 角色数据
     * @param Request $request 请求对象
     * @return array 处理结果
     */
    public function createPreview(Picbook $picbook, $previewPages, array $characters, Request $request)
    {
        $userId = Auth::id();
        
        // 1. 首先检查是否已达到今日预览上限
        if (PicbookPreview::hasReachedDailyLimit($userId, $picbook->id)) {
            return [
                'success' => false,
                'message' => __('picbook.preview_limit_reached')
            ];
        }
        
        // 准备预览选项
        $previewOptions = [];
        if ($request->has('recipient_name')) $previewOptions['recipient_name'] = $request->input('recipient_name');
        if ($request->has('message')) $previewOptions['message'] = $request->input('message');
        if ($request->has('cover_type')) $previewOptions['cover_type'] = $request->input('cover_type');
        if ($request->has('binding_type')) $previewOptions['binding_type'] = $request->input('binding_type');
        if ($request->has('gift_box')) $previewOptions['gift_box'] = $request->input('gift_box');
        
        $result = [
            'success' => true,
            'message' => __('picbook.preview_success'),
            'pages' => []
        ];
        
        $language = '';
        if (isset($characters[0]['language'])) {
            $language = $characters[0]['language'];
        }
        
        $character_skincolors = [];
        foreach ($characters as $character) {
            if (isset($character['skincolor'])) {
                $character_skincolors[] = $character['skincolor'];
            }
        }
        
        // 处理预览页面
        $face_data = []; // 确保变量初始化
        foreach ($previewPages as $page) {
            // 获取匹配的变体
            $variant = $page->variants()
                ->where('language', $language)
                ->where('gender', $characters[0]['gender'] ?? null)
                ->whereJsonContains('character_skincolors', $character_skincolors)
                ->first();
            if (!$variant) {
                // 如果没有找到匹配的变体，报警提醒
                Log::warning('没有找到匹配的变体', [
                    'page_id' => $page->id,
                    'page_number' => $page->page_number,
                    'language' => $language,
                    'gender' => $characters[0]['gender'] ?? null,
                    'character_skincolors' => $character_skincolors
                ]);
                continue;
            }
            
            if ($variant) {
                // 处理变体
                $variantResult = $this->picbookImageProcessor->processPageVariantWithMultipleCharacters($variant, $characters);
                if ($variantResult['success']) {
                    $result['pages'][] = [
                        'page_id' => $page->id,
                        'page_number' => $page->page_number,
                        'has_question' => $page->has_question ?? false,
                        'has_choice' => $page->is_choices ?? false,
                        'choice_type' => $page->choice_type ?? 0,
                        'image_url' => $variantResult['image_url'],
                        'content' => $variantResult['content'] ?? $variant->content,
                        'question' => $variantResult['question'] ?? $variant->question,
                        'choice_options' => $variant->choice_options
                    ];
                }
                
                //记录需要换脸的预览页
                if ($variant->has_face && !empty($variant->face_config) && !empty($characters[0]['photo'])) {
                    $face_data[] = [
                        'page_id' => $page->id,
                        'page_number' => $page->page_number,
                        'variant_id' => $variant->id,
                        'target_image_url' => $variant->image_url
                    ];
                }
            }
        }
        
        // 处理AI换脸
        if (!empty($face_data)) {
            try {
                // 检查是否是优先级任务
                $isPriority = $request->boolean('is_priority', false);
                
                // 使用新的FaceSwapService创建批次
                $faceSwapResult = $this->faceSwapService->createBatch(
                    $face_data, 
                    $characters[0]['photo'], 
                    $userId, 
                    $isPriority
                );
                
                // 检查AI换脸批次是否创建成功
                if (!$faceSwapResult['success']) {
                    // 如果AI换脸批次创建失败，则返回错误，阻止预览继续创建
                    Log::error('AI换脸批次创建失败', [
                        'error' => $faceSwapResult['message'] ?? '未知错误',
                        'user_id' => $userId,
                        'picbook_id' => $picbook->id
                    ]);
                    
                    return [
                        'success' => false,
                        'message' => '创建AI换脸批次失败: ' . ($faceSwapResult['message'] ?? '未知错误')
                    ];
                }
                
                // 将批次ID添加到结果中
                if (!empty($faceSwapResult['batch_id'])) {
                    // 获取Redis队列长度
                    $redis = Redis::connection();
                    $regularQueueLength = $redis->llen('queues:face_swap');
                    $priorityQueueLength = $redis->llen('queues:high_priority_face_swap');
                    
                    $queuePosition = $isPriority ? $priorityQueueLength : $regularQueueLength;
                    $queueType = $isPriority ? 'high_priority' : 'regular';
                    
                    // 预计等待时间（平均每个任务30秒）
                    $estimatedWaitTime = max(1, $queuePosition) * 60;
                    
                    $result['face_swap_batch'] = [
                        'batch_id' => $faceSwapResult['batch_id'],
                        'total_pages' => count($faceSwapResult['task_ids']),
                        'status' => 'pending',
                        'queue_position' => $queuePosition,
                        'estimated_wait_time' => $estimatedWaitTime,
                        'queue_type' => $queueType
                    ];
                    
                    // 记录批次信息到缓存（用于恢复通知）
                    Cache::put(
                        "user:{$userId}:face_swap:notifications:batch:{$faceSwapResult['batch_id']}", 
                        [
                            'batch_id' => $faceSwapResult['batch_id'],
                            'picbook_id' => $picbook->id,
                            'picbook_title' => $picbook->default_name ?? 'Picbook',
                            'created_at' => now()->toDateTimeString(),
                            'status' => 'pending',
                            'read' => false,
                            'queue_position' => $queuePosition,
                            'estimated_wait_time' => $estimatedWaitTime
                        ], 
                        now()->addDays(7)
                    );
                    
                    // 记录任务开始信息
                    Log::info('创建AI换脸任务批次', [
                        'user_id' => $userId,
                        'batch_id' => $faceSwapResult['batch_id'],
                        'is_priority' => $isPriority,
                        'queue_position' => $queuePosition,
                        'estimated_wait_time' => $estimatedWaitTime
                    ]);
                    
                    // 广播队列状态
                    if (class_exists('\App\Events\AiFaceQueueStatus')) {
                        event(new \App\Events\AiFaceQueueStatus(
                            $userId,
                            $regularQueueLength,
                            $priorityQueueLength,
                            $queueType,
                            $queuePosition,
                            $estimatedWaitTime
                        ));
                    }
                }
            } catch (\Exception $e) {
                Log::error('AI换脸请求异常', [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                // AI换脸失败时，整个预览也应该失败
                return [
                    'success' => false,
                    'message' => 'AI换脸处理失败: ' . $e->getMessage()
                ];
            }
        }
        
        if (count($result['pages']) > 0) {
            $response = [
                'pages' => $result['pages'],
                'characters' => $characters
            ];
            
            // 添加批次信息(如果存在)
            if (isset($result['face_swap_batch'])) {
                $response['face_swap_batch'] = $result['face_swap_batch'];
                // 转换为JSON以便保存
                $face_swap_batch_json = $result['face_swap_batch'];
            } else {
                $face_swap_batch_json = null;
            }
            
            // 保存预览记录，包括最新的批次信息
            try {
                $preview = new PicbookPreview();
                $preview->user_id = $userId;
                $preview->picbook_id = $picbook->id;
                $preview->gender = $characters[0]['gender'] ?? null;
                $preview->language = $language;
                $preview->skin_color = $character_skincolors;
                $preview->face_image = $characters[0]['photo'] ?? null;
                $preview->options = $previewOptions;
                $preview->characters = $characters;
                $preview->status = PicbookPreview::STATUS_COMPLETED;
                $preview->face_swap_batch = $face_swap_batch_json;
                
                // 如果存在批次ID，更新相关字段
                if (isset($result['face_swap_batch']) && !empty($result['face_swap_batch']['batch_id'])) {
                    $preview->batch_id = $result['face_swap_batch']['batch_id'];
                    $preview->status = PicbookPreview::STATUS_PENDING;
                }
                
                $preview->save();
                
                // 将预览ID添加到响应中
                $response['preview_id'] = $preview->id;
                
                // 记录成功创建预览
                Log::info('成功创建预览', [
                    'preview_id' => $preview->id,
                    'user_id' => $userId,
                    'picbook_id' => $picbook->id,
                    'pages_count' => count($result['pages']),
                    'batch_id' => $preview->batch_id ?? null
                ]);
            } catch (\Exception $e) {
                Log::error('保存预览记录失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
            
            return [
                'success' => true,
                'message' => $result['message'],
                'data' => $response
            ];
        } else {
            return [
                'success' => false,
                'message' => __('picbook.preview_failed'),
                'error' => '没有可用的预览页面'
            ];
        }
    }
    
    /**
     * 更新预览结果
     *
     * @param string $batchId 批次ID
     * @param array $resultImages 结果图片URL数组
     * @param string $status 处理状态
     * @return array 处理结果
     */
    public function updatePreviewResult(string $batchId, array $resultImages, string $status = 'completed')
    {
        try {
            // 查找对应的预览记录
            $preview = PicbookPreview::where('batch_id', $batchId)->first();
            
            if (!$preview) {
                Log::warning('找不到批次对应的预览记录', ['batch_id' => $batchId]);
                return ['success' => false, 'message' => '找不到批次对应的预览记录'];
            }
            
            Log::info('开始更新预览结果', [
                'preview_id' => $preview->id,
                'batch_id' => $batchId,
                'status' => $status,
                'result_count' => count($resultImages),
                'result_images' => $resultImages,
                'current_status' => $preview->status
            ]);
            
            // 更新预览状态
            $newStatus = $status === 'completed' ? PicbookPreview::STATUS_COMPLETED : PicbookPreview::STATUS_FAILED;
            $preview->status = $newStatus;
            $preview->result_images = $resultImages;
            
            // 同时更新face_swap_batch中的状态
            if ($preview->face_swap_batch) {
                $faceSwapBatch = $preview->face_swap_batch;
                $faceSwapBatch['status'] = $status;
                $preview->face_swap_batch = $faceSwapBatch;
            }
            
            // 记录更新前的状态
            $beforeSave = [
                'id' => $preview->id,
                'status' => $preview->status,
                'result_images' => $preview->result_images,
                'face_swap_batch' => $preview->face_swap_batch
            ];
            
            // 执行保存
            $saveResult = $preview->save();
            
            // 确认预览已正确保存
            $freshPreview = PicbookPreview::find($preview->id);
            
            Log::info('预览结果已更新', [
                'preview_id' => $preview->id,
                'batch_id' => $batchId,
                'before_status' => $beforeSave['status'],
                'after_status' => $freshPreview->status,
                'save_result' => $saveResult,
                'result_count' => count($freshPreview->result_images ?? [])
            ]);
            
            return [
                'success' => true,
                'message' => '成功更新预览结果',
                'preview_id' => $preview->id
            ];
        } catch (\Exception $e) {
            Log::error('更新预览结果失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return ['success' => false, 'message' => '更新预览结果失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 获取用户预览列表
     *
     * @param int $userId 用户ID
     * @param int|null $picbookId 可选的绘本ID
     * @return \Illuminate\Database\Eloquent\Collection 预览列表
     */
    public function getUserPreviews(int $userId, ?int $picbookId = null)
    {
        $query = PicbookPreview::where('user_id', $userId)
            ->orderBy('created_at', 'desc');
            
        if ($picbookId) {
            $query->where('picbook_id', $picbookId);
        }
        
        return $query->get();
    }
    
    /**
     * 分派AI换脸任务并更新预览批次ID
     *
     * @param PicbookPreview $preview 预览记录
     * @param array $pages 需要处理的页面
     * @param bool $isPriority 是否为优先任务
     * @return string 批次ID
     */
    public function dispatchFaceSwapTask(PicbookPreview $preview, array $pages, bool $isPriority = false)
    {
        // 调用FaceSwapService创建批次
        $result = $this->faceSwapService->createBatch(
            $pages, 
            $preview->face_image, 
            $preview->user_id, 
            $isPriority
        );
        
        if ($result['success'] && $result['batch_id']) {
            // 更新预览记录的批次ID
            $preview->batch_id = $result['batch_id'];
            $preview->status = PicbookPreview::STATUS_PENDING;
            $preview->save();
            
            Log::info('更新预览批次ID', [
                'preview_id' => $preview->id,
                'batch_id' => $result['batch_id']
            ]);
        }
        
        return $result['batch_id'] ?? '';
    }
    
    /**
     * 获取预览选项列表
     *
     * @param int|null $picbookId 绘本ID
     * @param string|null $language 语言代码
     * @return array
     */
    public function getPreviewOptions(?int $picbookId = null, ?string $language = null)
    {
        $language = $language ?? app()->getLocale() ?? 'en';
        // 封面选项从 picbook_cover_variants 表获取
        $coverOptions = [];
        if ($picbookId) {
            $coverOptions = \App\Models\PicbookCoverVariant::where('picbook_id', $picbookId)
                ->where('language', $language)
                ->where('is_published', true)
                ->orderBy('sort_order')
                ->get()
                ->map(function($variant) {
                    // 从text_config中获取名称
                    $name = 'Cover ' . $variant->id;
                    if (isset($variant->text_config['title'])) {
                        $name = $variant->text_config['title'];
                    }
                    
                    return [
                        'id' => $variant->id,
                        'option_type' => 'cover',
                        'option_key' => 'cover_' . $variant->id,
                        'name' => $name,
                        'description' => null,
                        'price' => $variant->price ?? 0,
                        'currency_code' => $variant->currencycode ?? 'USD',
                        'image_url' => $variant->image_url,
                        'is_default' => $variant->is_default ?? false,
                        'gender' => $variant->gender,
                        'skincolor' => $variant->skincolor,
                        'has_face' => $variant->has_face,
                        'has_text' => $variant->has_text,
                        'text_config' => $variant->text_config,
                        'face_config' => $variant->face_config
                    ];
                });
        }
        // 为装帧选项添加多语言支持
        $bindingOptions = \App\Models\ProductOption::where('option_type', \App\Models\ProductOption::TYPE_BINDING)
            ->where('status', \App\Models\ProductOption::STATUS_ACTIVE)
            ->orderBy('sort_order')
            ->get()
            ->map(function($option) use ($language) {
                // 名称和描述支持多语言
                $name = $option->name;
                $description = $option->description;
                
                // 如果存在多语言名称，使用对应语言的名称
                if (is_array($option->name) && isset($option->name[$language])) {
                    $name = $option->name[$language];
                }
                
                // 如果存在多语言描述，使用对应语言的描述
                if (is_array($option->description) && isset($option->description[$language])) {
                    $description = $option->description[$language];
                }
                
                return [
                    'id' => $option->id,
                    'option_type' => $option->option_type,
                    'option_key' => $option->option_key,
                    'name' => $name,
                    'description' => $description,
                    'price' => $option->price,
                    'currency_code' => $option->currency_code,
                    'image_url' => $option->image_url,
                    'is_default' => $option->is_default
                ];
            });
        
        // 为礼盒选项添加多语言支持
        $giftBoxOptions = \App\Models\ProductOption::where('option_type', \App\Models\ProductOption::TYPE_GIFT_BOX)
            ->where('status', \App\Models\ProductOption::STATUS_ACTIVE)
            ->orderBy('sort_order')
            ->get()
            ->map(function($option) use ($language) {
                // 名称和描述支持多语言
                $name = $option->name;
                $description = $option->description;
                
                // 如果存在多语言名称，使用对应语言的名称
                if (is_array($option->name) && isset($option->name[$language])) {
                    $name = $option->name[$language];
                }
                
                // 如果存在多语言描述，使用对应语言的描述
                if (is_array($option->description) && isset($option->description[$language])) {
                    $description = $option->description[$language];
                }
                
                return [
                    'id' => $option->id,
                    'option_type' => $option->option_type,
                    'option_key' => $option->option_key,
                    'name' => $name,
                    'description' => $description,
                    'price' => $option->price,
                    'currency_code' => $option->currency_code,
                    'image_url' => $option->image_url,
                    'is_default' => $option->is_default
                ];
            });
        
        return [
            'cover_options' => $coverOptions,
            'binding_options' => $bindingOptions,
            'gift_box_options' => $giftBoxOptions
        ];
    }
    
    /**
     * 验证并获取产品选项价格
     *
     * @param string|int $coverId 封面ID或类型
     * @param string $bindingType 装帧方式
     * @param string $giftBox 礼盒类型
     * @param int|null $picbookId 绘本ID
     * @return array
     */
    public function getOptionsPrice($coverId, string $bindingType, string $giftBox, ?int $picbookId = null)
    {
        // 获取封面价格
        $coverPrice = 0;
        $currencyCode = 'USD';
        
        if (is_numeric($coverId)) {
            // 从picbook_cover_variants表获取封面价格
            $coverVariant = \App\Models\PicbookCoverVariant::where('id', $coverId)
                ->where('is_published', true)
                ->first();
                
            if ($coverVariant) {
                $coverPrice = $coverVariant->price ?? 0;
                $currencyCode = $coverVariant->currencycode ?? 'USD';
                $picbookId = $coverVariant->picbook_id; // 获取关联的绘本ID，用于后续可能的处理
            }
        } else {
            // 如果传入的不是数字ID，记录错误日志
            \Illuminate\Support\Facades\Log::warning('尝试使用非数字ID获取封面价格', [
                'cover_id' => $coverId,
                'picbook_id' => $picbookId
            ]);
        }
        
        // 获取装帧价格
        $bindingOption = \App\Models\ProductOption::getOption($bindingType, \App\Models\ProductOption::TYPE_BINDING);
        $bindingPrice = $bindingOption ? $bindingOption->price : 0;
        
        // 获取礼盒价格
        $giftBoxPrice = 0;
        if ($giftBox) {
            $giftBoxOption = \App\Models\ProductOption::getOption($giftBox, \App\Models\ProductOption::TYPE_GIFT_BOX);
            $giftBoxPrice = $giftBoxOption ? $giftBoxOption->price : 0;
        }
        
        return [
            'cover_price' => $coverPrice,
            'binding_price' => $bindingPrice,
            'gift_box_price' => $giftBoxPrice,
            'currency_code' => $currencyCode
        ];
    }
} 