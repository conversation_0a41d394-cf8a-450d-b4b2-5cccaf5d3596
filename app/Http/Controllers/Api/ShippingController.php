<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ExpService;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ShippingController extends Controller
{
    use ApiResponse;

    protected $expService;

    public function __construct(ExpService $expService)
    {
        $this->expService = $expService;
    }

    /**
     * 获取物流价格选项
     */
    public function getPrices(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'country_code' => 'required|string|size:2',
                'weight' => 'nullable|numeric|min:1',
                'length' => 'nullable|numeric|min:1',
                'width' => 'nullable|numeric|min:1',
                'height' => 'nullable|numeric|min:1',
                'currency' => 'nullable|string|size:3'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            // 设置默认值（基于实际绘本尺寸）
            $params = [
                'country_code' => $request->country_code,
                'weight' => $request->weight ?? 600,  // 默认600克（0.6kg）
                'length' => $request->length ?? 36.5, // 默认36.5cm
                'width' => $request->width ?? 26.5,   // 默认26.5cm
                'height' => $request->height ?? 5.0   // 默认5cm
            ];

            $currency = $request->currency ?? 'USD';

            // 获取物流选项
            $shippingOptions = $this->expService->getShippingOptions($params, $currency);

            return $this->success([
                'shipping_options' => $shippingOptions,
                'package_info' => [
                    'weight' => $params['weight'],
                    'dimensions' => [
                        'length' => $params['length'],
                        'width' => $params['width'],
                        'height' => $params['height']
                    ],
                    'weight_unit' => 'g',
                    'dimension_unit' => 'cm'
                ]
            ], __('shipping.prices_retrieved'));
        } catch (\Exception $e) {
            Log::error('获取物流价格失败', [
                'params' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取支持的国家列表
     */
    public function getCountries()
    {
        try {
            $countries = $this->expService->getCountryList();

            return $this->success($countries, __('shipping.countries_retrieved'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取物流产品列表
     */
    public function getProducts()
    {
        try {
            $products = $this->expService->getLogisticsProducts();

            return $this->success([
                'products' => $products
            ], __('shipping.products_retrieved'));
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
}
