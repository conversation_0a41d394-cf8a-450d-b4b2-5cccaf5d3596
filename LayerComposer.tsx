'use client';

import React, { useRef, useState, useCallback, useEffect } from 'react';
import Image from 'next/image';
// @ts-ignore
import piexif from 'piexifjs';

interface LayerComposerProps {
  onCompositionComplete?: (composedImage: string) => void;
  className?: string;
  availableBaseImages?: string[];
  defaultBaseImage?: string;
  userChoices?: {
    hairStyle: string;
    hairColor: string;
    skinColor: string;
  } | null;
}

interface LayerSettings {
  baseImage: string;
  hairStyle: 'hair1' | 'hair2' | 'hair3' | 'hair4';
  hairColor: 'original' | 'brown' | 'dark';
  skinTone: 'original' | 'brown' | 'dark';
  targetDPI: number;
}

// 滤镜配置接口
interface FilterConfig {
  saturate?: string;
  hue?: string;
  brightness?: string;
  contract?: string;
}

interface PageProperties {
  skinToneFilter: {
    brown: FilterConfig;
    dark: FilterConfig;
  };
  hairColorFilter: {
    brown: FilterConfig;
    dark: FilterConfig;
  };
}

// 默认配置（直接硬编码，避免网络请求问题）
const defaultConfig: PageProperties = {
  skinToneFilter: {
    brown: { saturate: "+2", hue: "-4", brightness: "-31" },
    dark: { brightness: "-115", contract: "-48" }
  },
  hairColorFilter: {
    brown: { saturate: "+2", hue: "-4", brightness: "-31" },
    dark: { brightness: "-115", contract: "-48" }
  }
};

// 使用EXIF库添加DPI元数据到图片
const addDPIMetadata = async (canvas: HTMLCanvasElement, dpi: number): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    console.log(`开始注入DPI元数据: ${dpi} DPI`);
    
    // 生成JPEG dataURL
    const dataURL = canvas.toDataURL('image/jpeg', 0.95);
    
    try {
      // 创建EXIF对象
      const exifObj: any = { 
        "0th": {}, 
        "Exif": {}, 
        "GPS": {}, 
        "Interop": {}, 
        "1st": {}, 
        "thumbnail": null 
      };
      
      // 设置DPI信息
      exifObj["0th"][piexif.ImageIFD.XResolution] = [dpi, 1];
      exifObj["0th"][piexif.ImageIFD.YResolution] = [dpi, 1];
      exifObj["0th"][piexif.ImageIFD.ResolutionUnit] = 2; // 2 = inch (DPI)
      
      console.log(`EXIF对象创建完成，DPI: ${dpi}`);
      
      // 将EXIF数据插入到JPEG中
      const exifBytes = piexif.dump(exifObj);
      const newDataURL = piexif.insert(exifBytes, dataURL);
      
      console.log(`EXIF数据插入完成`);
      
      // 转换为Blob
      fetch(newDataURL)
        .then(response => response.blob())
        .then(blob => {
          console.log(`DPI注入完成，Blob大小: ${blob.size} 字节`);
          resolve(blob);
        })
        .catch(error => {
          console.error('转换为Blob失败:', error);
          reject(error);
        });
        
    } catch (error) {
      console.error('EXIF处理失败:', error);
      // 如果EXIF处理失败，返回原始blob
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('无法创建Blob'));
        }
      }, 'image/jpeg', 0.95);
    }
  });
};

const LayerComposer: React.FC<LayerComposerProps> = ({
  onCompositionComplete,
  className = '',
  availableBaseImages = [],
  defaultBaseImage,
  userChoices
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [compositionResult, setCompositionResult] = useState<string | null>(null);
  const [pageProperties, setPageProperties] = useState<PageProperties | null>(null);
  
  // 预览状态
  const [hairPreview, setHairPreview] = useState<string | null>(null);
  const [skinPreview, setSkinPreview] = useState<string | null>(null);
  const [isGeneratingHairPreview, setIsGeneratingHairPreview] = useState(false);
  const [isGeneratingSkinPreview, setIsGeneratingSkinPreview] = useState(false);
  
  // 默认底图选项
  const defaultBaseImages = [
    '/frontend-hair-test/layer_bg.jpg',
    '/frontend-hair-test/example_1.jpg',
    '/frontend-hair-test/example_2.jpg'
  ];
  
  const allBaseImages = [...new Set([...defaultBaseImages, ...availableBaseImages])];
  
  const [settings, setSettings] = useState<LayerSettings>({
    baseImage: defaultBaseImage || '/frontend-hair-test/layer_bg.jpg',
    hairStyle: (userChoices?.hairStyle as 'hair1' | 'hair2' | 'hair3' | 'hair4') || 'hair1',
    hairColor: (userChoices?.hairColor as 'original' | 'brown' | 'dark') || 'original',
    skinTone: userChoices?.skinColor === '3' ? 'dark' : userChoices?.skinColor === '2' ? 'brown' : 'original',
    targetDPI: 300
  });

  // 当userChoices变化时更新settings
  useEffect(() => {
    if (userChoices) {
      setSettings(prev => ({
        ...prev,
        hairStyle: (userChoices.hairStyle as 'hair1' | 'hair2' | 'hair3' | 'hair4') || 'hair1',
        hairColor: (userChoices.hairColor as 'original' | 'brown' | 'dark') || 'original',
        skinTone: userChoices.skinColor === '3' ? 'dark' : userChoices.skinColor === '2' ? 'brown' : 'original',
      }));
    }
  }, [userChoices]);

  // 初始化配置
  useEffect(() => {
    // 直接使用硬编码的配置，避免网络请求问题
    setPageProperties(defaultConfig);
    console.log('使用默认配置:', defaultConfig);
  }, []);

  // 加载图像
  const loadImage = useCallback((src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = document.createElement('img') as HTMLImageElement;
      if (src.startsWith('http')) {
        img.crossOrigin = 'anonymous';
      }
      img.onload = () => resolve(img);
      img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
      img.src = src;
    });
  }, []);

  // 应用滤镜到单独的图层
  const applyFilterToLayer = useCallback((
    imageData: ImageData,
    filterType: 'skin' | 'hair',
    filterName: 'brown' | 'dark'
  ) => {
    if (!pageProperties) return imageData;

    const filterConfig = filterType === 'skin' 
      ? pageProperties.skinToneFilter[filterName]
      : pageProperties.hairColorFilter[filterName];

    const data = imageData.data;
    
    // 验证透明区域数据
    const testPixel = data.slice(0, 4);
    console.log(`${filterType}图层滤镜前透明区域验证:`, testPixel);
    
    // 检查前几个像素的alpha值分布
    let alphaValues = [];
    for (let i = 0; i < Math.min(100, data.length); i += 4) {
      alphaValues.push(data[i + 3]);
    }
    console.log(`${filterType}图层前100个像素的alpha值分布:`, alphaValues);
    
    let processedPixels = 0;
    let skippedPixels = 0;
    
    for (let i = 0; i < data.length; i += 4) {
      let r = data[i];
      let g = data[i + 1];
      let b = data[i + 2];
      const a = data[i + 3]; // Alpha通道

      // 只对有像素的部分（非透明区域）应用滤镜
      if (a > 0) {
        processedPixels++;
        // 应用饱和度调整
        if (filterConfig.saturate) {
          const saturateValue = parseFloat(filterConfig.saturate.replace('+', ''));
          const gray = 0.299 * r + 0.587 * g + 0.114 * b;
          r = Math.min(255, Math.max(0, gray + saturateValue * (r - gray)));
          g = Math.min(255, Math.max(0, gray + saturateValue * (g - gray)));
          b = Math.min(255, Math.max(0, gray + saturateValue * (b - gray)));
        }

        // 应用色相调整
        if (filterConfig.hue) {
          const hueValue = parseFloat(filterConfig.hue);
          const hueRad = hueValue * Math.PI / 180;
          const newR = r * Math.cos(hueRad) - g * Math.sin(hueRad);
          const newG = r * Math.sin(hueRad) + g * Math.cos(hueRad);
          r = Math.min(255, Math.max(0, newR));
          g = Math.min(255, Math.max(0, newG));
        }

        // 应用亮度调整
        if (filterConfig.brightness) {
          const brightnessValue = parseFloat(filterConfig.brightness);
          r = Math.min(255, Math.max(0, r + brightnessValue));
          g = Math.min(255, Math.max(0, g + brightnessValue));
          b = Math.min(255, Math.max(0, b + brightnessValue));
        }

        // 应用对比度调整
        if (filterConfig.contract) {
          const contrastValue = parseFloat(filterConfig.contract);
          const factor = (259 * (contrastValue + 255)) / (255 * (259 - contrastValue));
          r = Math.min(255, Math.max(0, factor * (r - 128) + 128));
          g = Math.min(255, Math.max(0, factor * (g - 128) + 128));
          b = Math.min(255, Math.max(0, factor * (b - 128) + 128));
        }

        data[i] = Math.min(255, Math.max(0, r));
        data[i + 1] = Math.min(255, Math.max(0, g));
        data[i + 2] = Math.min(255, Math.max(0, b));
        // 保持原始alpha值不变
        data[i + 3] = a;
      } else {
        skippedPixels++;
      }
      // 透明区域保持原样，不应用滤镜
    }

    console.log(`${filterType}图层滤镜处理统计: 处理了${processedPixels}个像素，跳过了${skippedPixels}个透明像素`);

    // 验证滤镜后透明区域数据
    const testPixelAfter = data.slice(0, 4);
    console.log(`${filterType}图层滤镜后透明区域验证:`, testPixelAfter);

    return imageData;
  }, [pageProperties]);

  // 生成头发预览
  const generateHairPreview = useCallback(async () => {
    if (!pageProperties) return;

    setIsGeneratingHairPreview(true);

    try {
      if (settings.hairColor !== 'original') {
        const hairImage = await loadImage(`/frontend-hair-test/layer_hair_${settings.hairStyle.replace('hair', '')}.png`);
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (ctx) {
          canvas.width = hairImage.width;
          canvas.height = hairImage.height;
          
          // 直接绘制头发图层，保持透明背景
          ctx.drawImage(hairImage, 0, 0);
          
          // 验证透明区域
          const testPixel = ctx.getImageData(0, 0, 1, 1).data;
          console.log('头发图层透明区域验证:', testPixel); // 应该是 [0,0,0,0]
          
          const hairImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const filteredHairData = applyFilterToLayer(hairImageData, 'hair', settings.hairColor);
          ctx.putImageData(filteredHairData, 0, 0);
          
          setHairPreview(canvas.toDataURL('image/png'));
        }
      } else {
        setHairPreview(null);
      }

    } catch (error) {
      console.error('生成头发预览失败:', error);
    } finally {
      setIsGeneratingHairPreview(false);
    }
  }, [settings.hairStyle, settings.hairColor, pageProperties, applyFilterToLayer, loadImage]);

  // 生成皮肤预览
  const generateSkinPreview = useCallback(async () => {
    if (!pageProperties) return;

    setIsGeneratingSkinPreview(true);

    try {
      if (settings.skinTone !== 'original') {
        const skinImage = await loadImage('/frontend-hair-test/layer_skin.png');
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (ctx) {
          canvas.width = skinImage.width;
          canvas.height = skinImage.height;
          
          // 直接绘制皮肤图层，保持透明背景
          ctx.drawImage(skinImage, 0, 0);
          
          // 验证透明区域
          const testPixel = ctx.getImageData(0, 0, 1, 1).data;
          console.log('皮肤图层透明区域验证:', testPixel); // 应该是 [0,0,0,0]
          
          const skinImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const filteredSkinData = applyFilterToLayer(skinImageData, 'skin', settings.skinTone);
          ctx.putImageData(filteredSkinData, 0, 0);
          
          setSkinPreview(canvas.toDataURL('image/png'));
        }
      } else {
        setSkinPreview(null);
      }

    } catch (error) {
      console.error('生成皮肤预览失败:', error);
    } finally {
      setIsGeneratingSkinPreview(false);
    }
  }, [settings.skinTone, pageProperties, applyFilterToLayer, loadImage]);

  // 当头发相关设置改变时生成头发预览
  useEffect(() => {
    if (pageProperties) {
      generateHairPreview();
    }
  }, [settings.hairStyle, settings.hairColor, pageProperties, generateHairPreview]);

  // 当皮肤设置改变时生成皮肤预览
  useEffect(() => {
    if (pageProperties) {
      generateSkinPreview();
    }
  }, [settings.skinTone, pageProperties, generateSkinPreview]);

  // 图层合成
  const composeLayers = useCallback(async () => {
    if (!canvasRef.current) return;

    setIsComposing(true);
    setCompositionResult(null);

    try {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('无法获取canvas上下文');
      }

      // 加载所有图层
      const bgImage = await loadImage(settings.baseImage);
      const hairImage = await loadImage(`/frontend-hair-test/layer_hair_${settings.hairStyle.replace('hair', '')}.png`);
      const skinImage = await loadImage('/frontend-hair-test/layer_skin.png');

      // 设置canvas尺寸
      canvas.width = bgImage.width;
      canvas.height = bgImage.height;

      // 1. 绘制背景图层
      ctx.drawImage(bgImage, 0, 0);

      // 2. 处理皮肤图层
      if (settings.skinTone !== 'original' && pageProperties) {
        // 创建临时canvas处理皮肤图层
        const skinCanvas = document.createElement('canvas');
        const skinCtx = skinCanvas.getContext('2d');
        if (skinCtx) {
          skinCanvas.width = skinImage.width;
          skinCanvas.height = skinImage.height;
          
          // 绘制皮肤图层到临时canvas
          skinCtx.drawImage(skinImage, 0, 0);
          
          // 获取皮肤图层数据并应用滤镜
          const skinImageData = skinCtx.getImageData(0, 0, skinCanvas.width, skinCanvas.height);
          const filteredSkinData = applyFilterToLayer(skinImageData, 'skin', settings.skinTone);
          
          // 将处理后的皮肤图层绘制到主canvas
          skinCtx.putImageData(filteredSkinData, 0, 0);
          ctx.drawImage(skinCanvas, 0, 0);
        }
      } else {
        // 直接绘制原始皮肤图层
        ctx.drawImage(skinImage, 0, 0);
      }

      // 3. 处理头发图层
      if (settings.hairColor !== 'original' && pageProperties) {
        // 创建临时canvas处理头发图层
        const hairCanvas = document.createElement('canvas');
        const hairCtx = hairCanvas.getContext('2d');
        if (hairCtx) {
          hairCanvas.width = hairImage.width;
          hairCanvas.height = hairImage.height;
          
          // 绘制头发图层到临时canvas
          hairCtx.drawImage(hairImage, 0, 0);
          
          // 获取头发图层数据并应用滤镜
          const hairImageData = hairCtx.getImageData(0, 0, hairCanvas.width, hairCanvas.height);
          const filteredHairData = applyFilterToLayer(hairImageData, 'hair', settings.hairColor);
          
          // 将处理后的头发图层绘制到主canvas
          hairCtx.putImageData(filteredHairData, 0, 0);
          ctx.drawImage(hairCanvas, 0, 0);
        }
      } else {
        // 直接绘制原始头发图层
        ctx.drawImage(hairImage, 0, 0);
      }

      // 获取合成结果 (带DPI元数据)
      const result = canvas.toDataURL('image/jpeg', 0.95);
      setCompositionResult(result);

      if (onCompositionComplete) {
        onCompositionComplete(result);
      }

    } catch (error) {
      console.error('图层合成失败:', error);
      alert('合成失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsComposing(false);
    }
  }, [settings, applyFilterToLayer, loadImage, onCompositionComplete, pageProperties]);

  // 下载合成结果 (带DPI元数据)
  const downloadResult = useCallback(async () => {
    if (!compositionResult || !canvasRef.current) return;

    console.log(`开始下载，目标DPI: ${settings.targetDPI}`);

    try {
      // 添加DPI元数据
      const blob = await addDPIMetadata(canvasRef.current, settings.targetDPI);
      
      console.log(`DPI注入完成，Blob大小: ${blob.size} 字节`);
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.download = `composed-image-${settings.targetDPI}dpi.jpg`;
      link.href = url;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 清理URL
      URL.revokeObjectURL(url);
      
      console.log(`下载完成: composed-image-${settings.targetDPI}dpi.jpg`);
    } catch (error) {
      console.error('DPI注入失败:', error);
      console.log('使用原始方法下载');
      
      // 如果DPI注入失败，使用原始方法
      const link = document.createElement('a');
      link.download = 'composed-image.jpg';
      link.href = compositionResult;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [compositionResult, settings.targetDPI]);

  // 更新设置
  const updateSetting = useCallback((key: keyof LayerSettings, value: string | number) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  return (
    <div className={`layer-composer ${className}`}>
      <div className="mb-6">
        <h3 className="text-2xl font-semibold mb-4">图层合成工具</h3>
        
        {/* 配置状态 */}
        {pageProperties ? (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800">✓ 配置已加载</p>
            <p className="text-xs text-blue-600 mt-1">使用内置配置</p>
          </div>
        ) : (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">⏳ 正在初始化配置...</p>
          </div>
        )}
        
        {/* 图层预览 */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <h4 className="text-sm font-medium mb-2">背景图层</h4>
            <div className="relative w-full h-32 border rounded-lg overflow-hidden">
              <Image
                src={settings.baseImage}
                alt="背景图层"
                fill
                className="object-cover"
              />
            </div>
          </div>
          
          <div className="text-center">
            <h4 className="text-sm font-medium mb-2">
              头发图层 
              {settings.hairColor !== 'original' && (
                <span className="text-xs text-blue-600 ml-1">
                  ({settings.hairColor})
                </span>
              )}
            </h4>
            <div className="relative w-full h-32 border rounded-lg overflow-hidden">
              {isGeneratingHairPreview ? (
                <div className="flex items-center justify-center h-full bg-gray-100">
                  <div className="text-xs text-gray-500">生成预览中...</div>
                </div>
              ) : hairPreview ? (
                <div className="relative w-full h-full bg-white">
                  <Image
                    src={hairPreview}
                    alt="头发图层预览"
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <Image
                  src={`/frontend-hair-test/layer_hair_${settings.hairStyle.replace('hair', '')}.png`}
                  alt="头发图层"
                  fill
                  className="object-cover"
                />
              )}
            </div>
          </div>
          
          <div className="text-center">
            <h4 className="text-sm font-medium mb-2">
              皮肤图层
              {settings.skinTone !== 'original' && (
                <span className="text-xs text-blue-600 ml-1">
                  ({settings.skinTone})
                </span>
              )}
            </h4>
            <div className="relative w-full h-32 border rounded-lg overflow-hidden">
              {isGeneratingSkinPreview ? (
                <div className="flex items-center justify-center h-full bg-gray-100">
                  <div className="text-xs text-gray-500">生成预览中...</div>
                </div>
              ) : skinPreview ? (
                <div className="relative w-full h-full bg-white">
                  <Image
                    src={skinPreview}
                    alt="皮肤图层预览"
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <Image
                  src="/frontend-hair-test/layer_skin.png"
                  alt="皮肤图层"
                  fill
                  className="object-cover"
                />
              )}
            </div>
          </div>
        </div>

        {/* 合成设置 */}
        <div className="mb-6 p-6 bg-gray-50 rounded-lg">
          <h4 className="text-lg font-medium mb-4">合成设置</h4>
          
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">底图选择</label>
              <select
                value={settings.baseImage}
                onChange={(e) => updateSetting('baseImage', e.target.value)}
                className="w-full p-2 border rounded"
              >
                {allBaseImages.map((image, index) => (
                  <option key={index} value={image}>
                    {image.includes('layer_bg') ? '默认背景' :
                     image.includes('example_1') ? '示例图片 1' :
                     image.includes('example_2') ? '示例图片 2' :
                     `底图 ${index + 1}`}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">发型选择</label>
              <select
                value={settings.hairStyle}
                onChange={(e) => updateSetting('hairStyle', e.target.value)}
                className="w-full p-2 border rounded"
              >
                <option value="hair1">发型 1</option>
                <option value="hair2">发型 2</option>
                <option value="hair3">发型 3</option>
                <option value="hair4">发型 4</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">头发颜色</label>
              <select
                value={settings.hairColor}
                onChange={(e) => updateSetting('hairColor', e.target.value)}
                className="w-full p-2 border rounded"
              >
                <option value="original">原始颜色</option>
                <option value="brown">棕色</option>
                <option value="dark">深色</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">皮肤色调</label>
              <select
                value={settings.skinTone}
                onChange={(e) => updateSetting('skinTone', e.target.value)}
                className="w-full p-2 border rounded"
              >
                <option value="original">原始色调</option>
                <option value="brown">棕色</option>
                <option value="dark">深色</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">目标DPI</label>
              <select
                value={settings.targetDPI}
                onChange={(e) => updateSetting('targetDPI', parseInt(e.target.value))}
                className="w-full p-2 border rounded"
              >
                <option value={72}>72 DPI (屏幕)</option>
                <option value={150}>150 DPI (普通打印)</option>
                <option value={300}>300 DPI (高质量打印)</option>
                <option value={600}>600 DPI (专业打印)</option>
              </select>
            </div>
          </div>

          {/* 配置信息显示 */}
          {pageProperties && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <h5 className="text-sm font-medium mb-2">当前滤镜配置 (符合原始数值)</h5>
              <div className="text-xs text-blue-800 space-y-1">
                <p><strong>皮肤棕色:</strong> 饱和度{pageProperties.skinToneFilter.brown.saturate}, 色相{pageProperties.skinToneFilter.brown.hue}, 亮度{pageProperties.skinToneFilter.brown.brightness}</p>
                <p><strong>皮肤深色:</strong> 亮度{pageProperties.skinToneFilter.dark.brightness}, 对比度{pageProperties.skinToneFilter.dark.contract}</p>
                <p><strong>头发棕色:</strong> 饱和度{pageProperties.hairColorFilter.brown.saturate}, 色相{pageProperties.hairColorFilter.brown.hue}, 亮度{pageProperties.hairColorFilter.brown.brightness}</p>
                <p><strong>头发深色:</strong> 亮度{pageProperties.hairColorFilter.dark.brightness}, 对比度{pageProperties.hairColorFilter.dark.contract}</p>
                <p className="text-green-600 mt-2"><strong>✓ 滤镜只应用于有像素的部分，透明区域保持原样</strong></p>
                <p className="text-blue-600 mt-1"><strong>ℹ️ 预览使用PNG格式保持透明度，容器白色背景显示透明区域</strong></p>
              </div>
            </div>
          )}

          {/* DPI信息 */}
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <h5 className="text-sm font-medium mb-2">DPI设置信息</h5>
            <div className="text-xs text-green-800 space-y-1">
              <p><strong>目标DPI:</strong> {settings.targetDPI} DPI</p>
              <p><strong>文件格式:</strong> JPEG (95% 质量)</p>
              <p><strong>元数据:</strong> 将注入{settings.targetDPI} DPI信息到JPEG文件</p>
              <p><strong>打印效果:</strong> 适合{settings.targetDPI >= 300 ? '高质量打印' : settings.targetDPI >= 150 ? '普通打印' : '屏幕显示'}</p>
            </div>
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="flex gap-4 mb-6">
          <button
            onClick={composeLayers}
            disabled={isComposing}
            className={`px-8 py-3 rounded-lg font-medium ${
              isComposing
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white`}
          >
            {isComposing ? '合成中...' : '开始图层合成'}
          </button>
          
          {compositionResult && (
            <button
              onClick={downloadResult}
              className="px-8 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium"
            >
              下载结果 ({settings.targetDPI} DPI)
            </button>
          )}
        </div>

        {/* 结果展示 */}
        {compositionResult && (
          <div className="text-center">
            <h4 className="text-lg font-medium mb-4">图层合成结果</h4>
            <div className="relative w-full max-w-2xl mx-auto h-96 border rounded-lg overflow-hidden">
              <Image
                src={compositionResult}
                alt="图层合成结果"
                fill
                className="object-contain"
              />
            </div>
          </div>
        )}
      </div>

      {/* 隐藏的canvas用于图像处理 */}
      <canvas
        ref={canvasRef}
        className="hidden"
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default LayerComposer; 