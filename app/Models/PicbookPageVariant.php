<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PicbookPageVariant extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'page_id',
        'language',
        'gender',
        'skincolor',
        'character_skincolors_hash',
        'image_url',
        'skin_mask_url',
        'has_text',
        'text_config',
        'has_face',
        'face_config',
        'is_published',
        'content',
        'choice_options',
        'question',
        'character_masks',
        'is_preview_variant',
        'variant_type',
        'elements',
        'text_elements',
        'character_skincolors',
        'processing_status',
        'processed_image_url'
    ];

    // 将 choice_options 字段转换为数组
    protected $casts = [
        'choice_options' => 'array',
        'gender' => 'integer',
        'skincolor' => 'integer',
        'character_masks' => 'array',
        'text_config' => 'array',
        'face_config' => 'array',
        'has_text' => 'boolean',
        'has_face' => 'boolean',
        'is_published' => 'boolean',
        'is_preview_variant' => 'boolean',
        'variant_type' => 'integer',
        'elements' => 'array',
        'text_elements' => 'array',
        'character_skincolors' => 'array'
    ];

    /**
     * 获取关联的绘本页面
     */
    public function page()
    {
        return $this->belongsTo(PicbookPage::class, 'page_id');
    }

    /**
     * 通过页面获取关联的绘本
     */
    public function picbook()
    {
        return $this->hasOneThrough(
            Picbook::class,
            PicbookPage::class,
            'id', // picbook_pages.id
            'id', // picbooks.id
            'page_id', // picbook_page_variants.page_id
            'picbook_id' // picbook_pages.picbook_id
        );
    }
    
    
    /**
     * 获取无肤色基础图URL
     */
    public function getNoneSkinUrlAttribute()
    {
        return optional($this->languageAsset())->none_skin_url;
    }

    /**
     * 获取指定位置的角色蒙版
     */
    public function getCharacterMask($position)
    {
        return $this->character_masks[$position] ?? null;
    }

    /**
     * 验证角色蒙版数量是否与页面角色序列匹配
     * 
     * @return bool|string 验证通过返回true，否则返回错误信息
     */
    public function validateCharacterMasks()
    {
        // 如果页面没有角色序列，则不需要蒙版
        if (empty($this->page->character_sequence)) {
            return $this->character_masks ? __('picbook.page_variant.no_sequence_with_masks') : true;
        }

        // 如果页面有角色序列，但没有提供蒙版
        if (empty($this->character_masks)) {
            return __('picbook.page_variant.masks_required');
        }

        // 验证蒙版数量是否匹配
        $sequence_count = count($this->page->character_sequence);
        if (count($this->character_masks) !== $sequence_count) {
            return __('picbook.page_variant.masks_count_mismatch', [
                'masks' => count($this->character_masks),
                'sequence' => $sequence_count
            ]);
        }

        // 验证所有蒙版URL是否有效
        foreach ($this->character_masks as $mask) {
            if (empty($mask) || !is_string($mask)) {
                return __('picbook.page_variant.invalid_mask_url');
            }
        }

        return true;
    }

    /**
     * 本地化范围查询
     */
    public function scopeLocalized($query, $language = null, $gender = null, $skincolor = null)
    {
        $language = $language ?? app()->getLocale();
        $gender = $gender ?? 1;
        $skincolor = $skincolor ?? 1;

        return $query->where([
            'language' => $language,
            'gender' => $gender,
            'skincolor' => $skincolor
        ]);
    }
    
    /**
     * 已发布的页面变体范围
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * 处理问题文本，替换变量
     * 
     * @param array $answers 用户回答
     * @return string 处理后的问题文本
     */
    public function processQuestion($answers = [])
    {
        if (empty($this->question) || empty($answers)) {
            return $this->question;
        }
        
        $processedQuestion = $this->question;
        
        foreach ($answers as $key => $value) {
            $processedQuestion = str_replace("{{$key}}", $value, $processedQuestion);
        }
        
        return $processedQuestion;
    }
    
    /**
     * 处理内容文本，替换变量
     * 
     * @param array $answers 用户回答
     * @param string $characterName 角色名称
     * @return string 处理后的内容文本
     */
    public function processContent($answers = [], $characterName = null)
    {
        if (empty($this->content)) {
            return $this->content;
        }
        
        $processedContent = $this->content;
        
        // 替换角色名称
        if ($characterName) {
            $processedContent = str_replace('{name}', $characterName, $processedContent);
        }
        
        // 替换用户回答变量
        if (!empty($answers)) {
            foreach ($answers as $key => $value) {
                $processedContent = str_replace("{{$key}}", $value, $processedContent);
            }
        }
        
        return $processedContent;
    }
    
    /**
     * 获取用户的回答记录
     * 
     * @param int $userId 用户ID
     * @return PicbookUserAnswer|null 用户回答记录
     */
    public function getUserAnswer($userId)
    {
        return PicbookUserAnswer::where('user_id', $userId)
            ->where('picbook_id', $this->picbook()->first()->id)
            ->where('page_id', $this->page_id)
            ->latest()
            ->first();
    }
    
    /**
     * 保存用户回答
     * 
     * @param int $userId 用户ID
     * @param array $answers 回答数据
     * @return PicbookUserAnswer 创建的回答记录
     */
    public function saveUserAnswer($userId, array $answers)
    {
        $picbookId = $this->picbook()->first()->id;
        
        return PicbookUserAnswer::create([
            'user_id' => $userId,
            'picbook_id' => $picbookId,
            'page_id' => $this->page_id,
            'answers' => $answers
        ]);
    }
} 