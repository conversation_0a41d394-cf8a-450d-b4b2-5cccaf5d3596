# 物流费用安全处理方案

## 问题描述

在订单创建过程中，如果直接信任前端传递的 `shipping_cost`，存在以下安全风险：
1. 用户可能篡改物流费用，支付更低的价格
2. 恶意用户可能绕过物流费用，免费获得商品
3. 汇率波动或API价格变化时，前端缓存的价格可能不准确

## 解决方案

### 1. 后端强制校验（优化后）

物流费用的校验和计算逻辑统一在 `OrderService` 中处理，避免重复计算：

```php
// OrderController 简化为直接调用服务
$order = $this->orderService->createOrderFromCartItems($userId, $cartItemIds, $request->all());

// OrderService 内部处理校验和计算
private function validateAndCalculateShippingCost(array $cartItemIds, array $orderData)
{
    // 根据实际购物车商品计算物流费用
    $calculatedCostData = $this->calculateShippingCostForCartItems(
        $cartItemIds,
        $orderData['shipping_method'],
        $orderData['shipping_address']['country']
    );

    $calculatedShippingCost = $calculatedCostData['total'];
    $frontendShippingCost = $orderData['shipping_cost'] ?? null;

    // 校验前端传递的费用
    if ($frontendShippingCost !== null) {
        $costDifference = abs($calculatedShippingCost - $frontendShippingCost);
        if ($costDifference > 0.01) {
            throw new \Exception("物流费用校验失败");
        }
    }

    return $calculatedShippingCost; // 返回后端计算的准确费用
}
```

### 2. 基于实际商品的费用计算

根据购物车中的实际商品计算重量和尺寸：

```php
public function calculateShippingCostForCartItems(array $cartItemIds, string $shippingMethod, string $countryCode)
{
    $totalWeight = 0;
    $maxLength = 0;
    $maxWidth = 0;
    $totalHeight = 0;
    
    // 每本绘本的默认参数
    $bookWeight = 500; // 500克
    $bookDimensions = [20, 15, 2]; // 20x15x2厘米
    
    foreach ($cartItemIds as $cartItemId) {
        $cartItem = CartItem::find($cartItemId);
        if ($cartItem) {
            $quantity = $cartItem->quantity;
            
            // 累加重量
            $totalWeight += $bookWeight * $quantity;
            
            // 计算包装尺寸（假设书籍叠放）
            $maxLength = max($maxLength, $bookDimensions[0]);
            $maxWidth = max($maxWidth, $bookDimensions[1]);
            $totalHeight += $bookDimensions[2] * $quantity;
        }
    }
    
    $dimensions = [$maxLength, $maxWidth, $totalHeight];
    
    return $this->calculateShippingCost($shippingMethod, $countryCode, $totalWeight, $dimensions);
}
```

### 3. API接口安全设计

#### 3.1 物流费用计算接口

```javascript
// 前端调用 - 基于购物车商品计算
POST /api/order/calculate-shipping
{
    "shipping_method": "YANWEN_STANDARD",
    "country_code": "US",
    "cart_item_ids": [1, 2, 3] // 基于实际商品计算
}

// 响应
{
    "success": true,
    "data": {
        "cost": 15.00,
        "currency": "USD",
        "tax": 0,
        "total": 15.00
    }
}
```

#### 3.2 订单创建接口

```javascript
// 前端调用 - shipping_cost 可选
POST /api/order/create
{
    "cart_item_ids": [1, 2, 3],
    "shipping_address": {...},
    "shipping_method": "YANWEN_STANDARD",
    "shipping_cost": 15.00, // 可选，后端会校验
    "payment_method": "stripe"
}

// 如果费用不匹配，返回错误
{
    "success": false,
    "message": "物流费用校验失败，请重新计算物流费用",
    "data": {
        "calculated_cost": 15.00,
        "provided_cost": 12.00,
        "difference": 3.00
    }
}
```

## 前端最佳实践

### 1. 实时费用计算

```javascript
class ShippingCalculator {
    async calculateShipping(cartItemIds, shippingMethod, countryCode) {
        try {
            const response = await fetch('/api/order/calculate-shipping', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    cart_item_ids: cartItemIds,
                    shipping_method: shippingMethod,
                    country_code: countryCode
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('计算物流费用失败:', error);
            throw error;
        }
    }
}
```

### 2. 订单创建流程

```javascript
class OrderCreator {
    async createOrder(orderData) {
        try {
            // 1. 先计算最新的物流费用
            const shippingCost = await this.shippingCalculator.calculateShipping(
                orderData.cart_item_ids,
                orderData.shipping_method,
                orderData.shipping_address.country
            );
            
            // 2. 创建订单时包含计算的费用
            const response = await fetch('/api/order/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    ...orderData,
                    shipping_cost: shippingCost.total
                })
            });
            
            const result = await response.json();
            
            if (!result.success) {
                // 如果费用校验失败，重新计算并重试
                if (result.message.includes('物流费用校验失败')) {
                    return this.retryWithUpdatedCost(orderData);
                }
                throw new Error(result.message);
            }
            
            return result.data;
            
        } catch (error) {
            console.error('创建订单失败:', error);
            throw error;
        }
    }
    
    async retryWithUpdatedCost(orderData) {
        // 重新计算费用并重试
        const updatedCost = await this.shippingCalculator.calculateShipping(
            orderData.cart_item_ids,
            orderData.shipping_method,
            orderData.shipping_address.country
        );
        
        // 更新UI显示新的费用
        this.updateShippingCostDisplay(updatedCost.total);
        
        // 询问用户是否继续
        const confirmed = await this.confirmUpdatedCost(updatedCost.total);
        if (confirmed) {
            return this.createOrder({
                ...orderData,
                shipping_cost: updatedCost.total
            });
        } else {
            throw new Error('用户取消订单创建');
        }
    }
}
```

## 监控和日志

### 1. 费用校验失败监控

```php
// 记录费用校验失败的情况
if ($costDifference > $allowedDifference) {
    Log::warning('物流费用校验失败', [
        'user_id' => $userId,
        'shipping_method' => $orderData['shipping_method'],
        'country_code' => $countryCode,
        'calculated_cost' => $calculatedShippingCost,
        'provided_cost' => $frontendShippingCost,
        'difference' => $costDifference,
        'cart_items' => $cartItemIds
    ]);
}
```

### 2. 异常费用监控

```php
// 监控异常高的物流费用
if ($calculatedShippingCost > 100) {
    Log::alert('异常高的物流费用', [
        'user_id' => $userId,
        'shipping_method' => $orderData['shipping_method'],
        'country_code' => $countryCode,
        'calculated_cost' => $calculatedShippingCost,
        'cart_items' => $cartItemIds
    ]);
}
```

## 配置参数

### 1. 费用校验配置

```php
// config/order.php
return [
    'shipping' => [
        'cost_validation' => [
            'enabled' => true,
            'allowed_difference' => 0.01, // 允许的费用误差
            'max_cost_threshold' => 100.00, // 异常费用阈值
        ],
        'default_package' => [
            'weight' => 500, // 默认重量(克)
            'dimensions' => [20, 15, 2], // 默认尺寸(厘米)
        ]
    ]
];
```

### 2. 缓存策略

```php
// 缓存物流费用计算结果（短时间）
$cacheKey = "shipping_cost_{$shippingMethod}_{$countryCode}_" . md5(serialize($cartItemIds));
$cachedCost = Cache::remember($cacheKey, 300, function() use ($cartItemIds, $shippingMethod, $countryCode) {
    return $this->calculateShippingCostForCartItems($cartItemIds, $shippingMethod, $countryCode);
});
```

## 架构优化

### 职责分离
- **OrderController**: 负责请求验证和响应处理
- **OrderService**: 负责业务逻辑，包括物流费用校验和计算
- **ExpService**: 负责与4PX API的交互

### 避免重复计算
优化前的问题：
```php
// Controller 中计算一次
$calculatedCost = $this->orderService->calculateShippingCost(...);

// Service 中又可能计算一次
$order = $this->orderService->createOrderFromCartItems(...);
```

优化后的方案：
```php
// Controller 只负责调用
$order = $this->orderService->createOrderFromCartItems($userId, $cartItemIds, $request->all());

// Service 内部统一处理校验和计算
private function validateAndCalculateShippingCost(...) {
    // 一次计算，多重用途：校验 + 存储
}
```

## 总结

通过以上安全措施和架构优化，我们确保了：

1. **后端强制校验** - 所有物流费用都由后端重新计算和验证
2. **基于实际商品** - 根据购物车中的真实商品计算重量和尺寸
3. **容错处理** - 允许小幅误差，处理汇率波动等情况
4. **用户体验** - 费用变化时及时通知用户并允许重新确认
5. **监控告警** - 记录异常情况，便于问题排查和优化
6. **避免重复计算** - 统一在Service层处理，提高效率
7. **职责清晰** - Controller和Service各司其职，代码更清晰

这样既保证了安全性，又维护了良好的用户体验和代码质量。