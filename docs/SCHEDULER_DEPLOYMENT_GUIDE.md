# Laravel Scheduler 生产环境部署指南

## 问题诊断

你遇到的问题是 Laravel 11 中调度任务的配置方式发生了变化。在 Laravel 11 中，调度任务应该在 `routes/console.php` 中定义，而不是在 `app/Console/Kernel.php` 中。

## 解决方案

### 1. 清理旧配置

首先，清理 `app/Console/Kernel.php` 中的旧调度配置：

```php
// app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    // Laravel 11 中的调度任务现在在 routes/console.php 中定义
    // 请参考 routes/console.php 文件查看当前的调度配置
}
```

### 2. 调度任务配置

在 `routes/console.php` 中定义调度任务：

```php
<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

// 定义调度任务
Schedule::command('faceswap:process-pending')
    ->everyMinute()
    ->withoutOverlapping()
    ->runInBackground()
    ->onOneServer()
    ->appendOutputTo(storage_path('logs/faceswap-scheduler.log'));

Schedule::command('faceswap:cleanup-timeout')
    ->everyFiveMinutes()
    ->withoutOverlapping()
    ->onOneServer()
    ->appendOutputTo(storage_path('logs/faceswap-cleanup.log'));

Schedule::command('faceswap:queue-stats')
    ->hourly()
    ->onOneServer()
    ->appendOutputTo(storage_path('logs/faceswap-stats.log'));

Schedule::command('horizon:snapshot')
    ->everyFiveMinutes()
    ->onOneServer();

Schedule::command('horizon:clear')
    ->daily()
    ->onOneServer();
```

### 2. 生产环境 Cron 配置

确保在生产服务器上配置了正确的 cron 任务：

```bash
# 编辑 crontab
crontab -e

# 添加以下行（替换 /path/to/your/project 为实际路径）
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

### 3. 验证调度任务

```bash
# 检查调度任务列表
php artisan schedule:list

# 手动运行调度器（测试用）
php artisan schedule:run --verbose

# 检查特定命令是否存在
php artisan list | grep faceswap
```

### 4. 监控和日志

#### 创建日志目录
```bash
mkdir -p storage/logs
chmod 755 storage/logs
```

#### 检查日志文件
```bash
# 查看调度器日志
tail -f storage/logs/faceswap-scheduler.log

# 查看清理任务日志
tail -f storage/logs/faceswap-cleanup.log

# 查看统计日志
tail -f storage/logs/faceswap-stats.log
```

## 生产环境最佳实践

### 1. 使用 Supervisor 管理 Cron

创建 `/etc/supervisor/conf.d/laravel-scheduler.conf`：

```ini
[program:laravel-scheduler]
process_name=%(program_name)s_%(process_num)02d
command=/bin/bash -c 'while true; do php /path/to/your/project/artisan schedule:run --verbose --no-interaction; sleep 60; done'
directory=/path/to/your/project
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/path/to/your/project/storage/logs/scheduler-supervisor.log
```

### 2. 健康检查脚本

创建 `scripts/check_scheduler.sh`：

```bash
#!/bin/bash

PROJECT_PATH="/path/to/your/project"
LOG_FILE="$PROJECT_PATH/storage/logs/scheduler-health.log"

cd $PROJECT_PATH

echo "$(date): 检查调度器状态" >> $LOG_FILE

# 检查调度任务列表
php artisan schedule:list >> $LOG_FILE 2>&1

# 检查最近的日志
if [ -f "storage/logs/faceswap-scheduler.log" ]; then
    echo "最近的调度执行:" >> $LOG_FILE
    tail -5 storage/logs/faceswap-scheduler.log >> $LOG_FILE
else
    echo "警告: 调度日志文件不存在" >> $LOG_FILE
fi

echo "---" >> $LOG_FILE
```

### 3. 监控告警

在调度任务中添加失败通知：

```php
Schedule::command('faceswap:process-pending')
    ->everyMinute()
    ->withoutOverlapping()
    ->runInBackground()
    ->onOneServer()
    ->appendOutputTo(storage_path('logs/faceswap-scheduler.log'))
    ->emailOutputOnFailure('<EMAIL>') // 失败时发送邮件
    ->pingBefore('https://your-monitoring-service.com/ping/before') // 执行前ping
    ->thenPing('https://your-monitoring-service.com/ping/after'); // 执行后ping
```

### 4. 环境变量配置

确保生产环境的 `.env` 文件包含必要的配置：

```env
# 调度器配置
SCHEDULER_ENABLED=true
SCHEDULER_LOG_LEVEL=info

# 邮件配置（用于失败通知）
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# 监控配置
MONITORING_PING_URL=https://your-monitoring-service.com/ping
```

## 故障排除

### 1. 调度任务不执行

```bash
# 检查 cron 是否运行
service cron status

# 检查 cron 日志
tail -f /var/log/cron

# 检查 Laravel 日志
tail -f storage/logs/laravel.log

# 手动测试调度
php artisan schedule:run --verbose
```

### 2. 权限问题

```bash
# 确保正确的文件权限
chown -R www-data:www-data /path/to/your/project
chmod -R 755 /path/to/your/project
chmod -R 775 /path/to/your/project/storage
chmod -R 775 /path/to/your/project/bootstrap/cache
```

### 3. 内存和性能问题

```bash
# 检查内存使用
php artisan schedule:run --verbose --memory

# 优化配置缓存
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 监控仪表板

创建一个简单的监控页面来检查调度器状态：

```php
// routes/web.php
Route::get('/admin/scheduler-status', function () {
    $logs = [];
    
    // 读取最近的日志
    $logFiles = [
        'scheduler' => storage_path('logs/faceswap-scheduler.log'),
        'cleanup' => storage_path('logs/faceswap-cleanup.log'),
        'stats' => storage_path('logs/faceswap-stats.log'),
    ];
    
    foreach ($logFiles as $name => $file) {
        if (file_exists($file)) {
            $logs[$name] = array_slice(file($file), -10);
        }
    }
    
    return view('admin.scheduler-status', compact('logs'));
})->middleware('auth:admin');
```

## 总结

Laravel 11 中调度任务的主要变化：

1. **配置位置变更**: 从 `app/Console/Kernel.php` 移动到 `routes/console.php`
2. **使用 Schedule Facade**: 直接使用 `Schedule::command()` 而不是在 `schedule()` 方法中定义
3. **更简洁的语法**: 不需要继承和重写方法

确保在生产环境中：
- ✅ 正确配置 cron 任务
- ✅ 设置适当的日志记录
- ✅ 配置监控和告警
- ✅ 定期检查调度器健康状态

这样就能确保你的 Laravel Scheduler 在生产环境中稳定运行。