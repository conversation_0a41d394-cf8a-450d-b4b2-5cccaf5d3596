# 个性化绘本订单流程实现文档

## 概述

本文档描述了从购物车到订单完成的完整流程实现，包括EXP物流集成、订单状态管理、支付处理等功能。

## 核心功能

### 1. 购物车到订单流程

#### 1.1 购物车商品组合下单
- 用户可以选择购物车中的部分或全部商品进行下单
- 支持批量商品的价格计算和验证
- 自动验证商品的有效性（预览状态、封面变体等）

#### 1.2 地址信息填写
- 支持收货地址和账单地址分别填写
- 如果不填写账单地址，自动使用收货地址
- 支持从用户地址簿选择或直接输入地址信息
- 地址信息验证（必填字段、格式检查等）

#### 1.3 物流产品选择
- 集成4PX物流API，获取可用的物流产品
- 根据目标国家和包裹信息计算运费
- 提供多种物流选项供用户选择
- 显示预计送达时间和跟踪服务

#### 1.4 支付方式选择
- 目前仅支持Stripe支付
- 创建Stripe Checkout会话或PaymentIntent
- 支付成功后自动启动订单处理流程

### 2. 订单状态管理

#### 2.1 订单状态流转
```
pending (待支付) 
    ↓ (支付完成)
processing (书籍准备中) 
    ↓ (AI处理完成)
preparing (书籍制作中) 
    ↓ (48小时确认期或手动确认)
confirmed (已确认) 
    ↓ (生产完成)
printed (已打印) 
    ↓ (包装完成)
packed (已包装) 
    ↓ (发货)
shipped (已发货) 
    ↓ (送达)
delivered (已送达) 
    ↓ (完成)
completed (已完成)
```

#### 2.2 特殊时间规则
- **4小时修改期**: 订单创建后4小时内可以修改寄语
- **48小时确认期**: 订单支付后48小时内为确认期，超时自动确认
- 确认后进入生产和物流流程，无法修改

### 3. AI换脸处理

#### 3.1 订单处理流程
1. 支付完成后，订单状态变为`processing`
2. 自动为每个订单项创建全绘本AI换脸任务
3. 任务分发到高优先级队列`high_priority_face_swap`
4. AI处理完成后，订单状态变为`preparing`

#### 3.2 处理进度跟踪
- 每个订单项都有独立的处理进度
- 支持实时查询处理状态和进度百分比
- 处理失败时有重试机制和错误处理

### 4. 物流集成

#### 4.1 4PX物流API集成
- 获取可用物流产品列表
- 计算运费和时效
- 创建物流订单
- 跟踪物流状态
- 获取跟踪号和物流信息

#### 4.2 物流订单管理
- 订单确认后自动创建物流订单
- 保存物流请求号和相关数据
- 支持物流状态查询和更新
- 提供跟踪号给用户查询

## API接口

### 订单相关接口

```php
// 创建订单
POST /api/order/create
{
    "cart_item_ids": [1, 2, 3],
    "shipping_address": {...},
    "billing_address": {...}, // 可选
    "shipping_method": "YANWEN_STANDARD",
    "shipping_cost": 15.00,
    "payment_method": "stripe"
}

// 获取物流方式
GET /api/order/shipping-methods?country_code=US&weight=500

// 计算物流费用
POST /api/order/calculate-shipping
{
    "shipping_method": "YANWEN_STANDARD",
    "country_code": "US",
    "weight": 500,
    "dimensions": [20, 15, 2]
}

// 更新订单寄语（4小时内）
PUT /api/order/update-message/{id}
{
    "message": "新的寄语内容"
}

// 确认订单
POST /api/order/confirm/{id}

// 获取订单处理进度
GET /api/order/processing-progress/{id}

// 获取物流跟踪信息
GET /api/order/tracking/{id}
```

### 支付相关接口

```php
// 创建Stripe支付会话
POST /api/stripe/create-checkout-session
{
    "order_id": 123
}

// 支付成功回调
POST /api/stripe/payment-success
{
    "session_id": "cs_xxx",
    "order_id": 123
}
```

## 数据库结构

### 订单表 (orders)
```sql
-- 新增字段
confirmed_at TIMESTAMP NULL -- 确认时间
logistics_request_no VARCHAR(255) NULL -- 物流请求号
logistics_status VARCHAR(50) NULL -- 物流状态
logistics_data JSON NULL -- 物流相关数据
tracking_number VARCHAR(255) NULL -- 跟踪号
```

### 订单项表 (order_items)
```sql
-- 新增字段
face_swap_batch_id VARCHAR(255) NULL -- AI换脸批次ID
processing_progress INT DEFAULT 0 -- 处理进度(0-100)
```

## 后台任务

### 1. 自动确认订单
```bash
# 定时任务，每小时执行一次
php artisan orders:auto-confirm
```

### 2. 订单AI处理
```php
// 队列任务
ProcessOrderFaceSwap::dispatch($orderItemId)
    ->onQueue('high_priority_face_swap');
```

## 配置文件

### 4PX物流配置 (config/services.php)
```php
'4px' => [
    'base_url' => env('4PX_BASE_URL', 'https://open.4px.com/router/api/service'),
    'app_key' => env('4PX_APP_KEY'),
    'app_secret' => env('4PX_APP_SECRET'),
],
```

### Stripe配置
```php
'stripe' => [
    'key' => env('STRIPE_KEY'),
    'secret' => env('STRIPE_SECRET'),
    'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
],
```

## 前端示例

### 订单流程页面
- `resources/views/order-flow-demo.html` - 完整的订单创建流程演示
- 包含购物车、地址填写、物流选择、支付等步骤
- 响应式设计，支持移动端

### 后台管理页面
- `resources/views/admin-order-management.html` - 订单管理后台
- 订单列表、状态管理、进度跟踪
- 物流信息查询和更新

## 部署和运维

### 1. 环境变量配置
```env
# 4PX物流配置
4PX_BASE_URL=https://open.4px.com/router/api/service
4PX_APP_KEY=your_app_key
4PX_APP_SECRET=your_app_secret

# Stripe配置
STRIPE_KEY=pk_test_xxx
STRIPE_SECRET=sk_test_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx
```

### 2. 队列配置
确保高优先级队列正常运行：
```bash
php artisan queue:work --queue=high_priority_face_swap,face_swap,default
```

### 3. 定时任务
在crontab中添加：
```bash
0 * * * * cd /path/to/project && php artisan orders:auto-confirm
```

### 4. 数据库迁移
```bash
php artisan migrate
```

## 测试

### 1. 单元测试
- 订单创建流程测试
- 物流费用计算测试
- 支付回调处理测试

### 2. 集成测试
- 完整订单流程测试
- 4PX API集成测试
- Stripe支付集成测试

### 3. 性能测试
- 大量订单处理性能
- AI换脸队列处理能力
- 数据库查询优化

## 监控和日志

### 1. 关键指标监控
- 订单创建成功率
- 支付成功率
- AI处理完成率
- 物流订单创建成功率

### 2. 日志记录
- 订单状态变更日志
- 支付处理日志
- 物流API调用日志
- AI处理进度日志

### 3. 异常处理
- 支付失败处理
- AI处理失败重试
- 物流API调用失败处理
- 订单状态异常恢复

## 安全考虑

### 1. 数据验证
- 严格的输入验证
- 地址信息格式检查
- 价格计算验证

### 2. 权限控制
- 用户只能操作自己的订单
- 管理员权限分级
- API访问控制

### 3. 支付安全
- Stripe webhook签名验证
- 支付金额二次验证
- 防重复支付机制

## 扩展性

### 1. 多支付方式支持
- PayPal集成
- 支付宝/微信支付
- 银行转账

### 2. 多物流商支持
- DHL、FedEx等
- 本地物流商
- 物流商切换策略

### 3. 国际化支持
- 多语言订单界面
- 多币种支持
- 本地化物流选项

## 总结

本实现提供了完整的个性化绘本订单处理流程，包括：

1. **完整的订单流程**: 从购物车到订单完成的全流程管理
2. **智能物流集成**: 4PX API集成，自动计算运费和时效
3. **灵活的状态管理**: 支持4小时修改期和48小时确认期
4. **高效的AI处理**: 优先级队列确保付费订单快速处理
5. **完善的监控**: 订单进度跟踪和状态管理
6. **安全的支付**: Stripe集成，支持多种支付方式
7. **用户友好**: 清晰的界面和及时的状态反馈

该实现具有良好的扩展性和维护性，可以根据业务需求进行进一步的定制和优化。