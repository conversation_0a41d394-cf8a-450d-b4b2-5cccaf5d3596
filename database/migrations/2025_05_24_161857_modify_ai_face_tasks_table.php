<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ai_face_tasks', function (Blueprint $table) {
            // 添加批次处理相关字段
            $table->string('batch_id')->length(64)->nullable()->index()->after('id'); // 批次ID
            $table->integer('task_index')->nullable()->after('batch_id'); // 任务在批次中的索引
            $table->string('type')->default('task')->after('task_index'); // batch 或 task
            
            // 重命名并保留现有字段的兼容性
            $table->renameColumn('input_image', 'target_image_url')->nullable()->default('')->change();
            $table->renameColumn('face_image', 'face_image_url')->nullable()->change();
            $table->renameColumn('result_image', 'result_image_url')->nullable()->change();
            
            // 添加其他必要字段
            $table->boolean('is_priority')->default(false)->after('status'); // 是否优先处理
            $table->foreignId('page_id')->nullable()->after('user_id'); // 关联的页面ID
            $table->foreignId('variant_id')->nullable()->after('page_id'); // 关联的变体ID
            $table->json('config')->nullable()->after('variant_id'); // 任务配置
            $table->json('result')->nullable()->after('config'); // 完整结果
            $table->integer('total_tasks')->nullable()->after('result'); // 批次总任务数
            $table->integer('completed_tasks')->default(0)->after('total_tasks'); // 已完成任务数
            $table->integer('progress')->default(0)->after('completed_tasks'); // 进度百分比
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ai_face_tasks', function (Blueprint $table) {
            // 删除新增字段
            $table->dropColumn([
                'batch_id', 
                'task_index', 
                'type', 
                'is_priority',
                'page_id',
                'variant_id',
                'config',
                'result',
                'total_tasks',
                'completed_tasks',
                'progress'
            ]);
            
            // 恢复原始字段名
            $table->renameColumn('target_image_url', 'input_image');
            $table->renameColumn('face_image_url', 'face_image');
            $table->renameColumn('result_image_url', 'result_image');
        });
    }
};
