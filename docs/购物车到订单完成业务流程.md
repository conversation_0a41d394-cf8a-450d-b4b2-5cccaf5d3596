# 购物车到订单完成业务流程

## 🛒 购物车到订单完成流程

### 1. **购物车阶段**
```php
// 添加到购物车
POST /api/cart/add
- 验证绘本和选项有效性
- 计算价格（基础价+选项价）
- 创建购物车记录
```

### 2. **结算阶段**
```php
// 获取购物车
GET /api/cart
- 返回购物车商品列表
- 计算总价
- 检查商品可用性

// 创建订单
POST /api/order/create
- 从购物车创建订单
- 生成订单号
- 设置初始状态为 pending
```

### 3. **地址与物流阶段**
```php
// 获取可用地址
GET /api/order/available-addresses
- 返回用户的所有地址列表
- 标记默认地址

// 更新收货地址
PUT /api/order/update-address/{id}
- 更新订单的收货地址
- 重新计算物流费用
- 验证地址有效性

// 计算物流费用
POST /api/order/calculate-shipping
- 基于新地址计算物流费用
- 返回可选物流方式和价格

// 获取物流方式
GET /api/order/shipping-methods
- 基于收货地址查询可用物流
- 返回物流选项列表（快递、标准、经济）

// 确认物流方式
PUT /api/order/select-shipping/{id}
- 选择物流方式
- 更新订单物流费用
- 生成物流单号
```

### 4. **寄语管理阶段**
```php
// 更新订单寄语
PUT /api/order/update-message/{id}
- 更新订单的个性化寄语
- 仅限订单创建后4小时内
- 自动重新处理AI图片

// 寄语更新处理流程
- 验证时间限制（4小时内）
- 更新订单项和预览记录
- 如果已完成换脸，重新处理图片
- 更新处理进度和状态
```

### 5. **支付阶段**
```php
// 创建支付意图
POST /api/stripe/payment-intent
- 订单金额验证
- 创建 Stripe PaymentIntent
- 返回 client_secret

// 前端支付
- 用户完成 Stripe 支付
- 支付成功后调用 webhook

// 重新支付（如需要）
POST /api/order/repay/{id}
- 为支付失败的订单重新创建支付意图
```

### 6. **订单处理阶段**
```php
// 支付确认（Webhook）
POST /api/stripe/webhook
- 验证支付签名
- 更新订单状态为 confirmed
- 触发 OrderPaid 事件

// AI处理
- 分发换脸任务到队列
- 处理每个人脸交换
- 生成最终图片
```

### 7. **书籍生成阶段**
```php
// 生成书籍文件
GenerateOrderBooks Job:
- 合成所有页面
- 生成PDF/图片文件
- 上传到存储
- 更新订单状态
```

### 8. **物流配送阶段**
```php
// 物流处理
- 生成物流单号
- 更新物流状态
- 发送物流通知
- 订单状态更新为 shipped/delivered
```

## 📊 订单状态流转
```
pending → confirmed → ai_processing → ai_completed → 
prepared → printing → packed → shipped → delivered
```

## 💰 价格计算逻辑
```php
总价 = 基础价格 + 封面价格 + 装帧价格 + 礼盒价格 + 物流费用
```

## 🔧 核心服务说明

### OrderService 主要功能
- 订单创建和管理
- 收货地址更新和验证
- 物流方式选择和费用计算
- 订单寄语更新和重新处理
- 订单状态检查和流转
- AI换脸完成度验证
- 物流订单创建条件检查

### 关键业务逻辑
1. **订单创建**: 从购物车或直接下单创建订单
2. **支付处理**: 集成Stripe支付，支持Webhook确认
3. **AI处理**: 异步处理人脸交换任务
4. **物流集成**: 对接4PX物流服务
5. **状态管理**: 完整的订单生命周期管理

### 特殊功能
- **收货地址更新**: 支持在订单处理前更新收货地址
- **物流方式选择**: 支持多种物流方式（快递、标准、经济）
- **寄语更新**: 支持订单创建后4小时内更新寄语
- **自动重新处理**: 寄语更新后自动重新处理AI图片
- **物流费用计算**: 基于地址、重量和地区动态计算
- **多语言支持**: 支持中英文界面和内容
- **实时进度查询**: 支持查询AI处理和物流进度

## 📋 API 端点总览

### 购物车相关
- `POST /api/cart/add` - 添加到购物车
- `GET /api/cart` - 获取购物车
- `DELETE /api/cart/{item}` - 删除购物车项

### 订单相关
- `POST /api/order/create` - 创建订单
- `GET /api/order/list` - 获取订单列表
- `GET /api/order/detail/{id}` - 获取订单详情
- `POST /api/order/cancel/{id}` - 取消订单
- `PUT /api/order/update-address/{id}` - 更新收货地址
- `PUT /api/order/select-shipping/{id}` - 选择物流方式
- `PUT /api/order/update-message/{id}` - 更新订单寄语
- `POST /api/order/repay/{id}` - 重新支付
- `GET /api/order/processing-progress/{id}` - 获取处理进度
- `GET /api/order/tracking/{id}` - 获取物流跟踪信息
- `GET /api/order/available-addresses` - 获取可用地址
- `POST /api/order/calculate-shipping` - 计算物流费用
- `GET /api/order/shipping-methods` - 获取物流方式

### 支付相关
- `POST /api/stripe/payment-intent` - 创建支付意图
- `POST /api/stripe/webhook` - Stripe支付回调

### 物流相关
- `GET /api/shipping/methods` - 获取物流方式
- `POST /api/shipping/calculate` - 计算物流费用

## 🚀 队列任务

### 主要队列任务
- `ProcessOrderFaceSwap` - 订单换脸处理
- `GenerateOrderBooks` - 书籍文件生成
- `OrderPaidNotification` - 支付成功通知
- `OrderCompletedNotification` - 订单完成通知

### 队列优先级
- `high_priority` - 支付处理、AI换脸
- `default` - 普通任务
- `book_generation` - 书籍生成
- `email_notification` - 邮件通知

## 📝 注意事项

### 时间限制
1. **订单寄语更新限制**: 仅在订单创建后4小时内允许更新
2. **收货地址修改限制**: 仅在pending和processing状态下允许修改
3. **物流方式修改限制**: 仅在pending和processing状态下允许修改

### 业务规则
4. **AI处理完成度**: 所有人脸交换任务完成后才能进入下一阶段
5. **支付验证**: 后端会对支付金额进行二次验证
6. **文件存储**: 处理后的文件存储在AWS S3或本地存储

### 物流规则
7. **物流费用计算**: 基于收货地址、包裹重量和地区计算
8. **物流方式选择**: 根据收货地址自动过滤可用的物流方式
9. **物流订单创建**: 需要满足AI完成、支付完成、地址确认等条件

### 数据一致性
10. **状态同步**: 订单状态变更需要同步到相关子表
11. **图片重新处理**: 寄语更新后需要重新处理已完成的图片
12. **进度更新**: AI处理和物流进度需要实时更新

## 🔍 监控和日志

### 关键日志点
- 订单创建和状态变更
- 支付成功和失败
- AI处理进度和错误
- 物流状态更新
- 用户操作记录

### 错误处理
- 支付失败自动重试
- AI处理失败标记并记录
- 物流接口调用异常处理
- 文件上传失败处理

这个流程涵盖了从用户选择商品到最终收到商品的完整业务链路，确保了订单处理的可靠性和用户体验的流畅性。