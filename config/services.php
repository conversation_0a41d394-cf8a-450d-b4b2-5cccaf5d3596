<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    // 4PX物流服务配置
    '4px' => [
        'base_url' => env('4PX_BASE_URL', 'https://open.4px.com/router/api/service'),
        'app_key' => env('4PX_APP_KEY'),
        'app_secret' => env('4PX_APP_SECRET'),
    ],

    // 汇率服务配置
    'exchange' => [
        'api_key' => env('EXCHANGE_API_KEY'),
        'cache_ttl' => env('EXCHANGE_RATE_CACHE_TTL', 3600),
        'timeout' => env('EXCHANGE_API_TIMEOUT', 10),
        'auto_update' => env('EXCHANGE_RATE_AUTO_UPDATE', true),
        'update_interval' => env('EXCHANGE_RATE_UPDATE_INTERVAL', 3600),
        'fallback_rates' => [
            'CNY_USD' => env('RMB_TO_USD_RATE', 0.14),
            'CNY_EUR' => 0.13,
            'CNY_GBP' => 0.11,
        ],
    ],

];