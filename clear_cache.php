<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

$redis = Illuminate\Support\Facades\Redis::connection();
$keys = $redis->keys('face_swap:*');

echo "开始清除换脸任务缓存...\n";
$count = 0;

foreach ($keys as $key) {
    $redis->del($key);
    echo "删除键: {$key}\n";
    $count++;
}

echo "共清除 {$count} 个缓存键\n";
echo "完成!\n"; 