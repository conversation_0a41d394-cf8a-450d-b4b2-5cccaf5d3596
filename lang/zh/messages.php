<?php

return [
    'language_switched' => '语言切换成功',
    'validation_error' => '输入数据验证失败',
    'route_not_found' => '路由未找到',
    'method_not_allowed' => '路由 :route 不支持 :method 方法。支持的方法：:allowed_methods。',
    'server_error' => '服务器内部错误，请稍后重试',
    'unauthorized' => '未经授权的访问',
    'forbidden' => '禁止访问',
    'bad_request' => '错误的请求',
    'unknown_error' => '发生未知错误',
    'model_not_found' => '未找到指定的:model',
    'too_many_requests' => '请求过于频繁，请在 :seconds 秒后重试',
    'http_error' => 'HTTP请求错误',
    'database_error' => '数据库操作失败',
    'validation' => [
        'required' => ':attribute 是必填项',
        'email' => ':attribute 必须是有效的电子邮件地址',
        'min' => [
            'string' => ':attribute 必须至少 :min 个字符',
        ],
        'max' => [
            'string' => ':attribute 不能超过 :max 个字符',
        ],
        'unique' => ':attribute 已经存在',
        'confirmed' => ':attribute 两次输入不一致',
    ],
    'user_not_match' => '用户ID与当前登录用户不匹配',
    'image_not_found' => '图片不存在',
    'invalid_image_path' => '无效的图片路径',
    'skin_merge_success' => '皮肤合并成功',
    'skin_merge_failed' => '皮肤合并失败',
    'text_process_success' => '文字处理成功',
    'text_process_failed' => '文字处理失败',
    'face_swap_success' => '换脸处理成功',
    'face_swap_failed' => '换脸处理失败',
];
