<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 后台管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios@1.3.4/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">订单管理</h1>
            <p class="text-gray-600 mt-2">管理和跟踪所有订单状态</p>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-shopping-cart text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总订单数</p>
                        <p class="text-2xl font-semibold text-gray-900" id="total-orders">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总收入</p>
                        <p class="text-2xl font-semibold text-gray-900" id="total-revenue">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">待处理</p>
                        <p class="text-2xl font-semibold text-gray-900" id="pending-orders">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-600">
                        <i class="fas fa-exclamation-triangle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">问题订单</p>
                        <p class="text-2xl font-semibold text-gray-900" id="problem-orders">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- 订单状态筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">订单状态</label>
                        <select id="status-filter" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="">全部状态</option>
                            <option value="pending">待确认</option>
                            <option value="confirmed">已确认</option>
                            <option value="processing">处理中</option>
                            <option value="shipped">已发货</option>
                            <option value="delivered">已送达</option>
                            <option value="cancelled">已取消</option>
                            <option value="refunded">已退款</option>
                        </select>
                    </div>

                    <!-- 支付状态筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">支付状态</label>
                        <select id="payment-status-filter" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="">全部状态</option>
                            <option value="pending">待支付</option>
                            <option value="paid">已支付</option>
                            <option value="failed">支付失败</option>
                            <option value="refunded">已退款</option>
                        </select>
                    </div>

                    <!-- 日期范围 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">开始日期</label>
                        <input type="date" id="date-from" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">结束日期</label>
                        <input type="date" id="date-to" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                </div>

                <div class="mt-4 flex flex-wrap gap-4">
                    <!-- 搜索框 -->
                    <div class="flex-1 min-w-64">
                        <input type="text" id="search-input" placeholder="搜索订单号、用户邮箱..." 
                               class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>

                    <!-- 操作按钮 -->
                    <button onclick="searchOrders()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        <i class="fas fa-search mr-2"></i>搜索
                    </button>
                    <button onclick="resetFilters()" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                        <i class="fas fa-undo mr-2"></i>重置
                    </button>
                    <button onclick="exportOrders()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        <i class="fas fa-download mr-2"></i>导出
                    </button>
                </div>
            </div>
        </div>

        <!-- 批量操作 -->
        <div class="bg-white rounded-lg shadow mb-6" id="batch-actions" style="display: none;">
            <div class="p-4 border-b">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">已选择 <span id="selected-count">0</span> 个订单</span>
                    <div class="flex gap-2">
                        <select id="batch-status" class="border border-gray-300 rounded-md px-3 py-2">
                            <option value="">选择状态</option>
                            <option value="confirmed">确认订单</option>
                            <option value="processing">开始处理</option>
                            <option value="shipped">标记发货</option>
                            <option value="cancelled">取消订单</option>
                        </select>
                        <button onclick="batchUpdateStatus()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            批量更新
                        </button>
                        <button onclick="clearSelection()" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                            取消选择
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="bg-white rounded-lg shadow">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                订单信息
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                用户信息
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                状态
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                金额
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                创建时间
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody id="orders-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- 订单数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <button id="prev-page-mobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            上一页
                        </button>
                        <button id="next-page-mobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            下一页
                        </button>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700" id="pagination-info">
                                显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">97</span> 条结果
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination-nav">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单详情模态框 -->
    <div id="order-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="modal-title">订单详情</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div id="modal-content">
                    <!-- 订单详情内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let selectedOrders = new Set();
        let orders = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadOrders();
        });

        // 加载统计数据
        async function loadStatistics() {
            try {
                const response = await axios.get('/api/admin/orders/statistics/overview');
                const stats = response.data.data;
                
                document.getElementById('total-orders').textContent = stats.status_stats.total || 0;
                document.getElementById('total-revenue').textContent = '$' + (stats.revenue_stats.total_revenue || 0).toFixed(2);
                document.getElementById('pending-orders').textContent = stats.status_stats.pending || 0;
                document.getElementById('problem-orders').textContent = (stats.status_stats.cancelled || 0) + (stats.payment_stats.failed || 0);
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 加载订单列表
        async function loadOrders(page = 1) {
            try {
                const params = new URLSearchParams({
                    page: page,
                    per_page: 15
                });

                // 添加筛选参数
                const status = document.getElementById('status-filter').value;
                const paymentStatus = document.getElementById('payment-status-filter').value;
                const dateFrom = document.getElementById('date-from').value;
                const dateTo = document.getElementById('date-to').value;
                const search = document.getElementById('search-input').value;

                if (status) params.append('status', status);
                if (paymentStatus) params.append('payment_status', paymentStatus);
                if (dateFrom) params.append('date_from', dateFrom);
                if (dateTo) params.append('date_to', dateTo);
                if (search) params.append('order_number', search);

                const response = await axios.get(`/api/admin/orders?${params}`);
                const data = response.data.data;
                
                orders = data.data;
                currentPage = data.current_page;
                totalPages = data.last_page;

                renderOrdersTable(orders);
                renderPagination(data);
            } catch (error) {
                console.error('加载订单列表失败:', error);
                showNotification('加载订单列表失败', 'error');
            }
        }

        // 渲染订单表格
        function renderOrdersTable(orders) {
            const tbody = document.getElementById('orders-table-body');
            tbody.innerHTML = '';

            orders.forEach(order => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" value="${order.id}" onchange="toggleOrderSelection(${order.id})">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${order.order_number}</div>
                        <div class="text-sm text-gray-500">${order.items?.length || 0} 个商品</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${order.user?.name || '-'}</div>
                        <div class="text-sm text-gray-500">${order.user?.email || '-'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex flex-col space-y-1">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}">${getStatusText(order.status)}</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(order.payment_status)}">${getPaymentStatusText(order.payment_status)}</span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        $${order.total_amount}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${new Date(order.created_at).toLocaleString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewOrder(${order.id})" class="text-blue-600 hover:text-blue-900 mr-2">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editOrder(${order.id})" class="text-green-600 hover:text-green-900 mr-2">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${order.processing_status === 'pending' ? `
                            <button onclick="processOrder(${order.id})" class="text-purple-600 hover:text-purple-900">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'pending': 'bg-yellow-100 text-yellow-800',
                'confirmed': 'bg-blue-100 text-blue-800',
                'processing': 'bg-purple-100 text-purple-800',
                'shipped': 'bg-indigo-100 text-indigo-800',
                'delivered': 'bg-green-100 text-green-800',
                'cancelled': 'bg-red-100 text-red-800',
                'refunded': 'bg-gray-100 text-gray-800'
            };
            return colors[status] || 'bg-gray-100 text-gray-800';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'pending': '待确认',
                'confirmed': '已确认',
                'processing': '处理中',
                'shipped': '已发货',
                'delivered': '已送达',
                'cancelled': '已取消',
                'refunded': '已退款'
            };
            return texts[status] || status;
        }

        // 获取支付状态颜色
        function getPaymentStatusColor(status) {
            const colors = {
                'pending': 'bg-yellow-100 text-yellow-800',
                'paid': 'bg-green-100 text-green-800',
                'failed': 'bg-red-100 text-red-800',
                'refunded': 'bg-gray-100 text-gray-800'
            };
            return colors[status] || 'bg-gray-100 text-gray-800';
        }

        // 获取支付状态文本
        function getPaymentStatusText(status) {
            const texts = {
                'pending': '待支付',
                'paid': '已支付',
                'failed': '支付失败',
                'refunded': '已退款'
            };
            return texts[status] || status;
        }

        // 搜索订单
        function searchOrders() {
            loadOrders(1);
        }

        // 重置筛选条件
        function resetFilters() {
            document.getElementById('status-filter').value = '';
            document.getElementById('payment-status-filter').value = '';
            document.getElementById('date-from').value = '';
            document.getElementById('date-to').value = '';
            document.getElementById('search-input').value = '';
            loadOrders(1);
        }

        // 查看订单详情
        async function viewOrder(orderId) {
            try {
                const response = await axios.get(`/api/admin/orders/${orderId}`);
                const order = response.data.data;
                
                document.getElementById('modal-title').textContent = `订单详情 - ${order.order_number}`;
                document.getElementById('modal-content').innerHTML = renderOrderDetails(order);
                document.getElementById('order-modal').classList.remove('hidden');
            } catch (error) {
                console.error('加载订单详情失败:', error);
                showNotification('加载订单详情失败', 'error');
            }
        }

        // 渲染订单详情
        function renderOrderDetails(order) {
            return `
                <div class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">订单号</label>
                            <p class="mt-1 text-sm text-gray-900">${order.order_number}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">订单状态</label>
                            <select id="order-status" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                <option value="pending" ${order.status === 'pending' ? 'selected' : ''}>待确认</option>
                                <option value="confirmed" ${order.status === 'confirmed' ? 'selected' : ''}>已确认</option>
                                <option value="processing" ${order.status === 'processing' ? 'selected' : ''}>处理中</option>
                                <option value="shipped" ${order.status === 'shipped' ? 'selected' : ''}>已发货</option>
                                <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>已送达</option>
                                <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>已取消</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">支付状态</label>
                            <select id="payment-status" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                <option value="pending" ${order.payment_status === 'pending' ? 'selected' : ''}>待支付</option>
                                <option value="paid" ${order.payment_status === 'paid' ? 'selected' : ''}>已支付</option>
                                <option value="failed" ${order.payment_status === 'failed' ? 'selected' : ''}>支付失败</option>
                                <option value="refunded" ${order.payment_status === 'refunded' ? 'selected' : ''}>已退款</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">处理状态</label>
                            <select id="processing-status" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                <option value="pending" ${order.processing_status === 'pending' ? 'selected' : ''}>待处理</option>
                                <option value="processing" ${order.processing_status === 'processing' ? 'selected' : ''}>处理中</option>
                                <option value="completed" ${order.processing_status === 'completed' ? 'selected' : ''}>已完成</option>
                                <option value="failed" ${order.processing_status === 'failed' ? 'selected' : ''}>处理失败</option>
                            </select>
                        </div>
                    </div>

                    <!-- 用户信息 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">用户信息</h4>
                        <div class="bg-gray-50 p-4 rounded-md">
                            <p><strong>姓名:</strong> ${order.user?.name || '-'}</p>
                            <p><strong>邮箱:</strong> ${order.user?.email || '-'}</p>
                        </div>
                    </div>

                    <!-- 订单商品 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">订单商品</h4>
                        <div class="bg-gray-50 p-4 rounded-md">
                            ${order.items?.map(item => `
                                <div class="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                                    <div>
                                        <p class="font-medium">${item.picbook?.title || '未知商品'}</p>
                                        <p class="text-sm text-gray-500">数量: ${item.quantity}</p>
                                    </div>
                                    <p class="font-medium">$${item.price}</p>
                                </div>
                            `).join('') || '<p>暂无商品信息</p>'}
                        </div>
                    </div>

                    <!-- 备注 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                        <textarea id="order-note" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" placeholder="添加备注..."></textarea>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end space-x-3">
                        <button onclick="closeModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button onclick="updateOrderStatus(${order.id})" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            更新状态
                        </button>
                        ${order.processing_status === 'pending' ? `
                            <button onclick="processOrderFromModal(${order.id})" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                开始处理
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 更新订单状态
        async function updateOrderStatus(orderId) {
            try {
                const status = document.getElementById('order-status').value;
                const paymentStatus = document.getElementById('payment-status').value;
                const processingStatus = document.getElementById('processing-status').value;
                const note = document.getElementById('order-note').value;

                // 更新订单状态
                await axios.put(`/api/admin/orders/${orderId}/status`, {
                    status: status,
                    note: note
                });

                // 更新支付状态
                await axios.put(`/api/admin/orders/${orderId}/payment-status`, {
                    payment_status: paymentStatus,
                    note: note
                });

                // 更新处理状态
                await axios.put(`/api/admin/orders/${orderId}/processing-status`, {
                    processing_status: processingStatus,
                    note: note
                });

                showNotification('订单状态更新成功', 'success');
                closeModal();
                loadOrders(currentPage);
            } catch (error) {
                console.error('更新订单状态失败:', error);
                showNotification('更新订单状态失败', 'error');
            }
        }

        // 处理订单
        async function processOrder(orderId) {
            if (!confirm('确定要开始处理这个订单吗？')) return;

            try {
                const response = await axios.post(`/api/admin/orders/${orderId}/process`);
                showNotification('订单处理成功', 'success');
                loadOrders(currentPage);
            } catch (error) {
                console.error('处理订单失败:', error);
                showNotification('处理订单失败', 'error');
            }
        }

        // 从模态框处理订单
        async function processOrderFromModal(orderId) {
            try {
                const response = await axios.post(`/api/admin/orders/${orderId}/process`);
                showNotification('订单处理成功', 'success');
                closeModal();
                loadOrders(currentPage);
            } catch (error) {
                console.error('处理订单失败:', error);
                showNotification('处理订单失败', 'error');
            }
        }

        // 选择/取消选择订单
        function toggleOrderSelection(orderId) {
            const checkbox = document.querySelector(`input[value="${orderId}"]`);
            if (checkbox.checked) {
                selectedOrders.add(orderId);
            } else {
                selectedOrders.delete(orderId);
            }
            updateBatchActions();
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('#orders-table-body input[type="checkbox"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                const orderId = parseInt(checkbox.value);
                if (selectAll.checked) {
                    selectedOrders.add(orderId);
                } else {
                    selectedOrders.delete(orderId);
                }
            });
            
            updateBatchActions();
        }

        // 更新批量操作显示
        function updateBatchActions() {
            const batchActions = document.getElementById('batch-actions');
            const selectedCount = document.getElementById('selected-count');
            
            if (selectedOrders.size > 0) {
                batchActions.style.display = 'block';
                selectedCount.textContent = selectedOrders.size;
            } else {
                batchActions.style.display = 'none';
            }
        }

        // 批量更新状态
        async function batchUpdateStatus() {
            const status = document.getElementById('batch-status').value;
            if (!status) {
                showNotification('请选择要更新的状态', 'warning');
                return;
            }

            if (!confirm(`确定要将选中的 ${selectedOrders.size} 个订单状态更新为 ${getStatusText(status)} 吗？`)) {
                return;
            }

            try {
                const response = await axios.post('/api/admin/orders/batch-update-status', {
                    order_ids: Array.from(selectedOrders),
                    status: status
                });

                const result = response.data.data;
                showNotification(`批量更新完成，成功: ${result.success_count}，失败: ${result.fail_count}`, 'success');
                
                clearSelection();
                loadOrders(currentPage);
            } catch (error) {
                console.error('批量更新失败:', error);
                showNotification('批量更新失败', 'error');
            }
        }

        // 清除选择
        function clearSelection() {
            selectedOrders.clear();
            document.getElementById('select-all').checked = false;
            document.querySelectorAll('#orders-table-body input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            updateBatchActions();
        }

        // 导出订单
        async function exportOrders() {
            try {
                const params = new URLSearchParams();
                
                // 添加筛选参数
                const status = document.getElementById('status-filter').value;
                const paymentStatus = document.getElementById('payment-status-filter').value;
                const dateFrom = document.getElementById('date-from').value;
                const dateTo = document.getElementById('date-to').value;

                if (status) params.append('status', status);
                if (paymentStatus) params.append('payment_status', paymentStatus);
                if (dateFrom) params.append('date_from', dateFrom);
                if (dateTo) params.append('date_to', dateTo);
                params.append('format', 'csv');

                const response = await axios.post('/api/admin/orders/export', Object.fromEntries(params), {
                    responseType: 'blob'
                });

                // 创建下载链接
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `orders_export_${new Date().toISOString().split('T')[0]}.csv`);
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);

                showNotification('订单导出成功', 'success');
            } catch (error) {
                console.error('导出失败:', error);
                showNotification('导出失败', 'error');
            }
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('order-modal').classList.add('hidden');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 这里可以实现一个通知组件
            alert(message);
        }

        // 渲染分页
        function renderPagination(data) {
            const paginationInfo = document.getElementById('pagination-info');
            const paginationNav = document.getElementById('pagination-nav');
            
            // 更新分页信息
            const from = (data.current_page - 1) * data.per_page + 1;
            const to = Math.min(data.current_page * data.per_page, data.total);
            paginationInfo.innerHTML = `显示第 <span class="font-medium">${from}</span> 到 <span class="font-medium">${to}</span> 条，共 <span class="font-medium">${data.total}</span> 条结果`;
            
            // 生成分页按钮
            let paginationHTML = '';
            
            // 上一页按钮
            if (data.current_page > 1) {
                paginationHTML += `<button onclick="loadOrders(${data.current_page - 1})" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">上一页</button>`;
            }
            
            // 页码按钮
            for (let i = Math.max(1, data.current_page - 2); i <= Math.min(data.last_page, data.current_page + 2); i++) {
                const isActive = i === data.current_page;
                paginationHTML += `<button onclick="loadOrders(${i})" class="relative inline-flex items-center px-4 py-2 border ${isActive ? 'border-blue-500 bg-blue-50 text-blue-600' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'} text-sm font-medium">${i}</button>`;
            }
            
            // 下一页按钮
            if (data.current_page < data.last_page) {
                paginationHTML += `<button onclick="loadOrders(${data.current_page + 1})" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">下一页</button>`;
            }
            
            paginationNav.innerHTML = paginationHTML;
        }
    </script>
</body>
</html>