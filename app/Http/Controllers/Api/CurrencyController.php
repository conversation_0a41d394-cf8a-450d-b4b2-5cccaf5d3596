<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\CurrencyService;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CurrencyController extends Controller
{
    use ApiResponse;

    protected $currencyService;

    public function __construct(CurrencyService $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    /**
     * 获取汇率
     */
    public function getExchangeRate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from' => 'required|string|size:3',
            'to' => 'required|string|size:3',
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $rate = $this->currencyService->getExchangeRate(
                strtoupper($request->from),
                strtoupper($request->to)
            );

            return $this->success([
                'from' => strtoupper($request->from),
                'to' => strtoupper($request->to),
                'rate' => $rate,
                'timestamp' => now()->toISOString()
            ], '汇率获取成功');
        } catch (\Exception $e) {
            return $this->error('汇率获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 币种转换
     */
    public function convertCurrency(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0',
            'from' => 'required|string|size:3',
            'to' => 'required|string|size:3',
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $convertedAmount = $this->currencyService->convertCurrency(
                $request->amount,
                strtoupper($request->from),
                strtoupper($request->to)
            );

            $rate = $this->currencyService->getExchangeRate(
                strtoupper($request->from),
                strtoupper($request->to)
            );

            return $this->success([
                'original_amount' => $request->amount,
                'converted_amount' => $convertedAmount,
                'from' => strtoupper($request->from),
                'to' => strtoupper($request->to),
                'rate' => $rate,
                'timestamp' => now()->toISOString()
            ], '币种转换成功');
        } catch (\Exception $e) {
            return $this->error('币种转换失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 批量获取汇率
     */
    public function getBatchRates(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'base' => 'required|string|size:3',
            'targets' => 'required|array|min:1',
            'targets.*' => 'required|string|size:3',
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $rates = $this->currencyService->getBatchExchangeRates(
                strtoupper($request->base),
                array_map('strtoupper', $request->targets)
            );

            return $this->success([
                'base' => strtoupper($request->base),
                'rates' => $rates,
                'timestamp' => now()->toISOString()
            ], '批量汇率获取成功');
        } catch (\Exception $e) {
            return $this->error('批量汇率获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 获取支持的币种列表
     */
    public function getSupportedCurrencies()
    {
        try {
            $currencies = $this->currencyService->getSupportedCurrencies();

            return $this->success([
                'currencies' => $currencies,
                'count' => count($currencies)
            ], '支持币种列表获取成功');
        } catch (\Exception $e) {
            return $this->error('获取支持币种失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 清除汇率缓存（管理员功能）
     */
    public function clearCache(Request $request)
    {
        try {
            $from = $request->input('from');
            $to = $request->input('to');

            if ($from && $to) {
                $this->currencyService->clearCache(strtoupper($from), strtoupper($to));
                $message = "已清除 {$from} 到 {$to} 的汇率缓存";
            } else {
                $this->currencyService->clearCache();
                $message = '已清除所有汇率缓存';
            }

            return $this->success(null, $message);
        } catch (\Exception $e) {
            return $this->error('清除缓存失败: ' . $e->getMessage(), null, 500);
        }
    }
}
