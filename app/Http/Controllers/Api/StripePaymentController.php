<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Services\StripePaymentService;
use App\Services\OrderService;
use App\Traits\ApiResponse;
use App\Events\OrderPaid;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class StripePaymentController extends Controller
{
    use ApiResponse;

    protected $stripeService;
    protected $orderService;

    public function __construct(StripePaymentService $stripeService, OrderService $orderService)
    {
        $this->stripeService = $stripeService;
        $this->orderService = $orderService;
    }



    /**
     * 处理Stripe Webhook
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleWebhook(Request $request)
    {
        try {
            $payload = $request->getContent();
            $signature = $request->header('Stripe-Signature');

            if (!$signature) {
                Log::error('Stripe webhook signature missing');
                return response()->json(['error' => 'Signature missing'], 400);
            }

            // 处理webhook事件
            $result = $this->stripeService->handleWebhook($payload, $signature);

            return response()->json($result, 200);

        } catch (\Exception $e) {
            Log::error('Stripe webhook handling failed', [
                'error' => $e->getMessage(),
                'signature' => $request->header('Stripe-Signature')
            ]);

            return response()->json(['error' => 'Webhook handling failed'], 400);
        }
    }



    /**
     * 确认PaymentIntent支付
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function confirmPayment(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_intent_id' => 'required|string',
                'order_id' => 'required|integer|exists:orders,id',
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            $paymentIntentId = $request->payment_intent_id;
            $orderId = $request->order_id;
            $userId = Auth::id();

            // 获取订单并验证权限
            $order = Order::findOrFail($orderId);
            
            if ($order->user_id !== $userId) {
                return $this->error(__('order.access_denied'), null, 403);
            }
            if ($order->stripe_payment_intent_id !== $paymentIntentId) {
                return $this->error(__('order.invalid_status'), null, 403);
            }

            // 从Stripe获取PaymentIntent状态
            $paymentIntent = $this->stripeService->getPaymentIntent($paymentIntentId);

            Log::info('PaymentIntent状态检查', [
                'payment_intent_id' => $paymentIntentId,
                'status' => $paymentIntent->status,
                'order_id' => $orderId
            ]);

            // 根据PaymentIntent状态更新订单
            switch ($paymentIntent->status) {
                case 'succeeded':
                    if ($order->payment_status !== Order::PAYMENT_PAID) {
                        $order->setPaymentStatus(Order::PAYMENT_PAID, $paymentIntentId);
                        $order->payment_method = 'stripe';
                        $order->save();

                        // 触发订单支付完成事件
                        event(new OrderPaid($order));

                        // 启动订单处理
                        $this->orderService->startOrderProcessing($order);

                        Log::info('订单支付成功', [
                            'order_id' => $orderId,
                            'payment_intent_id' => $paymentIntentId
                        ]);
                    }

                    return $this->success([
                        'order_id' => $order->id,
                        'payment_status' => 'succeeded',
                        'payment_intent_id' => $paymentIntentId,
                        'order' => $order->fresh()
                    ], __('order.payment_success'));

                case 'requires_payment_method':
                case 'requires_confirmation':
                case 'requires_action':
                    return $this->success([
                        'order_id' => $order->id,
                        'payment_status' => $paymentIntent->status,
                        'payment_intent_id' => $paymentIntentId,
                        'client_secret' => $paymentIntent->client_secret,
                        'next_action' => $paymentIntent->next_action ?? null
                    ], __('order.payment_requires_action'));

                case 'processing':
                    return $this->success([
                        'order_id' => $order->id,
                        'payment_status' => 'processing',
                        'payment_intent_id' => $paymentIntentId
                    ], __('order.please_wait'));

                case 'canceled':
                    return $this->success([
                        'order_id' => $order->id,
                        'payment_status' => 'canceled',
                        'payment_intent_id' => $paymentIntentId
                    ], __('order.payment_cancelled'));

                default:
                    return $this->error(__('order.invalid_status') . $paymentIntent->status, [
                        'payment_status' => $paymentIntent->status,
                        'payment_intent_id' => $paymentIntentId
                    ], 400);
            }

        } catch (\Exception $e) {
            Log::error('确认PaymentIntent支付失败', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $request->payment_intent_id ?? null,
                'order_id' => $request->order_id ?? null,
                'user_id' => Auth::id()
            ]);

            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 检查PaymentIntent状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkPaymentStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_intent_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            $paymentIntentId = $request->payment_intent_id;

            // 从Stripe获取PaymentIntent状态
            $paymentIntent = $this->stripeService->getPaymentIntent($paymentIntentId);

            // 查找对应的订单
            $order = Order::where('stripe_payment_intent_id', $paymentIntentId)->first();

            $response = [
                'payment_intent_id' => $paymentIntentId,
                'status' => $paymentIntent->status,
                'amount' => $paymentIntent->amount,
                'currency' => $paymentIntent->currency,
                'created' => $paymentIntent->created,
            ];

            if ($order) {
                $response['order_id'] = $order->id;
                $response['order_status'] = $order->status;
                $response['order_payment_status'] = $order->payment_status;
            }

            // 如果需要客户端操作，包含client_secret
            if (in_array($paymentIntent->status, ['requires_payment_method', 'requires_confirmation', 'requires_action'])) {
                $response['client_secret'] = $paymentIntent->client_secret;
                $response['next_action'] = $paymentIntent->next_action ?? null;
            }

            return $this->success($response, 'PaymentIntent状态获取成功');

        } catch (\Exception $e) {
            Log::error('检查PaymentIntent状态失败', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $request->payment_intent_id ?? null
            ]);

            return $this->error('检查支付状态失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 取消PaymentIntent
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelPayment(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_intent_id' => 'required|string',
                'order_id' => 'required|integer|exists:orders,id',
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            $paymentIntentId = $request->payment_intent_id;
            $orderId = $request->order_id;
            $userId = Auth::id();

            // 获取订单并验证权限
            $order = Order::findOrFail($orderId);
            
            if ($order->user_id !== $userId) {
                return $this->error('您无权访问此订单', null, 403);
            }

            // 取消PaymentIntent
            $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId);
            
            if ($paymentIntent->status === 'succeeded') {
                return $this->error('支付已成功，无法取消', null, 400);
            }

            if (in_array($paymentIntent->status, ['requires_payment_method', 'requires_confirmation', 'requires_action'])) {
                $paymentIntent->cancel();
                
                Log::info('PaymentIntent已取消', [
                    'payment_intent_id' => $paymentIntentId,
                    'order_id' => $orderId
                ]);
            }

            return $this->success([
                'payment_intent_id' => $paymentIntentId,
                'order_id' => $orderId,
                'status' => 'canceled'
            ], 'PaymentIntent已取消');

        } catch (\Exception $e) {
            Log::error('取消PaymentIntent失败', [
                'error' => $e->getMessage(),
                'payment_intent_id' => $request->payment_intent_id ?? null,
                'order_id' => $request->order_id ?? null
            ]);

            return $this->error('取消支付失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 获取支付配置信息（前端需要）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentConfig()
    {
        return $this->success([
            'publishable_key' => config('stripe.key'),
            'currency' => config('app.currency', 'usd'),
            'country' => config('app.country', 'US'),
            'supported_payment_methods' => ['card'],
            'appearance' => [
                'theme' => 'stripe',
                'variables' => [
                    'colorPrimary' => '#0570de',
                    'colorBackground' => '#ffffff',
                    'colorText' => '#30313d',
                    'colorDanger' => '#df1b41',
                    'fontFamily' => 'Ideal Sans, system-ui, sans-serif',
                    'spacingUnit' => '2px',
                    'borderRadius' => '4px',
                ]
            ]
        ], '获取支付配置成功');
    }


}