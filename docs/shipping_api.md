# 物流API文档

## 概述

新的物流系统提供了以下功能：
1. 更新订单地址时自动查询物流选项
2. 获取3种物流方式：Express、Standard、Economy
3. 支持多币种价格显示
4. 自动更新PaymentIntent金额

## API接口

### 1. 获取物流价格选项

**POST** `/api/shipping/prices`

获取指定国家和包裹信息的物流价格选项。

#### 请求参数

```json
{
  "country_code": "US",     // 必填，2位国家代码
  "weight": 600,            // 可选，重量(克)，默认600 (0.6kg)
  "length": 36.5,           // 可选，长度(cm)，默认36.5 (365mm)
  "width": 26.5,            // 可选，宽度(cm)，默认26.5 (265mm)
  "height": 5.0,            // 可选，高度(cm)，默认5.0 (50mm)
  "currency": "USD"         // 可选，币种，默认USD
}
```

#### 响应示例

```json
{
  "success": true,
  "code": 200,
  "message": "物流价格获取成功",
  "data": {
    "shipping_options": [
      {
        "type": "express",
        "name": "Express Shipping",
        "code": "S5110",
        "cost": 35.00,
        "currency": "USD",
        "original_cost": 250.00,
        "original_currency": "CNY",
        "estimated_days": "5-10",
        "description": "Fastest delivery with tracking"
      },
      {
        "type": "standard",
        "name": "Standard Shipping",
        "code": "S5120",
        "cost": 22.00,
        "currency": "USD",
        "original_cost": 157.14,
        "original_currency": "CNY",
        "estimated_days": "10-20",
        "description": "Good balance of speed and cost"
      },
      {
        "type": "economy",
        "name": "Economy Shipping",
        "code": "S5130",
        "cost": 12.00,
        "currency": "USD",
        "original_cost": 57.14,
        "original_currency": "CNY",
        "estimated_days": "15-30",
        "description": "Most affordable option"
      }
    ],
    "package_info": {
      "weight": 600,
      "dimensions": {
        "length": 36.5,
        "width": 26.5,
        "height": 5.0
      },
      "weight_unit": "g",
      "dimension_unit": "cm"
    }
  }
}
```

### 2. 更新订单地址

**PUT** `/api/order/update-address/{id}`

更新订单地址，系统会自动查询物流选项并更新PaymentIntent。

#### 请求参数

```json
{
  "shipping_address": {
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+1234567890",
    "country": "US",
    "state": "CA",
    "city": "Los Angeles",
    "street": "123 Main St",
    "post_code": "90210"
  }
}
```

#### 响应示例

```json
{
  "success": true,
  "code": 200,
  "message": "订单地址更新成功",
  "data": {
    "order": {
      "id": 123,
      "order_number": "PB20250812123456",
      "status": "pending",
      "payment_status": "pending",
      "shipping_address": {
        "first_name": "John",
        "last_name": "Doe",
        "phone": "+1234567890",
        "country": "US",
        "state": "CA",
        "city": "Los Angeles",
        "street": "123 Main St",
        "post_code": "90210"
      },
      "shipping_options": [
        {
          "type": "express",
          "name": "Express Shipping",
          "code": "S5110",
          "cost": 35.00,
          "currency": "USD",
          "estimated_days": "5-10",
          "description": "Fastest delivery with tracking"
        }
      ]
    },
    "shipping_options": [
      // 物流选项数组
    ]
  }
}
```

### 3. 选择物流方式

**PUT** `/api/order/select-shipping/{id}`

为订单选择具体的物流方式。

#### 请求参数

```json
{
  "shipping_method": "S5120",  // 物流方式代码
  "shipping_cost": 15.00       // 物流费用
}
```

#### 响应示例

```json
{
  "success": true,
  "code": 200,
  "message": "物流方式更新成功",
  "data": {
    "order": {
      "id": 123,
      "shipping_method": "S5120",
      "shipping_cost": 15.00,
      "total_amount": 65.00
    }
  }
}
```

### 4. 获取订单详情

**GET** `/api/order/detail/{id}`

获取订单详情，包含物流选项信息。

#### 响应示例

```json
{
  "success": true,
  "code": 200,
  "message": "订单详情获取成功",
  "data": {
    "order": {
      "id": 123,
      "order_number": "PB20250812123456",
      "status": "pending",
      "payment_status": "pending",
      "shipping_options": [
        {
          "type": "express",
          "name": "Express Shipping",
          "code": "S5110",
          "cost": 35.00,
          "currency": "USD",
          "estimated_days": "5-10",
          "description": "Fastest delivery with tracking"
        },
        {
          "type": "standard",
          "name": "Standard Shipping",
          "code": "S5120",
          "cost": 22.00,
          "currency": "USD",
          "estimated_days": "10-20",
          "description": "Good balance of speed and cost"
        },
        {
          "type": "economy",
          "name": "Economy Shipping",
          "code": "S5130",
          "cost": 12.00,
          "currency": "USD",
          "estimated_days": "15-30",
          "description": "Most affordable option"
        }
      ]
    }
  }
}
```

## 工作流程

1. **创建订单**: 通过购物车checkout创建订单，此时无需地址信息
2. **更新地址**: 调用更新地址API，系统自动查询物流选项
3. **选择物流**: 从返回的物流选项中选择一种
4. **更新支付**: 系统自动更新PaymentIntent金额
5. **完成支付**: 用户完成支付流程

## 物流选项说明

- **Express Shipping**: 时间最短，价格最高，5-10天送达
- **Standard Shipping**: 性价比高，10-20天送达（推荐）
- **Economy Shipping**: 价格最低，15-30天送达

## 币种支持

- USD (美元)
- EUR (欧元)
- GBP (英镑)

系统会自动从人民币转换为用户所在地区的币种。

## 错误处理

如果物流API调用失败，系统会返回默认的物流选项，确保用户体验不受影响。