<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PicbookCoverVariantsSeeder extends Seeder
{
    /**
     * 为picbook_cover_variants表添加模拟数据
     *
     * @return void
     */
    public function run()
    {
        $now = Carbon::now();
        
        // 检查picbook_id=1是否存在
        $picbookExists = DB::table('picbooks')->where('id', 1)->exists();
        
        if (!$picbookExists) {
            $this->command->error('Picbook ID 1不存在，请先确保有该ID的绘本');
            return;
        }
        
        // 定义不同的封面变体数据
        $variants = [
            // 中文版本
            [
                'picbook_id' => 1,
                'language' => 'zh',
                'gender' => 1, // 男
                'skincolor' => 1, // 白色
                'image_url' => 'https://example.com/covers/zh/cover_male_white.jpg',
                'skin_mask_url' => 'https://example.com/masks/cover_skin_mask.png',
                'has_text' => true,
                'text_config' => [
                    'title' => '我的梦幻绘本',
                    'position' => 'top',
                    'font_size' => 24,
                    'color' => '#000000'
                ],
                'has_face' => true,
                'face_config' => [
                    'positions' => [
                        [
                            'x' => 100,
                            'y' => 150,
                            'width' => 80,
                            'height' => 100
                        ]
                    ]
                ],
                'is_published' => true,
                'sort_order' => 1,
                'price' => 99.00,
                'pricesymbol' => '¥',
                'currencycode' => 'USD',
                'is_default' => true
            ],
            [
                'picbook_id' => 1,
                'language' => 'zh',
                'gender' => 1, // 男
                'skincolor' => 2, // 黄色
                'image_url' => 'https://example.com/covers/zh/cover_male_yellow.jpg',
                'skin_mask_url' => 'https://example.com/masks/cover_skin_mask.png',
                'has_text' => true,
                'text_config' => [
                    'title' => '我的梦幻绘本',
                    'position' => 'top',
                    'font_size' => 24,
                    'color' => '#000000'
                ],
                'has_face' => true,
                'face_config' => [
                    'positions' => [
                        [
                            'x' => 100,
                            'y' => 150,
                            'width' => 80,
                            'height' => 100
                        ]
                    ]
                ],
                'is_published' => true,
                'sort_order' => 2,
                'price' => 99.00,
                'pricesymbol' => '¥',
                'currencycode' => 'USD',
                'is_default' => false
            ],
            [
                'picbook_id' => 1,
                'language' => 'zh',
                'gender' => 2, // 女
                'skincolor' => 1, // 白色
                'image_url' => 'https://example.com/covers/zh/cover_female_white.jpg',
                'skin_mask_url' => 'https://example.com/masks/cover_skin_mask.png',
                'has_text' => true,
                'text_config' => [
                    'title' => '我的梦幻绘本',
                    'position' => 'top',
                    'font_size' => 24,
                    'color' => '#FF69B4'
                ],
                'has_face' => true,
                'face_config' => [
                    'positions' => [
                        [
                            'x' => 100,
                            'y' => 150,
                            'width' => 80,
                            'height' => 100
                        ]
                    ]
                ],
                'is_published' => true,
                'sort_order' => 3,
                'price' => 99.00,
                'pricesymbol' => '¥',
                'currencycode' => 'USD',
                'is_default' => false
            ],
            
            // 英文版本
            [
                'picbook_id' => 1,
                'language' => 'en',
                'gender' => 1, // 男
                'skincolor' => 1, // 白色
                'image_url' => 'https://example.com/covers/en/cover_male_white.jpg',
                'skin_mask_url' => 'https://example.com/masks/cover_skin_mask.png',
                'has_text' => true,
                'text_config' => [
                    'title' => 'My Dream Book',
                    'position' => 'top',
                    'font_size' => 24,
                    'color' => '#000000'
                ],
                'has_face' => true,
                'face_config' => [
                    'positions' => [
                        [
                            'x' => 100,
                            'y' => 150,
                            'width' => 80,
                            'height' => 100
                        ]
                    ]
                ],
                'is_published' => true,
                'sort_order' => 1,
                'price' => 14.99,
                'pricesymbol' => '$',
                'currencycode' => 'USD',
                'is_default' => true
            ],
            [
                'picbook_id' => 1,
                'language' => 'en',
                'gender' => 2, // 女
                'skincolor' => 1, // 白色
                'image_url' => 'https://example.com/covers/en/cover_female_white.jpg',
                'skin_mask_url' => 'https://example.com/masks/cover_skin_mask.png',
                'has_text' => true,
                'text_config' => [
                    'title' => 'My Dream Book',
                    'position' => 'top',
                    'font_size' => 24,
                    'color' => '#FF69B4'
                ],
                'has_face' => true,
                'face_config' => [
                    'positions' => [
                        [
                            'x' => 100,
                            'y' => 150,
                            'width' => 80,
                            'height' => 100
                        ]
                    ]
                ],
                'is_published' => true,
                'sort_order' => 2,
                'price' => 14.99,
                'pricesymbol' => '$',
                'currencycode' => 'USD',
                'is_default' => false
            ],
            
            // 精装高级版本
            [
                'picbook_id' => 1,
                'language' => 'zh',
                'gender' => null, // 通用性别
                'skincolor' => null, // 通用肤色
                'image_url' => 'https://example.com/covers/zh/cover_premium.jpg',
                'skin_mask_url' => null,
                'has_text' => true,
                'text_config' => [
                    'title' => '豪华精装版',
                    'position' => 'center',
                    'font_size' => 28,
                    'color' => '#FFD700'
                ],
                'has_face' => false,
                'face_config' => null,
                'is_published' => true,
                'sort_order' => 10,
                'price' => 199.00,
                'pricesymbol' => '¥',
                'currencycode' => 'USD',
                'is_default' => false
            ],
            [
                'picbook_id' => 1,
                'language' => 'en',
                'gender' => null, // 通用性别
                'skincolor' => null, // 通用肤色
                'image_url' => 'https://example.com/covers/en/cover_premium.jpg',
                'skin_mask_url' => null,
                'has_text' => true,
                'text_config' => [
                    'title' => 'Premium Edition',
                    'position' => 'center',
                    'font_size' => 28,
                    'color' => '#FFD700'
                ],
                'has_face' => false,
                'face_config' => null,
                'is_published' => true,
                'sort_order' => 10,
                'price' => 29.99,
                'pricesymbol' => '$',
                'currencycode' => 'USD',
                'is_default' => false
            ]
        ];
        
        // 添加时间戳
        foreach ($variants as &$variant) {
            $variant['created_at'] = $now;
            $variant['updated_at'] = $now;
        }
        
        // 插入数据
        foreach ($variants as $variant) {
            // 检查是否已存在相同的记录
            $exists = DB::table('picbook_cover_variants')
                ->where('picbook_id', $variant['picbook_id'])
                ->where('language', $variant['language'])
                ->whereRaw('IFNULL(gender, 0) = IFNULL(?, 0)', [$variant['gender']])
                ->whereRaw('IFNULL(skincolor, 0) = IFNULL(?, 0)', [$variant['skincolor']])
                ->exists();
                
            if (!$exists) {
                DB::table('picbook_cover_variants')->insert($variant);
                $this->command->info("已添加封面变体: 语言 {$variant['language']}, 性别 {$variant['gender']}, 肤色 {$variant['skincolor']}");
            } else {
                $this->command->warn("跳过已存在的封面变体: 语言 {$variant['language']}, 性别 {$variant['gender']}, 肤色 {$variant['skincolor']}");
            }
        }
        
        $this->command->info('所有封面变体数据已添加完成');
    }
} 