<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('picbook_previews', function (Blueprint $table) {
            $table->id();
            //添加一个请求参数的md5值，当请求一致是，直接返回记录数据，减少请求操作
            $table->string('md5_key')->nullable()->default('')->index()->comment('请求md5加密');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('picbook_id')->constrained()->onDelete('cascade');
            $table->json('preview_data')->nullable();
            $table->json('characters')->nullable();
            $table->string('face_image')->default('');
            $table->string('batch_id')->nullable()->comment('AI换脸批次ID');
            $table->json('face_swap_batch')->nullable();
            $table->boolean('face_swapped')->default(false);
            $table->string('recipient_name')->nullable()->comment('赠与人姓名');
            $table->text('message')->nullable()->comment('赠言/寄语');
            $table->string('cover_type')->default('default')->comment('封面类型');
            $table->string('binding_type')->default('standard')->comment('装帧方式');
            $table->string('gift_box')->default('standard')->comment('礼盒类型');
            $table->string('status')->default('pending')->comment('处理状态');
            $table->integer('preview_count')->default(0)->comment('预览次数');
            $table->timestamp('last_preview_at')->nullable()->comment('最后预览时间');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('picbook_previews');
    }
}; 