<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\ApiController;
use App\Models\Picbook;
use App\Models\PicbookVariant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PicbookController extends ApiController
{
    /**
     * 获取绘本列表
     */
    public function index(Request $request)
    {
        //per_page和page
        $per_page = $request->input('per_page', 15);
        $page = $request->input('page', 1);
        //验证请求
        $validator = Validator::make($request->all(), [
            'keyword' => 'nullable|string',
            'status' => 'nullable|integer|in:0,1,2',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'currencycode' => 'nullable|string|size:3',
            'per_page' => 'nullable|integer|min:15|max:100',
            'page' => 'nullable|integer|min:1',
        ]);
        if ($validator->fails()) {
            return $this->error(
                __('validation.failed'),
                $validator->errors(),
                422
            );
        }
        $query = Picbook::query();
        
        // 搜索条件
        if ($request->has('keyword')) {
            $query->where('default_name', 'like', '%' . $request->keyword . '%');
        }
        
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // 价格范围筛选
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // 货币筛选
        if ($request->has('currencycode')) {
            $query->where('currencycode', $request->currencycode);
        }

        $picbooks = $query->orderBy('created_at', 'desc')
            ->paginate($per_page, ['id', 'default_name', 'default_cover', 'price', 'currencycode', 'status','price','rating','has_choices','has_question','supported_languages','supported_genders','supported_skincolors','tags'], 'page', $page);
        return $this->success($picbooks, __('picbook.list_success'));
    }

    /**
     * 创建绘本
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'default_name' => 'required|string|max:255',
            'default_cover' => 'required|string',
            'pricesymbol' => 'required|string|max:10',
            'price' => 'required|numeric|min:0',
            'currencycode' => 'required|string|size:3',
            'total_pages' => 'required|integer|min:1',
            'supported_languages' => 'required|array',
            'supported_genders' => 'required|array',
            'supported_skincolors' => 'required|array',
            'none_skin' => 'nullable|string',
            'tags' => 'nullable|array',
            'has_choices' => 'boolean',
            'has_question' => 'boolean',
            'status' => 'required|integer|in:0,1,2',
            'choices_type' => 'required|integer|in:0,1,2',
            'character_count' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->error(
                __('validation.failed'),
                $validator->errors(),
                422
            );
        }

        // 验证选择类型与总页数的关系
        if ($request->choices_type > 0) {
            $required_pages = $request->choices_type == 1 ? 8 : 16;
            $type_name = __('picbook.choices_type.type_names.' . $request->choices_type);
            if ($request->total_pages < $required_pages) {
                return $this->error(
                    __('validation.failed'),
                    ['total_pages' => [__('picbook.choices_type.min_pages_error', [
                        'type' => $type_name,
                        'pages' => $required_pages
                    ])]],
                    422
                );
            }
        }

        $picbook = Picbook::create($request->all());
        return $this->success($picbook);
    }

    /**
     * 获取绘本详情
     */
    public function show($id)
    {
        $picbook = Picbook::findOrFail($id);
        //获取变体数量
        $variants = PicbookVariant::where('picbook_id', $id)->count();
        $picbook->variants_count = $variants;
        return $this->success($picbook);
    }

    /**
     * 更新绘本
     */
    public function update(Request $request, $id)
    {
        $picbook = Picbook::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'default_name' => 'string|max:255',
            'default_cover' => 'string',
            'pricesymbol' => 'string|max:10',
            'price' => 'numeric|min:0',
            'currencycode' => 'string|size:3',
            'total_pages' => 'integer|min:1',
            'supported_languages' => 'array',
            'supported_genders' => 'array',
            'supported_skincolors' => 'array',
            'none_skin' => 'nullable|string',
            'tags' => 'nullable|array',
            'has_choices' => 'boolean',
            'has_question' => 'boolean',
            'status' => 'integer|in:0,1,2',
            'choices_type' => 'integer|in:0,1,2',
            'character_count' => 'integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->error(
                __('validation.failed'),
                $validator->errors(),
                422
            );
        }

        // 验证选择类型与总页数的关系
        if ($request->has('choices_type') && $request->choices_type > 0) {
            $total_pages = $request->input('total_pages', $picbook->total_pages);
            $required_pages = $request->choices_type == 1 ? 8 : 16;
            $type_name = __('picbook.choices_type.type_names.' . $request->choices_type);
            if ($total_pages < $required_pages) {
                return $this->error(
                    __('validation.failed'),
                    ['total_pages' => [__('picbook.choices_type.min_pages_error', [
                        'type' => $type_name,
                        'pages' => $required_pages
                    ])]],
                    422
                );
            }
        }

        $picbook->update($request->all());
        return $this->success($picbook);
    }

    /**
     * 删除绘本
     */
    public function destroy($id)
    {
        $picbook = Picbook::findOrFail($id);
        $picbook->delete();
        return $this->success(null, '删除成功');
    }

    /**
     * 批量处理绘本图片
     * 
     * @param Request $request
     * @param int $id 绘本ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchProcess(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'process_covers' => 'boolean',
                'process_pages' => 'boolean',
                'languages' => 'sometimes|array',
                'languages.*' => 'string|size:2',
            ]);
            
            if ($validator->fails()) {
                return $this->error(__('validation.failed'), $validator->errors(), 422);
            }
            
            $picbook = Picbook::with(['coverVariants', 'pages.variants'])->findOrFail($id);
            
            // 批量处理图片
            $options = [
                'process_covers' => $request->input('process_covers', true),
                'process_pages' => $request->input('process_pages', true)
            ];
            
            $results = $picbook->batchProcessAllVariants($options);
            
            return $this->success($results, __('picbook.batch_process_success'));
            
        } catch (\Exception $e) {
            \Log::error('批量处理绘本失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $id
            ]);
            
            return $this->error(__('picbook.batch_process_failed') . ': ' . $e->getMessage());
        }
    }
    
    /**
     * 批量发布绘本封面
     * 
     * @param Request $request
     * @param int $id 绘本ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchPublishCovers(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'cover_ids' => 'required|array',
                'cover_ids.*' => 'integer|exists:picbook_cover_variants,id',
                'is_published' => 'required|boolean'
            ]);
            
            if ($validator->fails()) {
                return $this->error(__('validation.failed'), $validator->errors(), 422);
            }
            
            $picbook = Picbook::findOrFail($id);
            $coverIds = $request->cover_ids;
            $isPublished = $request->is_published;
            
            $affected = PicbookCoverVariant::whereIn('id', $coverIds)
                ->where('picbook_id', $picbook->id)
                ->update(['is_published' => $isPublished]);
            
            return $this->success([
                'affected_count' => $affected
            ], __('picbook.batch_publish_success'));
            
        } catch (\Exception $e) {
            \Log::error('批量发布封面失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $id
            ]);
            
            return $this->error(__('picbook.batch_publish_failed') . ': ' . $e->getMessage());
        }
    }
    
    /**
     * 批量发布绘本页面
     * 
     * @param Request $request
     * @param int $id 绘本ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchPublishPages(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'page_ids' => 'required|array',
                'page_ids.*' => 'integer|exists:picbook_pages,id',
                'is_published' => 'required|boolean'
            ]);
            
            if ($validator->fails()) {
                return $this->error(__('validation.failed'), $validator->errors(), 422);
            }
            
            $picbook = Picbook::findOrFail($id);
            $pageIds = $request->page_ids;
            $isPublished = $request->is_published;
            
            // 验证页面属于该绘本
            $validPageIds = PicbookPage::where('picbook_id', $picbook->id)
                ->whereIn('id', $pageIds)
                ->pluck('id')
                ->toArray();
            
            if (count($validPageIds) !== count($pageIds)) {
                return $this->error(__('picbook.invalid_page_ids'));
            }
            
            // 更新所有相关页面变体的发布状态
            $affected = PicbookPageVariant::whereIn('page_id', $validPageIds)
                ->update(['is_published' => $isPublished]);
            
            return $this->success([
                'affected_count' => $affected
            ], __('picbook.batch_publish_success'));
            
        } catch (\Exception $e) {
            \Log::error('批量发布页面失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $id
            ]);
            
            return $this->error(__('picbook.batch_publish_failed') . ': ' . $e->getMessage());
        }
    }
    
    /**
     * 创建绘本封面变体
     * 
     * @param Request $request
     * @param int $id 绘本ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeCoverVariant(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'language' => 'required|string|size:2',
                'gender' => 'required|integer|in:1,2',
                'skincolor' => 'required|integer|in:1,2,3',
                'image_url' => 'required|string',
                'skin_mask_url' => 'nullable|string',
                'has_text' => 'boolean',
                'text_config' => 'nullable|array',
                'has_face' => 'boolean',
                'face_config' => 'nullable|array',
                'is_published' => 'boolean|default:false',
                'sort_order' => 'nullable|integer|min:0'
            ]);
            
            if ($validator->fails()) {
                return $this->error(__('validation.failed'), $validator->errors(), 422);
            }
            
            $picbook = Picbook::findOrFail($id);
            
            // 检查语言、性别、肤色是否在绘本支持范围内
            if (!in_array($request->language, $picbook->supported_languages)) {
                return $this->error(__('picbook.language_not_supported'));
            }
            if (!in_array($request->gender, $picbook->supported_genders)) {
                return $this->error(__('picbook.gender_not_supported'));
            }
            if (!in_array($request->skincolor, $picbook->supported_skincolors)) {
                return $this->error(__('picbook.skincolor_not_supported'));
            }
            
            // 检查是否已存在相同配置的封面变体
            $exists = \App\Models\PicbookCoverVariant::where([
                'picbook_id' => $id,
                'language' => $request->language,
                'gender' => $request->gender,
                'skincolor' => $request->skincolor
            ])->exists();
            
            if ($exists) {
                return $this->error(__('picbook.cover_variant_exists'));
            }
            
            // 创建新封面变体
            $coverVariant = new \App\Models\PicbookCoverVariant();
            $coverVariant->picbook_id = $id;
            $coverVariant->language = $request->language;
            $coverVariant->gender = $request->gender;
            $coverVariant->skincolor = $request->skincolor;
            $coverVariant->image_url = $request->image_url;
            $coverVariant->skin_mask_url = $request->skin_mask_url ?? null;
            $coverVariant->has_text = $request->has_text ?? false;
            $coverVariant->text_config = $request->text_config ?? null;
            $coverVariant->has_face = $request->has_face ?? false;
            $coverVariant->face_config = $request->face_config ?? null;
            $coverVariant->is_published = $request->is_published ?? false;
            $coverVariant->sort_order = $request->sort_order ?? 0;
            $coverVariant->save();
            
            return $this->success($coverVariant, __('picbook.cover_variant_create_success'));
            
        } catch (\Exception $e) {
            \Log::error('创建封面变体失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $id
            ]);
            
            return $this->error(__('picbook.cover_variant_create_failed') . ': ' . $e->getMessage());
        }
    }
    
    /**
     * 获取绘本封面变体列表
     * 
     * @param Request $request
     * @param int $id 绘本ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCoverVariants(Request $request, $id)
    {
        try {
            $picbook = Picbook::findOrFail($id);
            
            $query = \App\Models\PicbookCoverVariant::where('picbook_id', $id);
            
            // 语言筛选
            if ($request->has('language')) {
                $query->where('language', $request->language);
            }
            
            // 性别筛选
            if ($request->has('gender')) {
                $query->where('gender', $request->gender);
            }
            
            // 肤色筛选
            if ($request->has('skincolor')) {
                $query->where('skincolor', $request->skincolor);
            }
            
            // 发布状态筛选
            if ($request->has('is_published')) {
                $query->where('is_published', $request->boolean('is_published'));
            }
            
            $coverVariants = $query->orderBy('sort_order')
                ->orderBy('created_at', 'desc')
                ->paginate($request->input('per_page', 15));
                
            return $this->success($coverVariants, __('picbook.cover_variants_list_success'));
            
        } catch (\Exception $e) {
            \Log::error('获取封面变体列表失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $id
            ]);
            
            return $this->error(__('picbook.cover_variants_list_failed') . ': ' . $e->getMessage());
        }
    }
    
    /**
     * 更新绘本封面变体
     * 
     * @param Request $request
     * @param int $id 绘本ID
     * @param int $variantId 封面变体ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateCoverVariant(Request $request, $id, $variantId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'language' => 'string|size:2',
                'gender' => 'integer|in:1,2',
                'skincolor' => 'integer|in:1,2,3',
                'image_url' => 'string',
                'skin_mask_url' => 'nullable|string',
                'has_text' => 'boolean',
                'text_config' => 'nullable|array',
                'has_face' => 'boolean',
                'face_config' => 'nullable|array',
                'is_published' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            
            if ($validator->fails()) {
                return $this->error(__('validation.failed'), $validator->errors(), 422);
            }
            
            $picbook = Picbook::findOrFail($id);
            
            $coverVariant = \App\Models\PicbookCoverVariant::where('picbook_id', $id)
                ->where('id', $variantId)
                ->firstOrFail();
                
            // 检查语言、性别、肤色是否在绘本支持范围内
            if ($request->has('language') && !in_array($request->language, $picbook->supported_languages)) {
                return $this->error(__('picbook.language_not_supported'));
            }
            if ($request->has('gender') && !in_array($request->gender, $picbook->supported_genders)) {
                return $this->error(__('picbook.gender_not_supported'));
            }
            if ($request->has('skincolor') && !in_array($request->skincolor, $picbook->supported_skincolors)) {
                return $this->error(__('picbook.skincolor_not_supported'));
            }
            
            // 检查更新后是否会与已有封面变体冲突
            if ($request->has('language') || $request->has('gender') || $request->has('skincolor')) {
                $language = $request->language ?? $coverVariant->language;
                $gender = $request->gender ?? $coverVariant->gender;
                $skincolor = $request->skincolor ?? $coverVariant->skincolor;
                
                $exists = \App\Models\PicbookCoverVariant::where([
                    'picbook_id' => $id,
                    'language' => $language,
                    'gender' => $gender,
                    'skincolor' => $skincolor
                ])
                ->where('id', '!=', $variantId)
                ->exists();
                
                if ($exists) {
                    return $this->error(__('picbook.cover_variant_exists'));
                }
            }
            
            // 更新封面变体
            if ($request->has('language')) $coverVariant->language = $request->language;
            if ($request->has('gender')) $coverVariant->gender = $request->gender;
            if ($request->has('skincolor')) $coverVariant->skincolor = $request->skincolor;
            if ($request->has('image_url')) $coverVariant->image_url = $request->image_url;
            if ($request->has('skin_mask_url')) $coverVariant->skin_mask_url = $request->skin_mask_url;
            if ($request->has('has_text')) $coverVariant->has_text = $request->has_text;
            if ($request->has('text_config')) $coverVariant->text_config = $request->text_config;
            if ($request->has('has_face')) $coverVariant->has_face = $request->has_face;
            if ($request->has('face_config')) $coverVariant->face_config = $request->face_config;
            if ($request->has('is_published')) $coverVariant->is_published = $request->is_published;
            if ($request->has('sort_order')) $coverVariant->sort_order = $request->sort_order;
            
            $coverVariant->save();
            
            return $this->success($coverVariant, __('picbook.cover_variant_update_success'));
            
        } catch (\Exception $e) {
            \Log::error('更新封面变体失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $id,
                'variant_id' => $variantId
            ]);
            
            return $this->error(__('picbook.cover_variant_update_failed') . ': ' . $e->getMessage());
        }
    }
    
    /**
     * 删除绘本封面变体
     * 
     * @param int $id 绘本ID
     * @param int $variantId 封面变体ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteCoverVariant($id, $variantId)
    {
        try {
            $coverVariant = \App\Models\PicbookCoverVariant::where('picbook_id', $id)
                ->where('id', $variantId)
                ->firstOrFail();
                
            $coverVariant->delete();
            
            return $this->success(null, __('picbook.cover_variant_delete_success'));
            
        } catch (\Exception $e) {
            \Log::error('删除封面变体失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $id,
                'variant_id' => $variantId
            ]);
            
            return $this->error(__('picbook.cover_variant_delete_failed') . ': ' . $e->getMessage());
        }
    }
} 