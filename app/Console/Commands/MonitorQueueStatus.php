<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SimpleFaceSwapService;
use App\Models\AiFaceTask;

class MonitorQueueStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'faceswap:monitor-queue 
                            {--user= : 监控特定用户的队列状态}
                            {--interval=5 : 监控间隔（秒）}
                            {--count=10 : 监控次数，0表示无限循环}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '实时监控换脸队列状态';

    protected $faceSwapService;

    public function __construct(SimpleFaceSwapService $faceSwapService)
    {
        parent::__construct();
        $this->faceSwapService = $faceSwapService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user');
        $interval = (int) $this->option('interval');
        $count = (int) $this->option('count');
        
        $this->info('开始监控换脸队列状态...');
        $this->info('监控间隔: ' . $interval . '秒');
        
        if ($userId) {
            $this->info('监控用户: ' . $userId);
        }
        
        if ($count > 0) {
            $this->info('监控次数: ' . $count);
        } else {
            $this->info('持续监控 (按 Ctrl+C 停止)');
        }
        
        $this->line('');
        
        $iteration = 0;
        
        while ($count === 0 || $iteration < $count) {
            $iteration++;
            
            $this->displayHeader($iteration);
            
            try {
                // 显示全局队列统计
                $this->displayGlobalStats();
                
                // 如果指定了用户，显示用户特定信息
                if ($userId) {
                    $this->displayUserStats($userId);
                }
                
                $this->line('');
                
            } catch (\Exception $e) {
                $this->error('监控过程中发生错误: ' . $e->getMessage());
            }
            
            if ($count === 0 || $iteration < $count) {
                sleep($interval);
            }
        }
        
        $this->info('监控完成');
    }
    
    private function displayHeader($iteration)
    {
        $this->line(str_repeat('=', 80));
        $this->info('监控轮次: ' . $iteration . ' | 时间: ' . now()->format('Y-m-d H:i:s'));
        $this->line(str_repeat('-', 80));
    }
    
    private function displayGlobalStats()
    {
        $stats = [
            'pending_tasks' => AiFaceTask::where('status', 'pending')->where('type', 'task')->count(),
            'processing_tasks' => AiFaceTask::where('status', 'processing')->where('type', 'task')->count(),
            'high_priority_pending' => AiFaceTask::where('status', 'pending')
                ->where('type', 'task')
                ->where('is_priority', true)
                ->count(),
            'normal_priority_pending' => AiFaceTask::where('status', 'pending')
                ->where('type', 'task')
                ->where('is_priority', false)
                ->count(),
        ];
        
        $this->table(
            ['指标', '数量'],
            [
                ['待处理任务', $stats['pending_tasks']],
                ['处理中任务', $stats['processing_tasks']],
                ['高优先级待处理', $stats['high_priority_pending']],
                ['普通优先级待处理', $stats['normal_priority_pending']],
                ['总计活跃任务', $stats['pending_tasks'] + $stats['processing_tasks']],
            ]
        );
    }
    
    private function displayUserStats($userId)
    {
        $this->line('');
        $this->info('用户 ' . $userId . ' 的队列状态:');
        
        $queueStatus = $this->faceSwapService->getQueueStatus($userId);
        
        if ($queueStatus['success']) {
            $queueInfo = $queueStatus['queue_info'];
            $userTasks = $queueStatus['user_tasks'];
            
            $this->table(
                ['指标', '值'],
                [
                    ['用户待处理任务', $queueInfo['user_pending']],
                    ['用户处理中任务', $queueInfo['user_processing']],
                    ['队列中位置', $queueInfo['user_position']],
                    ['预估等待时间', $queueInfo['estimated_wait_time_formatted']],
                    ['用户批次数', count($userTasks)],
                ]
            );
            
            if (!empty($userTasks)) {
                $this->line('');
                $this->info('用户批次详情:');
                
                $batchData = [];
                foreach ($userTasks as $task) {
                    $batchData[] = [
                        $task['batch_id'],
                        $task['status'],
                        $task['progress'] . '%',
                        $task['completed_tasks'] . '/' . $task['total_tasks'],
                        $task['is_priority'] ? '是' : '否',
                        $task['created_at']->format('H:i:s'),
                    ];
                }
                
                $this->table(
                    ['批次ID', '状态', '进度', '完成/总计', '优先级', '创建时间'],
                    $batchData
                );
            }
        } else {
            $this->error('获取用户队列状态失败: ' . $queueStatus['error']);
        }
    }
}