# 生产环境配置示例 - Horizon + Reverb + FaceSwap

# 应用基础配置
APP_NAME="DreamAze FaceSwap"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Redis 配置（Horizon 和 Reverb 都需要）
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Horizon 配置
HORIZON_DOMAIN=your-domain.com
HORIZON_PATH=admin/horizon

# Reverb WebSocket 配置
REVERB_APP_ID=your-app-id
REVERB_APP_KEY=your-app-key
REVERB_APP_SECRET=your-app-secret
REVERB_HOST=your-domain.com
REVERB_PORT=443
REVERB_SCHEME=https
REVERB_SERVER_HOST=0.0.0.0
REVERB_SERVER_PORT=8080

# 队列配置
QUEUE_CONNECTION=redis

# 换脸服务配置
FACE_SWAP_API_URL=https://your-faceswap-api.com
FACE_SWAP_API_KEY=your-api-key
FACE_SWAP_TIMEOUT=600
FACE_SWAP_MAX_RETRIES=3

# 邮件配置（用于错误通知）
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# 管理员邮箱（用于调度器错误通知）
ADMIN_EMAIL=<EMAIL>