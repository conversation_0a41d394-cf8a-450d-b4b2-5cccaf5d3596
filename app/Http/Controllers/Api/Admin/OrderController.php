<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Api\ApiController;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Services\OrderService;
use App\Services\EnhancedPicbookProcessor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class OrderController extends ApiController
{
    protected $orderService;
    protected $picbookProcessor;

    public function __construct(OrderService $orderService, EnhancedPicbookProcessor $picbookProcessor)
    {
        $this->orderService = $orderService;
        $this->picbookProcessor = $picbookProcessor;
    }

    /**
     * 获取订单列表
     */
    public function index(Request $request)
    {
        $query = Order::with(['user', 'items.picbook']);
        
        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 支付状态筛选
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // 处理状态筛选
        if ($request->filled('processing_status')) {
            $query->where('processing_status', $request->processing_status);
        }

        // 物流状态筛选
        if ($request->filled('shipping_status')) {
            $query->where('shipping_status', $request->shipping_status);
        }

        // 用户搜索
        if ($request->filled('user_search')) {
            $userSearch = $request->user_search;
            $query->whereHas('user', function ($q) use ($userSearch) {
                $q->where('name', 'like', "%{$userSearch}%")
                  ->orWhere('email', 'like', "%{$userSearch}%");
            });
        }

        // 订单号搜索
        if ($request->filled('order_number')) {
            $query->where('order_number', 'like', "%{$request->order_number}%");
        }

        // 日期范围筛选
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', Carbon::parse($request->date_from)->startOfDay());
        }
        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', Carbon::parse($request->date_to)->endOfDay());
        }

        // 金额范围筛选
        if ($request->filled('amount_from')) {
            $query->where('total_amount', '>=', $request->amount_from);
        }
        if ($request->filled('amount_to')) {
            $query->where('total_amount', '<=', $request->amount_to);
        }

        // 排序
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $orders = $query->paginate($request->input('per_page', 15));

        return $this->success($orders, '订单列表获取成功');
    }

    /**
     * 获取订单详情
     */
    public function show($id)
    {
        $order = Order::with([
            'user',
            'items.picbook'
        ])->findOrFail($id);

        return $this->success($order, '订单详情获取成功');
    }

    /**
     * 更新订单状态
     */
    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,confirmed,processing,shipped,delivered,cancelled,refunded',
            'note' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            DB::beginTransaction();

            $order = Order::findOrFail($id);
            $oldStatus = $order->status;
            $newStatus = $request->status;

            // 状态变更逻辑验证
            if (!$this->isValidStatusTransition($oldStatus, $newStatus)) {
                return $this->error('无效的状态变更', null, 400);
            }

            $order->status = $newStatus;
            $order->save();

            // 记录状态变更日志
            $this->logStatusChange($order, $oldStatus, $newStatus, $request->note);

            // 处理状态变更后的业务逻辑
            $this->handleStatusChange($order, $oldStatus, $newStatus);

            DB::commit();

            return $this->success($order, '订单状态更新成功');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('订单状态更新失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error('订单状态更新失败', null, 500);
        }
    }

    /**
     * 更新支付状态
     */
    public function updatePaymentStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'payment_status' => 'required|in:pending,paid,failed,refunded,partial_refund',
            'note' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $order = Order::findOrFail($id);
            $oldPaymentStatus = $order->payment_status;
            
            $order->payment_status = $request->payment_status;
            
            if ($request->payment_status === 'paid' && $oldPaymentStatus !== 'paid') {
                $order->paid_at = now();
                
                // 支付成功后自动开始处理
                if ($order->status === Order::STATUS_PENDING) {
                    $order->status = Order::STATUS_PROCESSING;
                    $order->processing_status = 'pending';
                }
            }

            $order->save();

            // 记录支付状态变更日志
            $this->logPaymentStatusChange($order, $oldPaymentStatus, $request->payment_status, $request->note);

            return $this->success($order, '支付状态更新成功');

        } catch (\Exception $e) {
            Log::error('支付状态更新失败', [
                'order_id' => $id,
                'error' => $e->getMessage()
            ]);

            return $this->error('支付状态更新失败', null, 500);
        }
    }

    /**
     * 更新处理状态
     */
    public function updateProcessingStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'processing_status' => 'required|in:pending,processing,completed,failed',
            'note' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $order = Order::findOrFail($id);
            $oldProcessingStatus = $order->processing_status;
            
            $order->processing_status = $request->processing_status;
            
            if ($request->processing_status === 'completed') {
                $order->processed_at = now();
                
                // 处理完成后更新订单状态
                if ($order->status === Order::STATUS_CONFIRMED) {
                    $order->status = Order::STATUS_AI_PROCESSING;
                }
            }

            $order->save();

            // 记录处理状态变更日志
            $this->logProcessingStatusChange($order, $oldProcessingStatus, $request->processing_status, $request->note);

            return $this->success($order, '处理状态更新成功');

        } catch (\Exception $e) {
            Log::error('处理状态更新失败', [
                'order_id' => $id,
                'error' => $e->getMessage()
            ]);

            return $this->error('处理状态更新失败', null, 500);
        }
    }

    /**
     * 手动触发订单处理
     */
    public function processOrder(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'dedication_text' => 'nullable|string|max:200'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $order = Order::with('items.picbook')->findOrFail($id);

            // 检查订单是否可以处理
            if ($order->payment_status !== 'paid') {
                return $this->error('订单未支付，无法处理', null, 400);
            }

            if ($order->processing_status === 'processing') {
                return $this->error('订单正在处理中', null, 400);
            }

            if ($order->processing_status === 'completed') {
                return $this->error('订单已处理完成', null, 400);
            }

            // 更新处理状态
            $order->processing_status = 'processing';
            $order->save();

            // 调用图片处理服务
            $result = $this->picbookProcessor->processOrderImages(
                $order->id,
                $request->dedication_text
            );

            if ($result['success']) {
                $order->processing_status = 'completed';
                $order->processed_at = now();
                $order->save();

                $this->logProcessingStatusChange($order, 'processing', 'completed', '手动处理完成');

                return $this->success($result, '订单处理成功');
            } else {
                $order->processing_status = 'failed';
                $order->save();

                $this->logProcessingStatusChange($order, 'processing', 'failed', $result['message']);

                return $this->error('订单处理失败: ' . $result['message'], null, 500);
            }

        } catch (\Exception $e) {
            Log::error('订单处理失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error('订单处理失败', null, 500);
        }
    }

    /**
     * 获取订单绘本预览数据
     */
    public function getOrderPreview($id)
    {
        try {
            $order = Order::with([
                'items.preview.picbook.pages',
                'user'
            ])->findOrFail($id);

            // 检查订单状态
            if (!in_array($order->status, [Order::STATUS_AI_COMPLETED, Order::STATUS_CONFIRMED])) {
                return $this->error('订单状态不允许预览', null, 400);
            }

            $previewData = [
                'order_info' => [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'status' => $order->status,
                    'status_text' => $order->status_text,
                    'created_at' => $order->created_at,
                    'confirmed_at' => $order->confirmed_at,
                    'total' => $order->total,
                ],
                'user_info' => [
                    'id' => $order->user->id,
                    'name' => $order->user->name,
                    'email' => $order->user->email,
                ],
                'items' => []
            ];

            foreach ($order->items as $item) {
                if (!$item->preview) {
                    continue;
                }

                $preview = $item->preview;
                $previewDataJson = $preview->preview_data ?? [];

                $itemData = [
                    'item_id' => $item->id,
                    'picbook' => [
                        'id' => $preview->picbook->id,
                        'name' => $preview->picbook->name,
                        'description' => $preview->picbook->description,
                    ],
                    'character_info' => [
                        'full_name' => $previewDataJson['full_name'] ?? '',
                        'language' => $previewDataJson['language'] ?? 'en',
                        'gender' => $previewDataJson['gender'] ?? 1,
                        'skincolor' => $previewDataJson['skincolor'] ?? 1,
                    ],
                    'personalization' => [
                        'recipient_name' => $item->recipient_name,
                        'message' => $item->message,
                        'cover_type' => $item->cover_type,
                        'binding_type' => $item->binding_type,
                        'gift_box' => $item->gift_box,
                    ],
                    'face_images' => $previewDataJson['face_images'] ?? [],
                    'result_images' => $item->result_images ?? [],
                    'pages' => [],
                    'processing_info' => [
                        'status' => $preview->status,
                        'progress' => $item->processing_progress ?? 0,
                        'face_swap_batch' => $preview->face_swap_batch,
                    ]
                ];

                // 获取页面数据
                if ($preview->pages && is_array($preview->pages)) {
                    foreach ($preview->pages as $page) {
                        $itemData['pages'][] = [
                            'page_number' => $page['page_number'] ?? 0,
                            'image_url' => $page['image_url'] ?? '',
                            'text' => $page['text'] ?? '',
                            'character_positions' => $page['character_positions'] ?? [],
                        ];
                    }
                }

                $previewData['items'][] = $itemData;
            }

            return $this->success($previewData, '获取订单预览成功');

        } catch (\Exception $e) {
            Log::error('获取订单预览失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error('获取订单预览失败', null, 500);
        }
    }

    /**
     * 生成订单PDF文件
     */
    public function generateOrderPdf($id)
    {
        try {
            $order = Order::with([
                'items.preview.picbook',
                'items.preview.pages',
                'user'
            ])->findOrFail($id);

            // 检查订单状态
            if ($order->status !== Order::STATUS_CONFIRMED) {
                return $this->error('订单状态不允许生成PDF，需要确认状态', null, 400);
            }

            // 检查是否有有效的预览数据
            $hasValidPreview = false;
            foreach ($order->items as $item) {
                if ($item->preview && !empty($item->result_images)) {
                    $hasValidPreview = true;
                    break;
                }
            }

            if (!$hasValidPreview) {
                return $this->error('订单没有有效的预览数据，无法生成PDF', null, 400);
            }

            // 使用BookService生成PDF
            $bookService = app(\App\Services\BookService::class);
            $pdfResult = $bookService->generateOrderPdf($order);

            if (!$pdfResult['success']) {
                return $this->error('PDF生成失败: ' . $pdfResult['error'], null, 500);
            }

            // 更新订单状态为已打印
            $order->status = Order::STATUS_PRINTED;
            $order->printed_at = now();
            $order->save();

            // 记录PDF生成日志
            Log::info('订单PDF生成成功', [
                'order_id' => $order->id,
                'pdf_path' => $pdfResult['pdf_path'],
                'file_size' => $pdfResult['file_size'] ?? 0,
                'admin_user' => auth()->user()->name ?? 'System'
            ]);

            return $this->success([
                'pdf_url' => $pdfResult['pdf_url'],
                'pdf_path' => $pdfResult['pdf_path'],
                'file_size' => $pdfResult['file_size'] ?? 0,
                'order_status' => $order->status,
                'printed_at' => $order->printed_at
            ], 'PDF生成成功');

        } catch (\Exception $e) {
            Log::error('生成订单PDF失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error('生成PDF失败', null, 500);
        }
    }

    /**
     * 批量更新订单状态
     */
    public function batchUpdateStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_ids' => 'required|array',
            'order_ids.*' => 'integer|exists:orders,id',
            'status' => 'required|in:pending,confirmed,processing,shipped,delivered,cancelled,refunded',
            'note' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            DB::beginTransaction();

            $successCount = 0;
            $failCount = 0;
            $results = [];

            foreach ($request->order_ids as $orderId) {
                try {
                    $order = Order::findOrFail($orderId);
                    $oldStatus = $order->status;
                    $newStatus = $request->status;

                    if ($this->isValidStatusTransition($oldStatus, $newStatus)) {
                        $order->status = $newStatus;
                        $order->save();

                        $this->logStatusChange($order, $oldStatus, $newStatus, $request->note);
                        $this->handleStatusChange($order, $oldStatus, $newStatus);

                        $successCount++;
                        $results[] = [
                            'order_id' => $orderId,
                            'success' => true,
                            'message' => '更新成功'
                        ];
                    } else {
                        $failCount++;
                        $results[] = [
                            'order_id' => $orderId,
                            'success' => false,
                            'message' => '无效的状态变更'
                        ];
                    }
                } catch (\Exception $e) {
                    $failCount++;
                    $results[] = [
                        'order_id' => $orderId,
                        'success' => false,
                        'message' => $e->getMessage()
                    ];
                }
            }

            DB::commit();

            return $this->success([
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results
            ], "批量更新完成，成功: {$successCount}，失败: {$failCount}");

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批量更新订单状态失败', [
                'error' => $e->getMessage()
            ]);

            return $this->error('批量更新失败', null, 500);
        }
    }

    /**
     * 获取订单统计数据
     */
    public function getStatistics(Request $request)
    {
        try {
            $dateFrom = $request->input('date_from', Carbon::now()->subDays(30));
            $dateTo = $request->input('date_to', Carbon::now());

            $statistics = [
                // 订单状态统计
                'status_stats' => Order::selectRaw('status, COUNT(*) as count')
                    ->whereBetween('created_at', [$dateFrom, $dateTo])
                    ->groupBy('status')
                    ->pluck('count', 'status'),

                // 支付状态统计
                'payment_stats' => Order::selectRaw('payment_status, COUNT(*) as count')
                    ->whereBetween('created_at', [$dateFrom, $dateTo])
                    ->groupBy('payment_status')
                    ->pluck('count', 'payment_status'),

                // 处理状态统计
                'processing_stats' => Order::selectRaw('processing_status, COUNT(*) as count')
                    ->whereBetween('created_at', [$dateFrom, $dateTo])
                    ->groupBy('processing_status')
                    ->pluck('count', 'processing_status'),

                // 收入统计
                'revenue_stats' => [
                    'total_revenue' => Order::where('payment_status', 'paid')
                        ->whereBetween('created_at', [$dateFrom, $dateTo])
                        ->sum('total_amount'),
                    'pending_revenue' => Order::where('payment_status', 'pending')
                        ->whereBetween('created_at', [$dateFrom, $dateTo])
                        ->sum('total_amount'),
                ],

                // 每日订单统计
                'daily_stats' => Order::selectRaw('DATE(created_at) as date, COUNT(*) as orders, SUM(total_amount) as revenue')
                    ->whereBetween('created_at', [$dateFrom, $dateTo])
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get(),

                // 热门商品统计
                'popular_items' => OrderItem::with('picbook')
                    ->selectRaw('picbook_id, COUNT(*) as order_count, SUM(quantity) as total_quantity')
                    ->whereHas('order', function ($q) use ($dateFrom, $dateTo) {
                        $q->whereBetween('created_at', [$dateFrom, $dateTo]);
                    })
                    ->groupBy('picbook_id')
                    ->orderBy('order_count', 'desc')
                    ->limit(10)
                    ->get()
            ];

            return $this->success($statistics, '统计数据获取成功');

        } catch (\Exception $e) {
            Log::error('获取订单统计失败', [
                'error' => $e->getMessage()
            ]);

            return $this->error('获取统计数据失败', null, 500);
        }
    }

    /**
     * 导出订单数据
     */
    public function export(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'format' => 'required|in:csv,excel',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date',
            'status' => 'nullable|string',
            'payment_status' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', $validator->errors(), 422);
        }

        try {
            $query = Order::with(['user', 'items.picbook']);

            // 应用筛选条件
            if ($request->filled('date_from')) {
                $query->where('created_at', '>=', Carbon::parse($request->date_from)->startOfDay());
            }
            if ($request->filled('date_to')) {
                $query->where('created_at', '<=', Carbon::parse($request->date_to)->endOfDay());
            }
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }
            if ($request->filled('payment_status')) {
                $query->where('payment_status', $request->payment_status);
            }

            $orders = $query->orderBy('created_at', 'desc')->get();

            // 生成导出文件
            $filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.' . $request->format;
            $filePath = storage_path('app/exports/' . $filename);

            if (!is_dir(dirname($filePath))) {
                mkdir(dirname($filePath), 0755, true);
            }

            if ($request->format === 'csv') {
                $this->exportToCsv($orders, $filePath);
            } else {
                $this->exportToExcel($orders, $filePath);
            }

            return response()->download($filePath)->deleteFileAfterSend();

        } catch (\Exception $e) {
            Log::error('订单导出失败', [
                'error' => $e->getMessage()
            ]);

            return $this->error('导出失败', null, 500);
        }
    }

    /**
     * 验证状态转换是否有效
     */
    private function isValidStatusTransition($oldStatus, $newStatus)
    {
        $validTransitions = [
            'pending' => ['confirmed', 'cancelled'],
            'confirmed' => ['processing', 'cancelled'],
            'processing' => ['shipped', 'cancelled'],
            'shipped' => ['delivered'],
            'delivered' => ['refunded'],
            'cancelled' => [],
            'refunded' => []
        ];

        return in_array($newStatus, $validTransitions[$oldStatus] ?? []);
    }

    /**
     * 记录状态变更日志
     */
    private function logStatusChange($order, $oldStatus, $newStatus, $note = null)
    {
        DB::table('order_logs')->insert([
            'order_id' => $order->id,
            'type' => 'status_change',
            'old_value' => $oldStatus,
            'new_value' => $newStatus,
            'note' => $note,
            'admin_id' => auth()->user()->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * 记录支付状态变更日志
     */
    private function logPaymentStatusChange($order, $oldStatus, $newStatus, $note = null)
    {
        DB::table('order_logs')->insert([
            'order_id' => $order->id,
            'type' => 'payment_status_change',
            'old_value' => $oldStatus,
            'new_value' => $newStatus,
            'note' => $note,
            'admin_id' => auth()->user()->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * 记录处理状态变更日志
     */
    private function logProcessingStatusChange($order, $oldStatus, $newStatus, $note = null)
    {
        DB::table('order_logs')->insert([
            'order_id' => $order->id,
            'type' => 'processing_status_change',
            'old_value' => $oldStatus,
            'new_value' => $newStatus,
            'note' => $note,
            'admin_id' => auth()->user()->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * 处理状态变更后的业务逻辑
     */
    private function handleStatusChange($order, $oldStatus, $newStatus)
    {
        // 根据状态变更执行相应的业务逻辑
        switch ($newStatus) {
            case 'ai_processing':
                // AI处理开始
                break;
            case 'ai_completed':
                // AI处理完成，准备创建物流订单
                break;
            case 'printing':
                // 打印开始
                break;
            case 'shipped':
                // 订单发货后的逻辑
                break;
            case 'delivered':
                // 订单送达后的逻辑
                break;
            case 'cancelled':
                // 订单取消后的逻辑
                break;
        }
    }

    /**
     * 导出到CSV
     */
    private function exportToCsv($orders, $filePath)
    {
        $file = fopen($filePath, 'w');
        
        // 写入BOM以支持中文
        fwrite($file, "\xEF\xBB\xBF");
        
        // 写入表头
        fputcsv($file, [
            '订单号', '用户邮箱', '用户姓名', '订单状态', '支付状态', 
            '物流状态', '订单金额', '创建时间', '支付时间'
        ]);

        // 写入数据
        foreach ($orders as $order) {
            fputcsv($file, [
                $order->order_number,
                $order->user->email ?? '',
                $order->user->name ?? '',
                $order->status,
                $order->payment_status,
                $order->logistics_status ?? '',
                $order->total_amount,
                $order->created_at->format('Y-m-d H:i:s'),
                $order->paid_at ? $order->paid_at->format('Y-m-d H:i:s') : ''
            ]);
        }

        fclose($file);
    }

    /**
     * 导出到Excel (简化版，实际项目中可使用PhpSpreadsheet)
     */
    private function exportToExcel($orders, $filePath)
    {
        // 这里简化为CSV格式，实际项目中应使用PhpSpreadsheet
        $this->exportToCsv($orders, $filePath);
    }

    // ==================== 物流管理功能 ====================

    /**
     * 获取可创建物流订单的订单列表
     */
    public function getEligibleOrders(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            // 查询AI处理完成且未创建物流订单的订单
            $query = Order::where('status', Order::STATUS_AI_COMPLETED)
                ->where('payment_status', Order::PAYMENT_PAID)
                ->whereNull('logistics_request_no')
                ->with(['items.preview', 'user'])
                ->orderBy('confirmed_at', 'asc');

            $orders = $query->paginate($request->per_page ?? 20);

            // 为每个订单添加详细的检查信息
            $formattedOrders = $orders->getCollection()->map(function ($order) {
                $canCreateResult = $this->orderService->canCreateLogisticsOrder($order);
                
                return [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'user_name' => $order->user->name ?? 'Unknown',
                    'total_amount' => $order->total_amount,
                    'confirmed_at' => $order->confirmed_at,
                    'shipping_address' => $order->shipping_address,
                    'items_count' => $order->items->count(),
                    'can_create_logistics' => $canCreateResult['can_create'],
                    'logistics_checks' => $canCreateResult['checks'],
                    'missing_requirements' => $canCreateResult['missing_requirements']
                ];
            });

            return response()->json([
                'success' => true,
                'code' => 200,
                'message' => '获取可创建物流订单列表成功',
                'data' => $formattedOrders,
                'meta' => [
                    'total' => $orders->total(),
                    'per_page' => $orders->perPage(),
                    'current_page' => $orders->currentPage(),
                    'last_page' => $orders->lastPage(),
                    'from' => $orders->firstItem(),
                    'to' => $orders->lastItem()
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 检查订单是否可以创建物流订单
     */
    public function checkOrderEligibility(Request $request, $orderId)
    {
        try {
            $order = Order::with(['items.preview'])->findOrFail($orderId);
            $canCreateResult = $this->orderService->canCreateLogisticsOrder($order);

            return $this->success([
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'can_create_logistics' => $canCreateResult['can_create'],
                'checks' => $canCreateResult['checks'],
                'missing_requirements' => $canCreateResult['missing_requirements'],
                'order_status' => $order->status,
                'payment_status' => $order->payment_status,
                'has_shipping_address' => !$order->needsShippingAddress(),
                'has_logistics_order' => $order->hasLogisticsOrder()
            ], '订单物流创建资格检查完成');

        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 创建物流订单
     */
    public function createLogisticsOrder(Request $request, $orderId)
    {
        try {
            $order = Order::with(['items.preview'])->findOrFail($orderId);

            // 创建物流订单
            $result = $this->orderService->createLogisticsOrder($order);

            // 更新订单状态为已打印（准备发货）
            $order->status = Order::STATUS_PRINTED;
            $order->logistics_status = 'created';
            $order->save();

            Log::info('后台创建物流订单成功', [
                'order_id' => $order->id,
                'logistics_request_no' => $order->logistics_request_no,
                'admin_user' => auth()->user()?->name ?? 'Unknown'
            ]);

            return $this->success([
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'logistics_request_no' => $order->logistics_request_no,
                'logistics_status' => $order->logistics_status,
                'logistics_data' => $result,
                'new_order_status' => $order->status
            ], '物流订单创建成功');

        } catch (\Exception $e) {
            Log::error('后台创建物流订单失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'admin_user' => auth()->user()?->name ?? 'Unknown'
            ]);

            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 批量创建物流订单
     */
    public function batchCreateLogisticsOrders(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_ids' => 'required|array',
                'order_ids.*' => 'required|integer|exists:orders,id'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            $orderIds = $request->order_ids;
            $results = [
                'success' => [],
                'failed' => []
            ];

            foreach ($orderIds as $orderId) {
                try {
                    $order = Order::with(['items.preview'])->findOrFail($orderId);
                    $result = $this->orderService->createLogisticsOrder($order);

                    // 更新订单状态
                    $order->status = Order::STATUS_PRINTED;
                    $order->logistics_status = 'created';
                    $order->save();

                    $results['success'][] = [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'logistics_request_no' => $order->logistics_request_no
                    ];

                } catch (\Exception $e) {
                    $results['failed'][] = [
                        'order_id' => $orderId,
                        'error' => $e->getMessage()
                    ];
                }
            }

            Log::info('批量创建物流订单完成', [
                'total_orders' => count($orderIds),
                'success_count' => count($results['success']),
                'failed_count' => count($results['failed']),
                'admin_user' => auth()->user()?->name ?? 'Unknown'
            ]);

            return $this->success($results, sprintf(
                '批量创建完成，成功: %d，失败: %d',
                count($results['success']),
                count($results['failed'])
            ));

        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取物流订单列表
     */
    public function getLogisticsOrders(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'nullable|string',
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            $query = Order::whereNotNull('logistics_request_no')
                ->with(['user'])
                ->orderBy('created_at', 'desc');

            if ($request->status) {
                $query->where('logistics_status', $request->status);
            }

            $orders = $query->paginate($request->per_page ?? 20);

            $formattedOrders = $orders->getCollection()->map(function ($order) {
                return [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'user_name' => $order->user->name ?? 'Unknown',
                    'total_amount' => $order->total_amount,
                    'status' => $order->status,
                    'logistics_request_no' => $order->logistics_request_no,
                    'logistics_status' => $order->logistics_status,
                    'tracking_number' => $order->tracking_number,
                    'created_at' => $order->created_at,
                    'confirmed_at' => $order->confirmed_at
                ];
            });

            return response()->json([
                'success' => true,
                'code' => 200,
                'message' => '获取物流订单列表成功',
                'data' => $formattedOrders,
                'meta' => [
                    'total' => $orders->total(),
                    'per_page' => $orders->perPage(),
                    'current_page' => $orders->currentPage(),
                    'last_page' => $orders->lastPage(),
                    'from' => $orders->firstItem(),
                    'to' => $orders->lastItem()
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 更新物流订单状态
     */
    public function updateLogisticsStatus(Request $request, $orderId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'logistics_status' => 'required|string',
                'tracking_number' => 'nullable|string',
                'notes' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            $order = Order::findOrFail($orderId);

            if (empty($order->logistics_request_no)) {
                return $this->error('订单尚未创建物流订单', null, 400);
            }

            // 更新物流状态
            $order->logistics_status = $request->logistics_status;
            
            if ($request->filled('tracking_number')) {
                $order->tracking_number = $request->tracking_number;
            }

            // 根据物流状态更新订单状态
            if ($request->logistics_status === 'shipped') {
                $order->status = Order::STATUS_SHIPPED;
            } elseif ($request->logistics_status === 'delivered') {
                $order->status = Order::STATUS_DELIVERED;
            }

            $order->save();

            Log::info('物流订单状态已更新', [
                'order_id' => $order->id,
                'logistics_status' => $order->logistics_status,
                'tracking_number' => $order->tracking_number,
                'order_status' => $order->status,
                'admin_user' => auth()->user()?->name ?? 'Unknown'
            ]);

            return $this->success([
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'logistics_status' => $order->logistics_status,
                'tracking_number' => $order->tracking_number,
                'order_status' => $order->status
            ], '物流订单状态更新成功');

        } catch (\Exception $e) {
            Log::error('更新物流订单状态失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return $this->error($e->getMessage(), null, 500);
        }
    }

    /**
     * 获取物流订单详情
     */
    public function getLogisticsOrderDetail(Request $request, $orderId)
    {
        try {
            $order = Order::with(['user', 'items.picbook'])
                ->findOrFail($orderId);

            if (empty($order->logistics_request_no)) {
                return $this->error('订单尚未创建物流订单', null, 400);
            }

            $detail = [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'user_name' => $order->user->name ?? 'Unknown',
                'user_email' => $order->user->email ?? 'Unknown',
                'total_amount' => $order->total_amount,
                'currency_code' => $order->currency_code,
                'status' => $order->status,
                'payment_status' => $order->payment_status,
                'logistics_request_no' => $order->logistics_request_no,
                'logistics_status' => $order->logistics_status,
                'tracking_number' => $order->tracking_number,
                'shipping_address' => $order->shipping_address,
                'logistics_data' => $order->logistics_data,
                'items' => $order->items->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'picbook_name' => $item->picbook->default_name ?? 'Unknown',
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'total_price' => $item->total_price
                    ];
                }),
                'created_at' => $order->created_at,
                'confirmed_at' => $order->confirmed_at,
                'processed_at' => $order->processed_at
            ];

            return $this->success($detail, '获取物流订单详情成功');

        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
}
