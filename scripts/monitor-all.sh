#!/bin/bash

# 生产环境完整监控脚本
set -e

echo "🔍 开始完整系统监控..."
echo "=================================="

# 1. 基础服务监控
echo "1️⃣ 基础服务监控："
./scripts/monitor-services.sh

echo ""
echo "=================================="

# 2. Horizon 队列监控
echo "2️⃣ Horizon 队列监控："
./scripts/monitor-horizon.sh

echo ""
echo "=================================="

# 3. Reverb WebSocket 监控
echo "3️⃣ Reverb WebSocket 监控："
./scripts/monitor-reverb.sh

echo ""
echo "=================================="

# 4. 换脸服务专项监控
echo "4️⃣ 换脸服务监控："
php artisan faceswap:queue-stats

echo ""
echo "🎉 完整系统监控完成！"
echo "=================================="