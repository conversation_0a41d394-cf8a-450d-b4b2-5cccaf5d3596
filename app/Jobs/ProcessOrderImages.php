<?php

namespace App\Jobs;

use App\Services\EnhancedPicbookProcessor;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * 处理订单图片的队列任务
 */
class ProcessOrderImages implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $orderId;
    protected $dedicationText;
    protected $userOptions;

    /**
     * 任务最大尝试次数
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct($orderId, $dedicationText = null, $userOptions = [])
    {
        $this->orderId = $orderId;
        $this->dedicationText = $dedicationText;
        $this->userOptions = $userOptions;
    }

    /**
     * Execute the job.
     */
    public function handle(EnhancedPicbookProcessor $processor)
    {
        try {
            Log::info('开始处理订单图片', [
                'order_id' => $this->orderId,
                'dedication_text' => $this->dedicationText
            ]);

            $result = $processor->processOrderImages($this->orderId, $this->dedicationText);

            if ($result['success']) {
                Log::info('订单图片处理成功', [
                    'order_id' => $this->orderId,
                    'processed_pages' => $result['success_count'] ?? 0
                ]);
            } else {
                Log::error('订单图片处理失败', [
                    'order_id' => $this->orderId,
                    'message' => $result['message']
                ]);
                
                // 标记任务失败
                $this->fail(new \Exception($result['message']));
            }

        } catch (\Exception $e) {
            Log::error('处理订单图片任务异常', [
                'order_id' => $this->orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 重新抛出异常，让队列系统处理重试
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception)
    {
        Log::error('订单图片处理任务最终失败', [
            'order_id' => $this->orderId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // 这里可以发送失败通知给用户或管理员
        // 或者更新订单状态为处理失败
    }
}