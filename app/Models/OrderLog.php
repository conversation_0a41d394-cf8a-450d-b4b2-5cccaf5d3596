<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'type',
        'old_value',
        'new_value',
        'note',
        'admin_id',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array'
    ];

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 关联管理员
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * 日志类型常量
     */
    const TYPE_STATUS_CHANGE = 'status_change';
    const TYPE_PAYMENT_STATUS_CHANGE = 'payment_status_change';
    const TYPE_PROCESSING_STATUS_CHANGE = 'processing_status_change';
    const TYPE_SHIPPING_STATUS_CHANGE = 'shipping_status_change';
    const TYPE_NOTE = 'note';
    const TYPE_SYSTEM = 'system';

    /**
     * 获取日志类型的中文名称
     */
    public function getTypeNameAttribute()
    {
        $typeNames = [
            self::TYPE_STATUS_CHANGE => '订单状态变更',
            self::TYPE_PAYMENT_STATUS_CHANGE => '支付状态变更',
            self::TYPE_PROCESSING_STATUS_CHANGE => '处理状态变更',
            self::TYPE_SHIPPING_STATUS_CHANGE => '物流状态变更',
            self::TYPE_NOTE => '备注',
            self::TYPE_SYSTEM => '系统操作'
        ];

        return $typeNames[$this->type] ?? $this->type;
    }
}