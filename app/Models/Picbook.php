<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Picbook extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'default_name',
        'default_cover',
        'pricesymbol',
        'price',
        'currencycode',
        'total_pages',
        'preview_pages_count',
        'rating',
        'has_choices',
        'has_question',
        'supported_languages',
        'supported_genders',
        'supported_skincolors',
        'character_count',
        'tags',
        'status',
        'is_saleable',
        'choices_type'
    ];

    protected $casts = [
        'supported_languages' => 'array',
        'supported_genders' => 'array',
        'supported_skincolors' => 'array',
        'tags' => 'array',
        'price' => 'decimal:2',
        'rating' => 'decimal:2',
        'has_choices' => 'boolean',
        'has_question' => 'boolean',
        'choices_type' => 'integer',
        'preview_pages_count' => 'integer',
        'is_saleable' => 'boolean',
        'character_count' => 'integer',
        'batch_processing_status' => 'integer'
    ];

    // 关联绘本变体
    public function variants()
    {
        return $this->hasMany(PicbookVariant::class);
    }

    // 关联绘本页面
    public function pages()
    {
        return $this->hasMany(PicbookPage::class);
    }


    // 关联封面变体
    public function coverVariants()
    {
        return $this->hasMany(PicbookCoverVariant::class);
    }

    // 关联用户问答
    public function userAnswers()
    {
        return $this->hasMany(PicbookUserAnswer::class);
    }

    // 获取已发布的封面变体
    public function publishedCoverVariants()
    {
        return $this->coverVariants()->where('is_published', true)->orderBy('sort_order');
    }

    /**
     * 获取选择页面数量
     */
    public function getChoicePagesCountAttribute()
    {
        switch ($this->choices_type) {
            case 1:
                return ['total' => 8, 'select' => 4];
            case 2:
                return ['total' => 16, 'select' => 8];
            default:
                return ['total' => 0, 'select' => 0];
        }
    }

    /**
     * 获取处理日志
     */
    public function processingLogs()
    {
        return $this->hasMany(PicbookProcessingLog::class);
    }
} 