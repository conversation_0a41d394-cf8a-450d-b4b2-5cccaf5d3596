# 物流管理指南

## 概述

订单确认后不再自动创建物流订单，改为由后台物流管理人员手动操作。这样可以确保在所有换脸任务完成后再创建物流订单，提高发货准确性。

## 业务流程变更

### 原流程
```
订单确认 → 自动创建物流订单 → 发货
```

### 新流程
```
订单确认 → 等待换脸完成 → 后台手动创建物流订单 → 发货
```

## 创建物流订单的条件

系统会检查以下条件，只有全部满足才能创建物流订单：

1. **订单已确认** (`order_confirmed`): 订单状态为 `confirmed`
2. **支付已完成** (`payment_completed`): 支付状态为 `paid`
3. **有收货地址** (`has_shipping_address`): 订单包含完整的收货地址
4. **换脸已完成** (`face_swap_completed`): 所有订单项的换脸任务都已完成
5. **未创建物流订单** (`logistics_not_created`): 还没有创建过物流订单

## 后台管理 API

### 1. 获取可创建物流订单的订单列表

**端点**: `GET /api/admin/logistics/eligible-orders`

**参数**:
- `page`: 页码（可选）
- `per_page`: 每页数量（可选，最大50）

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取可创建物流订单列表成功",
  "data": [
    {
      "id": 10,
      "order_number": "PB20250729752294",
      "user_name": "John Doe",
      "total_amount": 29.99,
      "confirmed_at": "2025-01-29T10:30:00Z",
      "shipping_address": {
        "first_name": "John Doe",
        "country": "US",
        "city": "New York",
        "street": "123 Main St"
      },
      "items_count": 2,
      "can_create_logistics": true,
      "logistics_checks": {
        "order_confirmed": true,
        "payment_completed": true,
        "has_shipping_address": true,
        "face_swap_completed": true,
        "logistics_not_created": true
      },
      "missing_requirements": []
    }
  ],
  "meta": {
    "total": 5,
    "per_page": 20,
    "current_page": 1,
    "last_page": 1
  }
}
```

### 2. 检查订单是否可以创建物流订单

**端点**: `GET /api/admin/logistics/orders/{orderId}/check`

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "订单物流创建资格检查完成",
  "data": {
    "order_id": 10,
    "order_number": "PB20250729752294",
    "can_create_logistics": false,
    "checks": {
      "order_confirmed": true,
      "payment_completed": true,
      "has_shipping_address": true,
      "face_swap_completed": false,
      "logistics_not_created": true
    },
    "missing_requirements": ["face_swap_completed"],
    "order_status": "confirmed",
    "payment_status": "paid",
    "has_shipping_address": true,
    "has_logistics_order": false
  }
}
```

### 3. 创建物流订单

**端点**: `POST /api/admin/logistics/orders/{orderId}/create`

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "物流订单创建成功",
  "data": {
    "order_id": 10,
    "order_number": "PB20250729752294",
    "logistics_request_no": "PB20250729752294_1706518200",
    "logistics_status": "created",
    "logistics_data": {
      "request_no": "PB20250729752294_1706518200",
      "logistics_order_id": "LOG123456789"
    },
    "new_order_status": "printed"
  }
}
```

### 4. 批量创建物流订单

**端点**: `POST /api/admin/logistics/orders/batch-create`

**请求参数**:
```json
{
  "order_ids": [10, 11, 12]
}
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "批量创建完成，成功: 2，失败: 1",
  "data": {
    "success": [
      {
        "order_id": 10,
        "order_number": "PB20250729752294",
        "logistics_request_no": "PB20250729752294_1706518200"
      },
      {
        "order_id": 11,
        "order_number": "PB20250729752295",
        "logistics_request_no": "PB20250729752295_1706518201"
      }
    ],
    "failed": [
      {
        "order_id": 12,
        "error": "订单不满足创建物流订单的条件: face_swap_completed"
      }
    ]
  }
}
```

### 5. 获取物流订单列表

**端点**: `GET /api/admin/logistics/orders`

**参数**:
- `status`: 物流状态过滤（可选）
- `page`: 页码（可选）
- `per_page`: 每页数量（可选）

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取物流订单列表成功",
  "data": [
    {
      "id": 10,
      "order_number": "PB20250729752294",
      "user_name": "John Doe",
      "total_amount": 29.99,
      "status": "printed",
      "logistics_request_no": "PB20250729752294_1706518200",
      "logistics_status": "created",
      "tracking_number": null,
      "created_at": "2025-01-29T09:00:00Z",
      "confirmed_at": "2025-01-29T10:30:00Z"
    }
  ]
}
```

## 订单状态变化

创建物流订单后，订单状态会自动更新：

- `confirmed` → `printed` (已打印，准备发货)

## 权限控制

所有物流管理 API 都需要管理员权限：
- 需要通过 `auth:sanctum` 认证
- 需要通过 `check.user.type:admin` 中间件验证

## 日志记录

系统会记录以下操作日志：

1. **创建物流订单成功**:
```
后台创建物流订单成功: order_id=10, logistics_request_no=PB20250729752294_1706518200, admin_user=Admin Name
```

2. **创建物流订单失败**:
```
后台创建物流订单失败: order_id=10, error=订单不满足创建物流订单的条件, admin_user=Admin Name
```

3. **批量创建完成**:
```
批量创建物流订单完成: total_orders=3, success_count=2, failed_count=1, admin_user=Admin Name
```

## 前端集成建议

### 1. 订单列表页面
- 显示订单的物流创建资格状态
- 提供批量选择和创建功能
- 显示创建失败的原因

### 2. 订单详情页面
- 显示详细的资格检查结果
- 提供单个订单的物流创建按钮
- 显示换脸任务的完成状态

### 3. 物流管理页面
- 显示所有已创建的物流订单
- 提供状态筛选功能
- 支持跟踪号更新

## 错误处理

### 常见错误及解决方案

1. **订单不满足创建条件**:
   - 检查换脸任务是否完成
   - 确认订单状态和支付状态
   - 验证收货地址是否完整

2. **物流服务调用失败**:
   - 检查物流服务配置
   - 验证网络连接
   - 查看详细错误日志

3. **权限不足**:
   - 确认用户具有管理员权限
   - 检查认证 token 是否有效

## 监控建议

建议监控以下指标：

1. **待创建物流订单数量**: 确认后但未创建物流订单的数量
2. **换脸任务完成率**: 影响物流订单创建的关键指标
3. **物流订单创建成功率**: 物流服务的可用性指标
4. **订单处理时长**: 从确认到发货的时间