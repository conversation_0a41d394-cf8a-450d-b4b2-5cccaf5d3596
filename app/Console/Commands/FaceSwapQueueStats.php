<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AiFaceTask;
use App\Models\PicbookPreview;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class FaceSwapQueueStats extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'faceswap:queue-stats {--save : 保存统计数据到缓存}';

    /**
     * The console command description.
     */
    protected $description = '生成换脸队列统计报告';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('生成换脸队列统计报告...');
        
        $stats = $this->generateStats();
        
        // 显示统计信息
        $this->displayStats($stats);
        
        // 保存到缓存（可选）
        if ($this->option('save')) {
            Cache::put('faceswap_queue_stats', $stats, now()->addHours(1));
            $this->info('统计数据已保存到缓存');
        }
        
        // 记录到日志
        Log::info('换脸队列统计', $stats);
        
        // 检查是否需要告警
        $this->checkAlerts($stats);
    }
    
    private function generateStats()
    {
        $now = Carbon::now();
        $oneHourAgo = $now->copy()->subHour();
        $oneDayAgo = $now->copy()->subDay();
        
        return [
            'timestamp' => $now->toISOString(),
            'queue_status' => [
                'pending_batches' => AiFaceTask::where('type', 'batch')->where('status', 'pending')->count(),
                'processing_batches' => AiFaceTask::where('type', 'batch')->where('status', 'processing')->count(),
                'completed_batches_today' => AiFaceTask::where('type', 'batch')
                    ->where('status', 'completed')
                    ->where('completed_at', '>=', $oneDayAgo)
                    ->count(),
                'failed_batches_today' => AiFaceTask::where('type', 'batch')
                    ->where('status', 'failed')
                    ->where('updated_at', '>=', $oneDayAgo)
                    ->count(),
            ],
            'task_status' => [
                'pending_tasks' => AiFaceTask::where('type', 'task')->where('status', 'pending')->count(),
                'processing_tasks' => AiFaceTask::where('type', 'task')->where('status', 'processing')->count(),
                'completed_tasks_hour' => AiFaceTask::where('type', 'task')
                    ->where('status', 'completed')
                    ->where('completed_at', '>=', $oneHourAgo)
                    ->count(),
                'failed_tasks_hour' => AiFaceTask::where('type', 'task')
                    ->where('status', 'failed')
                    ->where('updated_at', '>=', $oneHourAgo)
                    ->count(),
            ],
            'preview_status' => [
                'pending_previews' => PicbookPreview::where('status', PicbookPreview::STATUS_PENDING)->count(),
                'completed_previews_today' => PicbookPreview::where('status', PicbookPreview::STATUS_COMPLETED)
                    ->where('updated_at', '>=', $oneDayAgo)
                    ->count(),
                'failed_previews_today' => PicbookPreview::where('status', PicbookPreview::STATUS_FAILED)
                    ->where('updated_at', '>=', $oneDayAgo)
                    ->count(),
            ],
            'performance' => [
                'avg_task_duration_minutes' => $this->getAverageTaskDuration(),
                'success_rate_today' => $this->getSuccessRate($oneDayAgo),
            ]
        ];
    }
    
    private function displayStats($stats)
    {
        $this->info('=== 换脸队列统计报告 ===');
        $this->info('生成时间: ' . $stats['timestamp']);
        
        $this->info("\n📊 队列状态:");
        $this->table(
            ['类型', '待处理', '处理中', '今日完成', '今日失败'],
            [
                [
                    '批次',
                    $stats['queue_status']['pending_batches'],
                    $stats['queue_status']['processing_batches'],
                    $stats['queue_status']['completed_batches_today'],
                    $stats['queue_status']['failed_batches_today']
                ],
                [
                    '任务',
                    $stats['task_status']['pending_tasks'],
                    $stats['task_status']['processing_tasks'],
                    $stats['task_status']['completed_tasks_hour'] . ' (1小时)',
                    $stats['task_status']['failed_tasks_hour'] . ' (1小时)'
                ]
            ]
        );
        
        $this->info("\n📈 性能指标:");
        $this->line("平均任务处理时间: {$stats['performance']['avg_task_duration_minutes']} 分钟");
        $this->line("今日成功率: {$stats['performance']['success_rate_today']}%");
        
        $this->info("\n🎯 预览状态:");
        $this->line("待处理预览: {$stats['preview_status']['pending_previews']}");
        $this->line("今日完成预览: {$stats['preview_status']['completed_previews_today']}");
        $this->line("今日失败预览: {$stats['preview_status']['failed_previews_today']}");
    }
    
    private function getAverageTaskDuration()
    {
        $completedTasks = AiFaceTask::where('type', 'task')
            ->where('status', 'completed')
            ->whereNotNull('completed_at')
            ->where('completed_at', '>=', Carbon::now()->subDay())
            ->get();
            
        if ($completedTasks->isEmpty()) {
            return 0;
        }
        
        $totalDuration = 0;
        foreach ($completedTasks as $task) {
            $duration = $task->created_at->diffInMinutes($task->completed_at);
            $totalDuration += $duration;
        }
        
        return round($totalDuration / $completedTasks->count(), 2);
    }
    
    private function getSuccessRate($since)
    {
        $total = AiFaceTask::where('type', 'task')
            ->whereIn('status', ['completed', 'failed'])
            ->where('updated_at', '>=', $since)
            ->count();
            
        if ($total === 0) {
            return 100;
        }
        
        $successful = AiFaceTask::where('type', 'task')
            ->where('status', 'completed')
            ->where('completed_at', '>=', $since)
            ->count();
            
        return round(($successful / $total) * 100, 2);
    }
    
    private function checkAlerts($stats)
    {
        $alerts = [];
        
        // 检查队列积压
        if ($stats['queue_status']['pending_batches'] > 10) {
            $alerts[] = "队列积压告警: 待处理批次数量过多 ({$stats['queue_status']['pending_batches']})";
        }
        
        // 检查失败率
        if ($stats['performance']['success_rate_today'] < 90) {
            $alerts[] = "成功率告警: 今日成功率过低 ({$stats['performance']['success_rate_today']}%)";
        }
        
        // 检查处理时间
        if ($stats['performance']['avg_task_duration_minutes'] > 10) {
            $alerts[] = "性能告警: 平均处理时间过长 ({$stats['performance']['avg_task_duration_minutes']} 分钟)";
        }
        
        if (!empty($alerts)) {
            $this->error("\n⚠️  告警信息:");
            foreach ($alerts as $alert) {
                $this->error("- {$alert}");
                Log::warning('换脸队列告警', ['alert' => $alert]);
            }
        } else {
            $this->info("\n✅ 系统运行正常，无告警");
        }
    }
}