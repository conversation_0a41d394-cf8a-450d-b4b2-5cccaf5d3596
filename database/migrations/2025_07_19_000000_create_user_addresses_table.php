<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('first_name', 32)->comment('名/姓名');
            $table->string('last_name', 32)->nullable()->comment('姓');
            $table->string('company', 64)->nullable()->comment('公司名');
            $table->string('phone', 32)->comment('电话（必填）');
            $table->string('phone2', 32)->nullable()->comment('电话2');
            $table->string('email', 32)->nullable()->comment('邮箱');
            $table->string('post_code', 32)->nullable()->comment('邮编');
            $table->string('country', 10)->comment('国家（国际二字码 标准ISO 3166-2）');
            $table->string('state', 64)->nullable()->comment('州/省');
            $table->string('city', 64)->comment('城市');
            $table->string('district', 64)->nullable()->comment('区、县（可对应为address 2）');
            $table->string('street', 64)->comment('街道/详细地址（可对应为address 1）');
            $table->string('house_number', 32)->nullable()->comment('门牌号');
            $table->string('second_name', 32)->nullable()->comment('备用名字，一般用于有两个名字的国家');
            $table->boolean('is_default')->default(false)->comment('是否为默认地址');
            $table->timestamps();
            
            // 索引
            $table->index('user_id');
            $table->index(['user_id', 'is_default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_addresses');
    }
};