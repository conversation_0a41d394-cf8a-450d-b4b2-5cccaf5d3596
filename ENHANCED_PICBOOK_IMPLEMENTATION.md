# 增强版绘本处理功能实现文档

## 概述

本文档描述了新实现的增强版绘本处理功能，包括多图合并、文字处理、AI换脸等功能。这些功能专门为满足新的绘本业务需求而设计，不会影响现有的绘本处理逻辑。

## 主要功能

### 1. 多图合并处理
- 支持底图、肤色层、发型层等多层图片合并
- 支持动态滤镜效果（亮度、饱和度、色调、对比度）
- 基于用户选择的肤色、发型、发色进行个性化合成

### 2. 文字处理
- 支持静态文字和动态文字元素
- 支持多种字体、字号、颜色配置
- 支持寄语文字添加（付款后4小时内可修改）

### 3. 订单后处理
- 订单付款后自动触发图片合并和文字处理
- 支持寄语文字的后期修改
- 队列化处理，避免阻塞用户操作

## 技术架构

### 核心服务类

#### EnhancedPicbookProcessor
位置: `app/Services/EnhancedPicbookProcessor.php`

主要方法:
- `processPage()` - 处理单个绘本页面
- `batchProcessPages()` - 批量处理绘本页面
- `processOrderImages()` - 处理订单付款后的图片
- `updateOrderDedication()` - 更新订单寄语文字

#### 控制器
位置: `app/Http/Controllers/Api/EnhancedPicbookController.php`

提供的API端点:
- `GET /api/enhanced-picbook/{picbook_id}/config` - 获取绘本配置
- `POST /api/enhanced-picbook/process-page` - 处理单个页面
- `POST /api/enhanced-picbook/batch-process` - 批量处理页面
- `POST /api/enhanced-picbook/process-order` - 处理订单图片
- `POST /api/enhanced-picbook/update-dedication` - 更新寄语文字

#### 队列任务
位置: `app/Jobs/ProcessOrderImages.php`

用于异步处理订单的图片合并和文字处理任务。

## 数据结构

### 页面配置文件格式
位置: `public/picbooks/{picbook_id}/page_properties.json`

```json
{
  "skinToneFilter": {
    "brown": {
      "saturate": "+2",
      "hue": "-4",
      "brightness": "-31"
    },
    "dark": {
      "brightness": "-115",
      "contrast": "-48"
    }
  },
  "hairColorFilter": {
    "brown": {
      "saturate": "+2",
      "hue": "-4",
      "brightness": "-31"
    },
    "dark": {
      "brightness": "-115",
      "contrast": "-48"
    }
  },
  "text": [
    {
      "type": "dynamic",
      "font": "Arial",
      "fontWeight": "regular",
      "fontSize": 36,
      "color": "#3487b9",
      "position": {"x": 846, "y": 488},
      "alignment": "left"
    },
    {
      "type": "static",
      "text": "Test1",
      "font": "Arial",
      "fontWeight": "regular",
      "fontSize": 36,
      "color": "#3487b9",
      "position": {"x": 1095, "y": 2369},
      "alignment": "left"
    }
  ]
}
```

### 图片文件结构
```
public/picbooks/{picbook_id}/
├── layer_bg.jpg          # 底图
├── layer_skin.png        # 肤色层
├── layer_hair_1.png      # 发型1
├── layer_hair_2.png      # 发型2
├── layer_hair_3.png      # 发型3
├── layer_hair_4.png      # 发型4
└── page_properties.json  # 页面配置
```

## API 使用示例

### 1. 获取绘本配置
```bash
curl -X GET "http://localhost/api/enhanced-picbook/test/config"
```

### 2. 处理单个页面
```bash
curl -X POST "http://localhost/api/enhanced-picbook/process-page" \
  -H "Content-Type: application/json" \
  -d '{
    "picbook_id": "test",
    "user_options": {
      "skin_tone": "brown",
      "hair_style": "1",
      "hair_color": "brown"
    },
    "dedication_text": "给我最爱的宝贝"
  }'
```

### 3. 批量处理页面
```bash
curl -X POST "http://localhost/api/enhanced-picbook/batch-process" \
  -H "Content-Type: application/json" \
  -d '{
    "picbook_id": "test",
    "pages": [
      {"id": 1, "page_number": 1},
      {"id": 2, "page_number": 2}
    ],
    "user_options": {
      "skin_tone": "brown",
      "hair_style": "1",
      "hair_color": "brown"
    },
    "dedication_text": "给我最爱的宝贝"
  }'
```

### 4. 处理订单图片（需要认证）
```bash
curl -X POST "http://localhost/api/enhanced-picbook/process-order" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "order_id": 123,
    "dedication_text": "给我最爱的宝贝"
  }'
```

## 部署要求

### 系统依赖
1. **PHP 8.2+** 
2. **GD 或 Imagick 扩展** - 用于图片处理
3. **Laravel 11.31** 
4. **Intervention Image** - 图片处理库

### 目录权限
```bash
# 确保存储目录可写
chmod -R 755 storage/app/public/processed
chmod -R 755 public/picbooks
chmod -R 755 public/fonts
```

### 字体文件
将字体文件放置在 `public/fonts/` 目录:
- `arial.ttf` - Arial 常规字体
- `arial-bold.ttf` - Arial 粗体
- `simhei.ttf` - 中文字体

### 队列配置
在 `.env` 文件中配置队列:
```env
QUEUE_CONNECTION=redis
# 或使用数据库队列
# QUEUE_CONNECTION=database
```

启动队列工作进程:
```bash
php artisan queue:work --queue=default
```

## 测试

### 运行测试脚本
```bash
php test_enhanced_picbook.php
```

### 前端演示
访问演示页面: `resources/views/enhanced-picbook-demo.html`

## 与现有系统的集成

### 1. 前端处理流程
- 前端负责图片预览和用户选项收集
- 后端负责最终的图片合并和文字处理
- AI换脸功能保持独立，可与新功能组合使用

### 2. 订单流程集成
- 订单付款后自动触发 `ProcessOrderImages` 队列任务
- 4小时内允许修改寄语文字，重新处理图片
- 处理完成后更新订单状态

### 3. 缓存策略
- 处理结果可缓存，避免重复计算
- 用户选项变更时自动清除相关缓存

## 注意事项

1. **性能考虑**: 图片处理是CPU密集型操作，建议使用队列异步处理
2. **存储空间**: 处理后的图片会占用存储空间，需要定期清理
3. **错误处理**: 图片文件缺失或格式错误时会返回详细错误信息
4. **字体支持**: 确保字体文件存在，否则会使用系统默认字体
5. **兼容性**: 新功能不会影响现有的绘本处理逻辑

## 后续扩展

1. **更多滤镜效果**: 可以添加更多图片滤镜和特效
2. **模板系统**: 支持更灵活的页面模板配置
3. **批量优化**: 优化大批量处理的性能
4. **监控和统计**: 添加处理任务的监控和统计功能