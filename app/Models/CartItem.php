<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CartItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'preview_id',
        'quantity',
        'price',
        'cover_price',
        'binding_price',
        'gift_box_price',
        'total_price',
        'currency_code',
        'notes',
        'origin_site'
    ];

    protected $casts = [
        'price' => 'float',
        'cover_price' => 'float',
        'binding_price' => 'float',
        'gift_box_price' => 'float',
        'total_price' => 'float',
        'quantity' => 'integer'
    ];

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联预览模型
     */
    public function preview()
    {
        return $this->belongsTo(PicbookPreview::class, 'preview_id');
    }

    /**
     * 计算总价
     */
    public function calculateTotal()
    {
        $this->total_price = bcadd(bcadd(bcadd($this->price, $this->cover_price, 2), $this->binding_price, 2), $this->gift_box_price, 2) * $this->quantity;
        return $this->total_price;
    }

    /**
     * 更新购物车项价格
     *
     * @param float $price 基本价格
     * @param float $coverPrice 封面价格
     * @param float $bindingPrice 装帧价格
     * @param float $giftBoxPrice 礼盒价格
     * @param string $currencyCode 货币代码
     * @return void
     */
    public function updatePrices($price, $coverPrice, $bindingPrice, $giftBoxPrice, $currencyCode)
    {
        $this->price = $price;
        $this->cover_price = $coverPrice;
        $this->binding_price = $bindingPrice;
        $this->gift_box_price = $giftBoxPrice;
        $this->currency_code = $currencyCode;
        $this->calculateTotal();
        $this->save();
    }
} 