<?php

/**
 * 全书预览优化功能演示
 * 
 * 这个示例展示了优化前后的对比效果
 */

// 模拟场景：20页绘本，用户已经预览了前5页
$totalPages = 20;
$previewedPages = 5;
$remainingPages = $totalPages - $previewedPages;

echo "=== 全书预览优化演示 ===\n\n";

echo "场景设置：\n";
echo "- 绘本总页数：{$totalPages}页\n";
echo "- 已预览页数：{$previewedPages}页\n";
echo "- 剩余页数：{$remainingPages}页\n\n";

// 传统方式
echo "传统方式（无优化）：\n";
echo "- 需要处理：{$totalPages}页\n";
echo "- 处理时间：" . ($totalPages * 5) . "秒\n";
echo "- API调用：{$totalPages}次\n";
echo "- 成本：100%\n\n";

// 优化方式
echo "优化方式（智能复用）：\n";
echo "- 复用结果：{$previewedPages}页\n";
echo "- 需要处理：{$remainingPages}页\n";
echo "- 处理时间：" . ($remainingPages * 5) . "秒\n";
echo "- API调用：{$remainingPages}次\n";
echo "- 成本：" . round(($remainingPages / $totalPages) * 100) . "%\n\n";

// 优化效果
$timeSaved = ($previewedPages * 5);
$costSaved = round(($previewedPages / $totalPages) * 100);

echo "优化效果：\n";
echo "- 节省时间：{$timeSaved}秒\n";
echo "- 节省成本：{$costSaved}%\n";
echo "- 优化率：{$costSaved}%\n\n";

// 不同场景的优化效果
echo "=== 不同场景的优化效果 ===\n\n";

$scenarios = [
    ['total' => 20, 'previewed' => 0, 'name' => '全新绘本'],
    ['total' => 20, 'previewed' => 3, 'name' => '少量预览'],
    ['total' => 20, 'previewed' => 10, 'name' => '一半预览'],
    ['total' => 20, 'previewed' => 15, 'name' => '大部分预览'],
    ['total' => 20, 'previewed' => 20, 'name' => '完全预览'],
];

foreach ($scenarios as $scenario) {
    $total = $scenario['total'];
    $previewed = $scenario['previewed'];
    $remaining = $total - $previewed;
    $optimizationRate = $previewed > 0 ? round(($previewed / $total) * 100) : 0;
    
    echo sprintf(
        "%-12s | 总页数: %2d | 已预览: %2d | 需处理: %2d | 优化率: %3d%%\n",
        $scenario['name'],
        $total,
        $previewed,
        $remaining,
        $optimizationRate
    );
}

echo "\n=== 实际代码示例 ===\n\n";

echo "检测已有结果：\n";
echo '```php
$existingPreview = PicbookPreview::where("user_id", $userId)
    ->where("picbook_id", $picbookId)
    ->where("status", "completed")
    ->whereNotNull("result_images")
    ->orderBy("created_at", "desc")
    ->first();

if ($existingPreview) {
    foreach ($existingPreview->result_images as $result) {
        $processedPageIds[] = $result["page_id"];
    }
}
```' . "\n\n";

echo "智能过滤页面：\n";
echo '```php
foreach ($allPages as $page) {
    if (in_array($page->id, $processedPageIds)) {
        $skippedTasks++;
        continue; // 跳过已处理的页面
    }
    // 添加到处理队列
    $images[] = $pageData;
    $totalTasks++;
}
```' . "\n\n";

echo "合并结果：\n";
echo '```php
// 先添加已存在的结果
foreach ($existingResults as $result) {
    $resultImages[] = [
        "page_id" => $result["page_id"],
        "result_image_url" => $result["result_image_url"],
        "reused" => true
    ];
}

// 再添加新处理的结果
foreach ($completedTasks as $task) {
    $resultImages[] = [
        "page_id" => $task->page_id,
        "result_image_url" => $task->result_image_url,
        "reused" => false
    ];
}

// 按页面号排序
usort($resultImages, function($a, $b) {
    return $a["page_number"] <=> $b["page_number"];
});
```' . "\n\n";

echo "=== 总结 ===\n\n";
echo "这个优化功能实现了：\n";
echo "✅ 智能检测已有预览结果\n";
echo "✅ 自动跳过已处理页面\n";
echo "✅ 无缝合并新旧结果\n";
echo "✅ 大幅提升处理效率\n";
echo "✅ 显著降低处理成本\n";
echo "✅ 改善用户体验\n\n";

echo "特别适用于：\n";
echo "- 用户多次预览同一绘本\n";
echo "- 从普通预览升级到全书预览\n";
echo "- 大型绘本的分批处理\n";
echo "- 成本敏感的应用场景\n";
