<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Events\Registered;
use App\Listeners\SendEmailVerificationNotification;
use App\Events\AiFaceTaskProgress;
use App\Listeners\AiFaceTaskProgressListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        
        // AI换脸进度事件
        \App\Events\AiFaceTaskProgress::class => [
            \App\Listeners\AiFaceTaskProgressListener::class,
        ],
        
        // AI换脸任务完成事件（针对单个任务的完整处理）
        \App\Events\AiFaceTaskCompleted::class => [
            \App\Listeners\AiFaceTaskCompletedListener::class,
            \App\Listeners\ProcessNextInBatchListener::class,
        ],
        
        // AI换脸任务失败事件（针对单个任务的完整处理）
        \App\Events\AiFaceTaskFailed::class => [
            // 如果需要监听器，可以在此添加
        ],
        
        // AI换脸队列状态事件
        \App\Events\AiFaceQueueStatus::class => [
            // 如果需要监听器，可以在此添加
        ],
        
        // 订单支付完成事件
        \App\Events\OrderPaid::class => [
            \App\Listeners\OrderPaidListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
} 