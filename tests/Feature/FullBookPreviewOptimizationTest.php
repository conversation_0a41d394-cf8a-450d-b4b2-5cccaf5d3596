<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\PicbookPreview;
use App\Models\Picbook;
use App\Models\PicbookPage;
use App\Models\PicbookPageVariant;
use App\Models\AiFaceTask;
use App\Services\SimpleFaceSwapService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;

class FullBookPreviewOptimizationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $picbook;
    protected $order;
    protected $orderItem;
    protected $preview;
    protected $faceSwapService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->picbook = Picbook::factory()->create([
            'default_name' => 'Test Book',
            'price' => 29.99
        ]);

        // 创建绘本页面
        for ($i = 1; $i <= 5; $i++) {
            $page = PicbookPage::create([
                'picbook_id' => $this->picbook->id,
                'page_number' => $i,
                'image_url' => "page_{$i}.jpg",
                'character_sequence' => [1]
            ]);

            // 创建页面变体
            PicbookPageVariant::create([
                'page_id' => $page->id,
                'language' => 'en',
                'gender' => 1,
                'character_skincolors' => [1],
                'image_url' => "variant_page_{$i}.jpg",
                'face_config' => ['mask_url' => "mask_{$i}.jpg"],
                'has_face' => true
            ]);
        }

        $this->order = Order::factory()->create(['user_id' => $this->user->id]);
        $this->preview = PicbookPreview::factory()->create([
            'user_id' => $this->user->id,
            'picbook_id' => $this->picbook->id,
            'status' => 'completed'
        ]);
        
        $this->orderItem = OrderItem::factory()->create([
            'order_id' => $this->order->id,
            'preview_id' => $this->preview->id,
            'picbook_id' => $this->picbook->id
        ]);

        $this->faceSwapService = app(SimpleFaceSwapService::class);
    }

    /**
     * 测试没有已有预览结果时的全书换脸
     */
    public function test_full_book_batch_without_existing_results()
    {
        $result = $this->faceSwapService->createFullBookBatch(
            $this->picbook->id,
            ['face1.jpg'],
            'Test Child',
            'en',
            1,
            1,
            $this->order->id,
            $this->user->id
        );

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('batch_id', $result);
        
        // 验证创建了5个任务（所有页面都需要处理）
        $tasks = AiFaceTask::where('batch_id', $result['batch_id'])
            ->where('type', 'task')
            ->count();
        $this->assertEquals(5, $tasks);
    }

    /**
     * 测试有部分已有预览结果时的全书换脸优化
     */
    public function test_full_book_batch_with_partial_existing_results()
    {
        // 模拟已有的预览结果（前3页已处理）
        $existingResults = [
            [
                'page_id' => 1,
                'variant_id' => 1,
                'result_image_url' => 'existing_result_1.jpg',
                'result' => ['standard_url' => 'existing_result_1.jpg']
            ],
            [
                'page_id' => 2,
                'variant_id' => 2,
                'result_image_url' => 'existing_result_2.jpg',
                'result' => ['standard_url' => 'existing_result_2.jpg']
            ],
            [
                'page_id' => 3,
                'variant_id' => 3,
                'result_image_url' => 'existing_result_3.jpg',
                'result' => ['standard_url' => 'existing_result_3.jpg']
            ]
        ];

        // 更新预览记录，添加已有结果
        $this->preview->update([
            'result_images' => $existingResults,
            'status' => PicbookPreview::STATUS_COMPLETED
        ]);

        $result = $this->faceSwapService->createFullBookBatch(
            $this->picbook->id,
            ['face1.jpg'],
            'Test Child',
            'en',
            1,
            1,
            $this->order->id,
            $this->user->id
        );

        $this->assertTrue($result['success']);
        
        // 验证只创建了2个新任务（第4、5页）
        $tasks = AiFaceTask::where('batch_id', $result['batch_id'])
            ->where('type', 'task')
            ->count();
        $this->assertEquals(2, $tasks);

        // 验证批次配置中包含已有结果
        $batchRecord = AiFaceTask::where('batch_id', $result['batch_id'])
            ->where('type', 'batch')
            ->first();
        
        $this->assertArrayHasKey('existing_results', $batchRecord->config);
        $this->assertEquals(3, $batchRecord->config['reused_pages_count']);
    }

    /**
     * 测试所有页面都已处理的情况
     */
    public function test_full_book_batch_with_all_pages_already_processed()
    {
        // 模拟所有页面都已处理
        $existingResults = [];
        for ($i = 1; $i <= 5; $i++) {
            $existingResults[] = [
                'page_id' => $i,
                'variant_id' => $i,
                'result_image_url' => "existing_result_{$i}.jpg",
                'result' => ['standard_url' => "existing_result_{$i}.jpg"]
            ];
        }

        $this->preview->update([
            'result_images' => $existingResults,
            'status' => PicbookPreview::STATUS_COMPLETED
        ]);

        $result = $this->faceSwapService->createFullBookBatch(
            $this->picbook->id,
            ['face1.jpg'],
            'Test Child',
            'en',
            1,
            1,
            $this->order->id,
            $this->user->id
        );

        $this->assertTrue($result['success']);
        $this->assertStringContains('reuse_', $result['batch_id']);
        $this->assertEquals(5, $result['reused_pages_count']);
        
        // 验证没有创建新的处理任务
        $tasks = AiFaceTask::where('batch_id', $result['batch_id'])
            ->where('type', 'task')
            ->count();
        $this->assertEquals(0, $tasks);

        // 验证订单项已更新
        $this->orderItem->refresh();
        $this->assertEquals($result['batch_id'], $this->orderItem->face_swap_batch_id);
        $this->assertCount(5, $this->orderItem->result_images);
        $this->assertEquals('completed', $this->orderItem->status);
    }

    /**
     * 测试优化率计算
     */
    public function test_optimization_rate_calculation()
    {
        // 模拟3页已处理，2页需要新处理
        $existingResults = [];
        for ($i = 1; $i <= 3; $i++) {
            $existingResults[] = [
                'page_id' => $i,
                'variant_id' => $i,
                'result_image_url' => "existing_result_{$i}.jpg",
                'result' => ['standard_url' => "existing_result_{$i}.jpg"]
            ];
        }

        $this->preview->update([
            'result_images' => $existingResults,
            'status' => PicbookPreview::STATUS_COMPLETED
        ]);

        // 捕获日志以验证优化率
        Log::shouldReceive('info')
            ->with('全书换脸任务分析', \Mockery::on(function ($data) {
                return $data['need_processing'] == 2 && 
                       $data['can_reuse'] == 3 && 
                       $data['optimization_rate'] == '60%';
            }))
            ->once();

        $this->faceSwapService->createFullBookBatch(
            $this->picbook->id,
            ['face1.jpg'],
            'Test Child',
            'en',
            1,
            1,
            $this->order->id,
            $this->user->id
        );
    }

    /**
     * 测试结果合并功能
     */
    public function test_result_merging_functionality()
    {
        // 创建部分已有结果
        $existingResults = [
            [
                'page_id' => 1,
                'variant_id' => 1,
                'result_image_url' => 'existing_result_1.jpg',
                'result' => ['standard_url' => 'existing_result_1.jpg']
            ],
            [
                'page_id' => 3,
                'variant_id' => 3,
                'result_image_url' => 'existing_result_3.jpg',
                'result' => ['standard_url' => 'existing_result_3.jpg']
            ]
        ];

        $this->preview->update([
            'result_images' => $existingResults,
            'status' => PicbookPreview::STATUS_COMPLETED
        ]);

        $result = $this->faceSwapService->createFullBookBatch(
            $this->picbook->id,
            ['face1.jpg'],
            'Test Child',
            'en',
            1,
            1,
            $this->order->id,
            $this->user->id
        );

        $this->assertTrue($result['success']);

        // 模拟新任务完成
        $newTasks = AiFaceTask::where('batch_id', $result['batch_id'])
            ->where('type', 'task')
            ->get();

        foreach ($newTasks as $index => $task) {
            $task->update([
                'status' => 'completed',
                'result_image_url' => "new_result_{$task->page_id}.jpg",
                'result' => ['standard_url' => "new_result_{$task->page_id}.jpg"],
                'completed_at' => now()
            ]);
        }

        // 触发结果更新
        $this->faceSwapService->updateBatchProgress($result['batch_id']);

        // 验证结果合并
        $this->orderItem->refresh();
        $resultImages = $this->orderItem->result_images;
        
        // 应该有5个结果（2个复用 + 3个新处理）
        $this->assertCount(5, $resultImages);
        
        // 验证复用的结果标记
        $reusedResults = collect($resultImages)->where('reused', true);
        $newResults = collect($resultImages)->where('reused', false);
        
        $this->assertCount(2, $reusedResults);
        $this->assertCount(3, $newResults);
    }
}
