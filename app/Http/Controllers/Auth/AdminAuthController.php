<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\Admin;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AdminAuthController extends Controller
{
    use ApiResponse;

    public function login(LoginRequest $request): JsonResponse
    {
        $admin = Admin::where('email', $request->email)->first();

        if (!$admin || !Hash::check($request->password, $admin->password)) {
            return $this->error(
                __('auth.failed'),
                ['email' => [__('auth.failed')]],
                401
            );
        }

        $token = $admin->createToken('admin_token')->plainTextToken;

        return $this->success([
            'admin' => $admin,
            'token' => $token
        ], __('auth.login_success'));
    }

    public function logout(): JsonResponse
    {
        $user = auth()->user();
        
        if ($user && $user->currentAccessToken()) {
            $user->currentAccessToken()->delete();
        }

        return $this->success(null, __('auth.logout_success'));
    }

    /**
     * 获取当前管理员信息
     */
    public function me(): JsonResponse
    {
        $admin = auth()->user();
        
        if (!$admin) {
            return $this->error(__('auth.unauthenticated'), null, 401);
        }
        
        return $this->success($admin, __('auth.admin_info_success'));
    }
}
