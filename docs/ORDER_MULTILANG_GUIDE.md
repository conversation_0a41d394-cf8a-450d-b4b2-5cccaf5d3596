# 订单多语言支持指南 / Order Multi-Language Support Guide

## 概述 / Overview

本系统已实现订单相关API的完整多语言支持，支持中文和英文两种语言。系统会根据客户端发送的语言偏好自动返回对应语言的响应信息。

This system implements complete multi-language support for order-related APIs, supporting both Chinese and English. The system automatically returns responses in the appropriate language based on client language preferences.

## 语言设置 / Language Configuration

### 支持的语言 / Supported Languages
- `en` - English (英文)
- `zh` - Chinese (中文)

### 语言检测 / Language Detection

系统通过以下方式检测客户端语言偏好：
The system detects client language preferences through:

1. **HTTP Header**: `Accept-Language: zh` 或 `Accept-Language: en`
2. **Query Parameter**: `?locale=zh` 或 `?locale=en`
3. **默认语言 / Default**: `en` (English)

### 中间件 / Middleware

系统使用 `SetLocale` 中间件自动处理语言设置：
The system uses `SetLocale` middleware to automatically handle language settings:

```php
// app/Http/Middleware/SetLocale.php
$locale = $request->header('Accept-Language') ?? $request->input('locale') ?? config('app.locale');
App::setLocale($locale);
```

## API响应格式 / API Response Format

### 标准响应结构 / Standard Response Structure

```json
{
    "success": true,
    "code": 200,
    "message": "订单创建成功", // 根据语言自动翻译
    "data": {
        "id": 12345,
        "status": "processing",
        "status_text": "书籍准备中", // 新增：本地化状态文本
        "payment_status": "paid",
        "payment_status_text": "已付款", // 新增：本地化支付状态文本
        "permissions": { // 新增：操作权限信息
            "can_cancel": true,
            "can_update_message": true,
            "can_update_address": true,
            "can_confirm": false,
            "should_auto_confirm": false
        }
    }
}
```

### 增强功能 / Enhanced Features

1. **状态文本本地化 / Localized Status Text**
   - `status_text`: 订单状态的本地化文本
   - `payment_status_text`: 支付状态的本地化文本

2. **权限信息 / Permission Information**
   - `can_cancel`: 是否可以取消订单
   - `can_update_message`: 是否可以更新寄语
   - `can_update_address`: 是否可以更新地址
   - `can_confirm`: 是否可以确认订单
   - `should_auto_confirm`: 是否将自动确认

## 订单状态翻译 / Order Status Translations

### 订单状态 / Order Status

| 状态码 / Status Code | 英文 / English | 中文 / Chinese |
|---------------------|----------------|----------------|
| `pending` | Pending Payment | 待付款 |
| `processing` | Book Preparation | 书籍准备中 |
| `preparing` | Book Production | 书籍制作中 |
| `confirmed` | Confirmed | 已确认 |
| `printed` | Printed | 已印刷 |
| `packed` | Packed | 已打包 |
| `shipped` | Shipped | 已发货 |
| `delivered` | Delivered | 已送达 |
| `completed` | Completed | 已完成 |
| `cancelled` | Cancelled | 已取消 |
| `refunded` | Refunded | 已退款 |

### 支付状态 / Payment Status

| 状态码 / Status Code | 英文 / English | 中文 / Chinese |
|---------------------|----------------|----------------|
| `pending` | Pending Payment | 待付款 |
| `paid` | Paid | 已付款 |
| `failed` | Payment Failed | 支付失败 |
| `refunded` | Refunded | 已退款 |
| `partially_refunded` | Partially Refunded | 部分退款 |

## API端点 / API Endpoints

### 1. 创建订单 / Create Order
```
POST /api/orders
```

**响应示例 / Response Example:**
```json
{
    "success": true,
    "message": "订单创建成功",
    "data": {
        "order": { /* 格式化的订单数据 */ },
        "payment_data": { /* 支付数据 */ }
    }
}
```

### 2. 获取订单列表 / Get Order List
```
GET /api/orders
```

**响应示例 / Response Example:**
```json
{
    "success": true,
    "message": "订单列表获取成功",
    "data": [
        {
            "id": 1,
            "status": "processing",
            "status_text": "书籍准备中",
            "payment_status": "paid",
            "payment_status_text": "已付款"
        }
    ]
}
```

### 3. 获取订单详情 / Get Order Details
```
GET /api/orders/{id}
```

### 4. 取消订单 / Cancel Order
```
POST /api/orders/{id}/cancel
```

### 5. 更新订单地址 / Update Order Address
```
PUT /api/orders/{id}/address
```

### 6. 更新订单寄语 / Update Order Message
```
PUT /api/orders/{id}/message
```

### 7. 确认订单 / Confirm Order
```
POST /api/orders/{id}/confirm
```

### 8. 获取处理进度 / Get Processing Progress
```
GET /api/orders/{id}/progress
```

### 9. 获取物流跟踪 / Get Tracking Info
```
GET /api/orders/{id}/tracking
```

### 10. 获取物流方式 / Get Shipping Methods
```
GET /api/orders/shipping-methods
```

**响应示例 / Response Example:**
```json
{
    "success": true,
    "message": "物流方式获取成功",
    "data": [
        {
            "code": "STANDARD",
            "name": "标准物流",
            "description": "标准国际物流",
            "cost": 15.00,
            "currency": "USD"
        }
    ]
}
```

## 错误处理 / Error Handling

### 常见错误消息 / Common Error Messages

| 错误类型 / Error Type | 英文 / English | 中文 / Chinese |
|----------------------|----------------|----------------|
| 订单不存在 | Order not found | 订单不存在 |
| 权限不足 | You do not have permission to access this order | 您无权访问此订单 |
| 无效状态 | Invalid order status | 无效订单状态 |
| 购物车无效 | Invalid cart items found, please remove them and try again | 购物车中包含无效商品，请移除后再尝试下单 |
| 地址必填 | Shipping address is required | 收货地址为必填项 |
| 寄语更新过期 | Order created more than 4 hours ago, cannot modify message | 订单创建超过4小时，无法修改寄语 |

## 使用示例 / Usage Examples

### 客户端请求示例 / Client Request Examples

#### 1. 使用Header设置语言 / Using Header for Language
```bash
curl -X GET "https://api.example.com/api/orders/123" \
  -H "Accept-Language: zh" \
  -H "Authorization: Bearer your-token"
```

#### 2. 使用Query参数设置语言 / Using Query Parameter for Language
```bash
curl -X GET "https://api.example.com/api/orders/123?locale=en" \
  -H "Authorization: Bearer your-token"
```

### JavaScript客户端示例 / JavaScript Client Example

```javascript
// 设置默认语言
const locale = localStorage.getItem('locale') || 'en';

// API请求
fetch('/api/orders/123', {
    headers: {
        'Accept-Language': locale,
        'Authorization': `Bearer ${token}`
    }
})
.then(response => response.json())
.then(data => {
    console.log(data.message); // 自动显示对应语言的消息
    console.log(data.data.status_text); // 本地化的状态文本
});
```

## 开发指南 / Development Guide

### 添加新的翻译 / Adding New Translations

1. **英文翻译文件 / English Translation File**
   ```php
   // lang/en/order.php
   return [
       'new_message' => 'New message in English',
   ];
   ```

2. **中文翻译文件 / Chinese Translation File**
   ```php
   // lang/zh/order.php
   return [
       'new_message' => '中文新消息',
   ];
   ```

3. **在控制器中使用 / Usage in Controller**
   ```php
   return $this->success($data, __('order.new_message'));
   ```

### 最佳实践 / Best Practices

1. **始终使用翻译键 / Always Use Translation Keys**
   ```php
   // ✅ 正确
   throw new \Exception(__('order.access_denied'));
   
   // ❌ 错误
   throw new \Exception('您无权访问此订单');
   ```

2. **提供完整的状态信息 / Provide Complete Status Information**
   ```php
   $response = [
       'status' => 'processing',
       'status_text' => __('order.status.processing'), // 本地化文本
   ];
   ```

3. **包含操作权限 / Include Operation Permissions**
   ```php
   $response['permissions'] = [
       'can_cancel' => $order->canCancel(),
       'can_update_message' => $order->canUpdateMessage(),
   ];
   ```

## 测试 / Testing

运行多语言测试：
Run multi-language test:

```bash
php test_order_multilang.php
```

这将显示不同语言下的API响应格式示例。
This will show API response format examples in different languages.

## 注意事项 / Notes

1. **语言切换实时生效 / Language Switching Takes Effect Immediately**
   - 无需重启应用，语言设置在每个请求中动态处理
   - No application restart needed, language settings are processed dynamically per request

2. **回退机制 / Fallback Mechanism**
   - 如果请求的语言不支持，系统会回退到默认语言（英文）
   - If requested language is not supported, system falls back to default language (English)

3. **缓存考虑 / Caching Considerations**
   - 翻译文件会被Laravel自动缓存，生产环境下修改翻译需要清除缓存
   - Translation files are automatically cached by Laravel, cache clearing needed after translation changes in production

4. **扩展性 / Extensibility**
   - 系统设计支持轻松添加更多语言
   - System designed to easily support additional languages