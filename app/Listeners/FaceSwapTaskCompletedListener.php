<?php

namespace App\Listeners;

use App\Events\FaceSwapTaskCompleted;
use App\Models\PicbookPreview;
use App\Models\OrderItem;
use App\Models\Order;
use App\Services\PreviewService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\OrderProcessingNotification;
use App\Mail\OrderCompletedNotification;

class FaceSwapTaskCompletedListener implements ShouldQueue
{
    /**
     * 任务应该发送到的队列
     *
     * @var string|null
     */
    public $queue = 'listeners';

    /**
     * 预览服务
     *
     * @var PreviewService
     */
    protected $previewService;

    /**
     * 创建事件监听器
     */
    public function __construct(PreviewService $previewService)
    {
        $this->previewService = $previewService;
    }

    /**
     * 处理事件
     */
    public function handle(FaceSwapTaskCompleted $event): void
    {
        try {
            $task = $event->task;
            
            Log::info('接收到FaceSwap任务完成事件', [
                'task_id' => $task->id,
                'batch_id' => $task->batch_id,
                'user_id' => $task->user_id,
                'status' => $task->status,
                'result_images_count' => count($task->result_images ?? [])
            ]);
            
            // 更新预览记录
            if ($task->batch_id) {
                // 查找对应的预览记录，确保直接从数据库获取最新数据
                $preview = \App\Models\PicbookPreview::where('batch_id', $task->batch_id)->first();
                
                if ($preview) {
                    Log::info('找到对应的预览记录，准备更新', [
                        'preview_id' => $preview->id,
                        'batch_id' => $task->batch_id,
                        'current_status' => $preview->status
                    ]);
                }
                
                $updateResult = $this->previewService->updatePreviewResult(
                    $task->batch_id,
                    $task->result_images ?? [],
                    $task->status
                );
                
                Log::info('FaceSwap任务完成，已更新预览记录', [
                    'batch_id' => $task->batch_id,
                    'status' => $task->status,
                    'update_result' => $updateResult
                ]);
                
                // 再次检查预览记录是否真的更新了
                $updatedPreview = \App\Models\PicbookPreview::where('batch_id', $task->batch_id)->first();
                if ($updatedPreview) {
                    Log::info('预览记录更新后的状态', [
                        'preview_id' => $updatedPreview->id,
                        'batch_id' => $task->batch_id,
                        'status' => $updatedPreview->status,
                        'result_images_count' => count($updatedPreview->result_images ?? [])
                    ]);
                }
                
                // 更新订单项和订单状态
                $this->updateOrderStatus($task->batch_id, $task->status, $task->result_images ?? []);
            }
            
            // 将通知持久化到缓存中，设置有效期为7天
            // 这样用户即使关闭页面，再次回来时仍然可以看到通知
            if ($task->user_id) {
                $notificationKey = "user:{$task->user_id}:face_swap:notifications:batch:{$task->batch_id}";
                
                $notificationData = [
                    'batch_id' => $task->batch_id,
                    'status' => $task->status,
                    'created_at' => now()->timestamp,
                    'updated_at' => now()->timestamp,
                    'result_images' => $task->result_images ?? [],
                    'read' => false, // 默认为未读
                    'message' => $task->status === 'completed' 
                        ? '您的换脸任务已完成' 
                        : '您的换脸任务处理失败'
                ];
                
                // 存储通知到缓存，7天过期
                Cache::put($notificationKey, $notificationData, now()->addDays(7));
                
                Log::info('FaceSwap任务通知已保存', [
                    'batch_id' => $task->batch_id,
                    'user_id' => $task->user_id,
                    'status' => $task->status
                ]);
            }
        } catch (\Exception $e) {
            Log::error('处理FaceSwap任务完成事件失败', [
                'error' => $e->getMessage(),
                'task_id' => $event->task->id ?? null,
                'batch_id' => $event->task->batch_id ?? null,
                'user_id' => $event->task->user_id ?? null
            ]);
        }
    }
    
    /**
     * 更新订单项和订单状态
     */
    private function updateOrderStatus(string $batchId, string $status, array $resultImages): void
    {
        try {
            // 查找对应的订单项
            $orderItem = OrderItem::where('face_swap_batch_id', $batchId)->first();
            
            if (!$orderItem) {
                Log::warning('未找到对应的订单项', ['batch_id' => $batchId]);
                return;
            }
            
            $order = $orderItem->order;
            
            Log::info('开始更新订单状态', [
                'order_id' => $order->id,
                'order_item_id' => $orderItem->id,
                'batch_id' => $batchId,
                'status' => $status
            ]);
            
            // 更新订单项状态
            if ($status === 'completed') {
                $orderItem->result_images = $resultImages;
                $orderItem->status = OrderItem::STATUS_PROCESSING; // 进入下一步处理
                $orderItem->processing_progress = 50; // 换脸完成，50%进度
            } else {
                $orderItem->status = 'failed';
                $orderItem->error_message = 'AI换脸处理失败';
            }
            
            $orderItem->save();
            
            // 检查订单的所有项是否都已完成换脸
            $this->checkAndUpdateOrderStatus($order);
            
            // 发送处理进度邮件
            $this->sendProgressEmail($order, $orderItem);
            
        } catch (\Exception $e) {
            Log::error('更新订单状态失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 检查并更新订单状态
     */
    private function checkAndUpdateOrderStatus(Order $order): void
    {
        try {
            $allItems = $order->items;
            $totalItems = $allItems->count();
            $completedFaceSwap = 0;
            $failedItems = 0;
            
            foreach ($allItems as $item) {
                if ($item->status === OrderItem::STATUS_PROCESSING && !empty($item->result_images)) {
                    $completedFaceSwap++;
                } elseif ($item->status === 'failed') {
                    $failedItems++;
                }
            }
            
            Log::info('检查订单换脸完成情况', [
                'order_id' => $order->id,
                'total_items' => $totalItems,
                'completed_face_swap' => $completedFaceSwap,
                'failed_items' => $failedItems
            ]);
            
            // 如果所有订单项都完成了换脸，更新订单状态
            if ($completedFaceSwap === $totalItems && $failedItems === 0) {
                $order->status = Order::STATUS_AI_COMPLETED;
                $order->save();
                
                Log::info('订单AI处理完成', [
                    'order_id' => $order->id,
                    'status' => $order->status
                ]);
                
                // 发送AI处理完成邮件
                $this->sendAiCompletedEmail($order);
                
            } elseif ($failedItems > 0) {
                // 如果有失败的订单项，标记订单为失败
                $order->status = Order::STATUS_CANCELLED;
                $order->save();
                
                Log::info('订单AI处理失败', [
                    'order_id' => $order->id,
                    'failed_items' => $failedItems
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('检查订单状态失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 发送处理进度邮件
     */
    private function sendProgressEmail(Order $order, OrderItem $orderItem): void
    {
        try {
            $progress = [
                'step' => 1,
                'total' => 3,
                'percentage' => 50,
                'current_task' => 'AI换脸处理完成',
                'estimated_time' => '30分钟',
                'face_swap_completed' => true,
                'face_swap_processing' => false,
                'image_processing_completed' => false,
                'image_processing_processing' => false,
                'quality_check_completed' => false,
                'quality_check_processing' => false
            ];
            
            Mail::to($order->user->email)
                ->send(new OrderProcessingNotification($order, $progress));
            
            Log::info('处理进度邮件已发送', [
                'order_id' => $order->id,
                'user_email' => $order->user->email
            ]);
            
        } catch (\Exception $e) {
            Log::warning('发送处理进度邮件失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 发送AI处理完成邮件
     */
    private function sendAiCompletedEmail(Order $order): void
    {
        try {
            $progress = [
                'step' => 2,
                'total' => 3,
                'percentage' => 75,
                'current_task' => 'AI换脸处理完成，开始图片合成',
                'estimated_time' => '1小时',
                'face_swap_completed' => true,
                'face_swap_processing' => false,
                'image_processing_completed' => false,
                'image_processing_processing' => true,
                'quality_check_completed' => false,
                'quality_check_processing' => false
            ];
            
            Mail::to($order->user->email)
                ->send(new OrderProcessingNotification($order, $progress));
            
            Log::info('AI处理完成邮件已发送', [
                'order_id' => $order->id,
                'user_email' => $order->user->email
            ]);
            
        } catch (\Exception $e) {
            Log::warning('发送AI处理完成邮件失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}