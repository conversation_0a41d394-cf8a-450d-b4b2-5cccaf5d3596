<?php

if (!function_exists('s3_url')) {
    /**
     * 获取S3预签名URL的快捷函数
     */
    function s3_url($path, $disk = 's3_picbook', $expires = 300, $options = []) {
        return \App\Helpers\FileHelper::getPresignedUrl($path, $disk, $expires, $options);
    }
}

if (!function_exists('s3_download')) {
    /**
     * 获取S3下载URL的快捷函数
     */
    function s3_download($path, $disk = 's3_picbook', $expires = 300, $filename = null) {
        return \App\Helpers\FileHelper::getDownloadUrl($path, $disk, $expires, $filename);
    }
}