<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('picbook_page_variants', function (Blueprint $table) {
            $table->tinyInteger('processing_status')->default(0)->comment('处理状态：0=未处理，1=处理中，2=处理完成，3=处理失败');
            $table->string('processed_image_url')->nullable()->comment('处理后的图片URL');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('picbook_page_variants', function (Blueprint $table) {
            $table->dropColumn('processing_status');
            $table->dropColumn('processed_image_url');
        });
    }
};
