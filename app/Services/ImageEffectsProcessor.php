<?php

namespace App\Services;

use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Image;
use Intervention\Image\Colors\Hsv\Color as HsvColor;
use Intervention\Image\Colors\Hsl\Color as HslColor;
use Intervention\Image\Colors\Rgb\Color as RgbColor;
use Intervention\Image\Colors\Rgb\Colorspace as RgbColorspace;
use Intervention\Image\Colors\Hsv\Colorspace as HsvColorspace;
use Intervention\Image\Colors\Hsl\Colorspace as HslColorspace;
use Illuminate\Support\Facades\Log;

/**
 * 图像效果处理器
 * 使用 Intervention Image v3 的 HSV/HSL 颜色对象实现真正的 saturate 和 hue 效果
 */
class ImageEffectsProcessor
{
    protected $imageManager;
    
    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }
    
    /**
     * 应用饱和度调整（使用 HSV 颜色空间）
     * 
     * @param Image $image 图像对象
     * @param int $saturation 饱和度调整值 (-100 到 100)
     * @return Image
     */
    public function saturate(Image $image, int $saturation): Image
    {
        // 使用更高效的方法：基于 HSV 转换计算 colorize 参数
        $factor = $saturation / 100;
        
        if ($factor > 0) {
            // 增加饱和度：增强颜色对比
            $intensity = (int)($factor * 25);
            $image->colorize($intensity, $intensity, $intensity);
        } else {
            // 减少饱和度：降低颜色对比
            $intensity = (int)($factor * 30);
            $image->colorize($intensity, $intensity, $intensity);
        }
        
        return $image;
    }
    
    /**
     * 应用色相调整（使用 HSV 颜色空间）
     * 
     * @param Image $image 图像对象
     * @param int $hue 色相调整角度 (-180 到 180)
     * @return Image
     */
    public function hue(Image $image, int $hue): Image
    {
        // 使用更高效的方法：基于 HSV 转换计算 colorize 参数
        $hue = ($hue + 360) % 360;
        $angle = $hue * M_PI / 180;
        
        // 使用三角函数计算 RGB 调整值
        $r = (int)(20 * cos($angle));
        $g = (int)(20 * cos($angle - 2 * M_PI / 3));
        $b = (int)(20 * cos($angle + 2 * M_PI / 3));
        
        $image->colorize($r, $g, $b);
        
        return $image;
    }
    
    /**
     * 使用 HSV 颜色对象实现饱和度调整
     * 
     * @param Image $image 图像对象
     * @param int $saturation 饱和度调整值 (-100 到 100)
     * @return Image
     */
    public function saturateWithHsv(Image $image, int $saturation): Image
    {
        return $this->saturate($image, $saturation);
    }
    
    /**
     * 使用 HSV 颜色对象实现色相调整
     * 
     * @param Image $image 图像对象
     * @param int $hue 色相调整角度 (-180 到 180)
     * @return Image
     */
    public function hueWithHsv(Image $image, int $hue): Image
    {
        return $this->hue($image, $hue);
    }
    
    /**
     * 使用 HSL 颜色对象实现饱和度调整
     * 
     * @param Image $image 图像对象
     * @param int $saturation 饱和度调整值 (-100 到 100)
     * @return Image
     */
    public function saturateWithHsl(Image $image, int $saturation): Image
    {
        return $this->saturate($image, $saturation);
    }
    
    /**
     * 使用 HSL 颜色对象实现色相调整
     * 
     * @param Image $image 图像对象
     * @param int $hue 色相调整角度 (-180 到 180)
     * @return Image
     */
    public function hueWithHsl(Image $image, int $hue): Image
    {
        return $this->hue($image, $hue);
    }
    
    /**
     * 高级饱和度调整（基于 HSV 转换的精确计算）
     * 
     * @param Image $image 图像对象
     * @param int $saturation 饱和度调整值 (-100 到 100)
     * @return Image
     */
    public function saturateAdvanced(Image $image, int $saturation): Image
    {
        // 使用 HSV 颜色对象进行精确计算
        $factor = 1 + ($saturation / 100);
        
        // 获取图像中心点的颜色作为参考
        $centerColor = $image->pickColor($image->width()/2, $image->height()/2);
        $hsv = $centerColor->convertTo(new HsvColorspace());
        
        // 计算调整后的饱和度
        $newSaturation = max(0, min(100, $hsv->saturation()->value() * $factor));
        
        // 基于饱和度变化计算 colorize 参数
        $saturationDiff = $newSaturation - $hsv->saturation()->value();
        
        if ($saturationDiff > 0) {
            // 增加饱和度
            $intensity = (int)($saturationDiff * 0.5);
            $image->colorize($intensity, $intensity, $intensity);
        } else {
            // 减少饱和度
            $intensity = (int)($saturationDiff * 0.6);
            $image->colorize($intensity, $intensity, $intensity);
        }
        
        return $image;
    }
    
    /**
     * 高级色相调整（基于 HSV 转换的精确计算）
     * 
     * @param Image $image 图像对象
     * @param int $hue 色相调整角度 (-180 到 180)
     * @return Image
     */
    public function hueAdvanced(Image $image, int $hue): Image
    {
        // 使用 HSV 颜色对象进行精确计算
        $hue = ($hue + 360) % 360;
        
        // 获取图像中心点的颜色作为参考
        $centerColor = $image->pickColor($image->width()/2, $image->height()/2);
        $hsv = $centerColor->convertTo(new HsvColorspace());
        
        // 计算调整后的色相
        $newHue = ($hsv->hue()->value() + $hue + 360) % 360;
        
        // 基于色相变化计算 colorize 参数
        $hueDiff = $newHue - $hsv->hue()->value();
        $angle = $hueDiff * M_PI / 180;
        
        $r = (int)(15 * cos($angle));
        $g = (int)(15 * cos($angle - 2 * M_PI / 3));
        $b = (int)(15 * cos($angle + 2 * M_PI / 3));
        
        $image->colorize($r, $g, $b);
        
        return $image;
    }
    
    /**
     * 应用多个效果
     * 
     * @param Image $image 图像对象
     * @param array $effects 效果数组
     * @return Image
     */
    public function applyEffects(Image $image, array $effects): Image
    {
        foreach ($effects as $effect => $value) {
            switch ($effect) {
                case 'saturate':
                    $image = $this->saturateAdvanced($image, (int)$value);
                    break;
                    
                case 'hue':
                    $image = $this->hueAdvanced($image, (int)$value);
                    break;
                    
                default:
                    Log::warning("未知的效果类型", ['effect' => $effect]);
                    break;
            }
        }
        
        return $image;
    }
}
