<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单支付成功</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #ffffff;
            padding: 30px;
            border: 1px solid #e9ecef;
            border-radius: 0 0 8px 8px;
        }
        .order-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .order-item {
            border-bottom: 1px solid #e9ecef;
            padding: 15px 0;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .btn {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>订单支付成功！</h1>
        <p>感谢您的购买，我们已收到您的订单</p>
    </div>
    
    <div class="content">
        <p>亲爱的 {{ $user->name }}，</p>
        
        <p>您的订单已成功支付，我们已经开始为您处理。以下是您的订单信息：</p>
        
        <div class="order-info">
            <h3>订单信息</h3>
            <p><strong>订单号：</strong>{{ $order->order_number }}</p>
            <p><strong>支付时间：</strong>{{ $order->paid_at->format('Y-m-d H:i:s') }}</p>
            <p><strong>订单金额：</strong>¥{{ number_format($order->total_amount, 2) }}</p>
            <p><strong>支付方式：</strong>{{ $order->payment_method }}</p>
        </div>
        
        <h3>订单详情</h3>
        @foreach($orderItems as $item)
            <div class="order-item">
                <p><strong>{{ $item->picbook->default_name }}</strong></p>
                <p>数量：{{ $item->quantity }}</p>
                <p>单价：¥{{ number_format($item->price, 2) }}</p>
                @if($item->message)
                    <p><strong>寄语：</strong>{{ $item->message }}</p>
                @endif
            </div>
        @endforeach
        
        <h3>处理流程</h3>
        <ol>
            <li>AI换脸处理（预计1-2小时）</li>
            <li>图片合成和文字处理（预计30分钟）</li>
            <li>质量检查和打包</li>
            <li>发货（1-3个工作日）</li>
        </ol>
        
        <p>在订单处理过程中，您可以随时在订单页面查看处理进度。</p>
        
        <p>如有任何问题，请联系我们的客服团队。</p>
        
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
            <p>&copy; 2024 Dreamazebook. All rights reserved.</p>
        </div>
    </div>
</body>
</html>