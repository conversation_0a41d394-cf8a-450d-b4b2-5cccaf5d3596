<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_options', function (Blueprint $table) {
            $table->id();
            $table->string('option_type')->comment('选项类型: cover, binding, gift_box');
            $table->string('option_key')->comment('选项唯一标识');
            $table->string('name')->comment('选项名称');
            $table->text('description')->nullable()->comment('选项描述');
            $table->decimal('price', 10, 2)->default(0)->comment('选项额外价格');
            $table->string('currency_code', 3)->default('USD')->comment('货币代码');
            $table->string('image_url')->nullable()->comment('选项图片URL');
            $table->boolean('is_default')->default(false)->comment('是否为默认选项');
            $table->tinyInteger('status')->default(1)->comment('状态: 1=启用, 0=禁用');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加唯一索引，确保每种类型的选项key唯一
            $table->unique(['option_type', 'option_key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_options');
    }
}; 