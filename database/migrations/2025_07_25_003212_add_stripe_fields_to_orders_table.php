<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Stripe相关字段
            $table->string('stripe_session_id')->nullable()->comment('Stripe Checkout Session ID');
            $table->string('stripe_payment_intent_id')->nullable()->comment('Stripe PaymentIntent ID');
            $table->string('stripe_customer_id')->nullable()->comment('Stripe Customer ID');
            $table->json('stripe_metadata')->nullable()->comment('Stripe元数据');
            $table->timestamp('stripe_webhook_received_at')->nullable()->comment('Stripe Webhook接收时间');
            
            // 添加索引
            $table->index('stripe_session_id');
            $table->index('stripe_payment_intent_id');
            $table->index('stripe_customer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['stripe_session_id']);
            $table->dropIndex(['stripe_payment_intent_id']);
            $table->dropIndex(['stripe_customer_id']);
            
            $table->dropColumn([
                'stripe_session_id',
                'stripe_payment_intent_id',
                'stripe_customer_id',
                'stripe_metadata',
                'stripe_webhook_received_at'
            ]);
        });
    }
};
