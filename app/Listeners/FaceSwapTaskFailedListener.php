<?php

namespace App\Listeners;

use App\Events\FaceSwapTaskFailed;
use App\Models\OrderItem;
use App\Models\Order;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class FaceSwapTaskFailedListener implements ShouldQueue
{
    /**
     * 任务应该发送到的队列
     *
     * @var string|null
     */
    public $queue = 'listeners';

    /**
     * 创建事件监听器
     */
    public function __construct()
    {
        //
    }

    /**
     * 处理事件
     */
    public function handle(FaceSwapTaskFailed $event): void
    {
        try {
            $task = $event->task;
            
            Log::info('接收到FaceSwap任务失败事件', [
                'task_id' => $task->id,
                'batch_id' => $task->batch_id,
                'user_id' => $task->user_id,
                'status' => $task->status,
                'error_message' => $task->error_message
            ]);
            
            // 更新订单项状态为失败
            if ($task->batch_id) {
                $this->updateOrderItemStatus($task->batch_id, $task->error_message);
            }
            
        } catch (\Exception $e) {
            Log::error('处理FaceSwap任务失败事件失败', [
                'error' => $e->getMessage(),
                'task_id' => $event->task->id ?? null,
                'batch_id' => $event->task->batch_id ?? null
            ]);
        }
    }
    
    /**
     * 更新订单项状态为失败
     */
    private function updateOrderItemStatus(string $batchId, string $errorMessage): void
    {
        try {
            // 查找对应的订单项
            $orderItem = OrderItem::where('face_swap_batch_id', $batchId)->first();
            
            if (!$orderItem) {
                Log::warning('未找到对应的订单项', ['batch_id' => $batchId]);
                return;
            }
            
            $order = $orderItem->order;
            
            Log::info('更新订单项状态为失败', [
                'order_id' => $order->id,
                'order_item_id' => $orderItem->id,
                'batch_id' => $batchId,
                'error_message' => $errorMessage
            ]);
            
            // 更新订单项状态
            $orderItem->status = 'failed';
            $orderItem->error_message = 'AI换脸处理失败: ' . $errorMessage;
            $orderItem->save();
            
            // 检查订单状态是否需要更新
            $this->checkOrderStatus($order);
            
        } catch (\Exception $e) {
            Log::error('更新订单项状态失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 检查订单状态
     */
    private function checkOrderStatus(Order $order): void
    {
        try {
            $allItems = $order->items;
            $totalItems = $allItems->count();
            $failedItems = 0;
            $pendingItems = 0;
            
            foreach ($allItems as $item) {
                if ($item->status === 'failed') {
                    $failedItems++;
                } elseif (in_array($item->status, ['pending', 'processing'])) {
                    $pendingItems++;
                }
            }
            
            Log::info('检查订单状态（失败事件）', [
                'order_id' => $order->id,
                'total_items' => $totalItems,
                'failed_items' => $failedItems,
                'pending_items' => $pendingItems
            ]);
            
            // 如果所有订单项都失败了，或者有失败项且没有待处理项，标记订单为失败
            if ($failedItems === $totalItems || ($failedItems > 0 && $pendingItems === 0)) {
                $order->status = Order::STATUS_CANCELLED;
                $order->save();
                
                Log::info('订单因换脸失败而被取消', [
                    'order_id' => $order->id,
                    'failed_items' => $failedItems,
                    'total_items' => $totalItems
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('检查订单状态失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}