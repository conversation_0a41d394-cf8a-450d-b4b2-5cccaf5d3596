<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;

class FileHelper
{
    /**
     * 获取S3文件的预签名URL
     *
     * @param string $path 文件路径
     * @param string $disk 存储磁盘
     * @param int $expires 有效期（秒）
     * @param array $options 额外选项
     * @return string|null 预签名URL或null
     */
    public static function getPresignedUrl($path, $disk = 's3_picbook', $expires = 300, $options = [])
    {
        try {
            if (!Storage::disk($disk)->exists($path)) {
                return null;
            }
            
            // 设置默认内联显示
            if (!isset($options['ResponseContentDisposition'])) {
                $options['ResponseContentDisposition'] = 'inline';
            }
            
            $expiresAt = now()->addSeconds($expires);
            return Storage::disk($disk)->temporaryUrl($path, $expiresAt, $options);
        } catch (\Exception $e) {
            \Log::error('生成预签名URL失败', [
                'error' => $e->getMessage(),
                'path' => $path,
                'disk' => $disk
            ]);
            return null;
        }
    }
    
    /**
     * 获取S3文件的下载URL（带attachment头）
     *
     * @param string $path 文件路径
     * @param string $disk 存储磁盘
     * @param int $expires 有效期（秒）
     * @param string|null $filename 指定下载文件名，默认使用原文件名
     * @return string|null 预签名URL或null
     */
    public static function getDownloadUrl($path, $disk = 's3_picbook', $expires = 300, $filename = null)
    {
        if (empty($filename)) {
            $filename = basename($path);
        }
        
        return self::getPresignedUrl($path, $disk, $expires, [
            'ResponseContentDisposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }
} 