<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单处理进度更新</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #ffffff;
            padding: 30px;
            border: 1px solid #e9ecef;
            border-radius: 0 0 8px 8px;
        }
        .progress-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-completed {
            background-color: #28a745;
            color: white;
        }
        .status-processing {
            background-color: #007bff;
            color: white;
        }
        .status-pending {
            background-color: #6c757d;
            color: white;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>订单处理进度更新</h1>
        <p>您的订单正在处理中</p>
    </div>
    
    <div class="content">
        <p>亲爱的 {{ $user->name }}，</p>
        
        <p>您的订单 <strong>{{ $order->order_number }}</strong> 处理进度已更新：</p>
        
        <div class="progress-info">
            <h3>当前进度</h3>
            @if(isset($progress['step']) && isset($progress['total']))
                <p>正在处理第 {{ $progress['step'] }} / {{ $progress['total'] }} 步</p>
            @endif
            
            @if(isset($progress['current_task']))
                <p><strong>当前任务：</strong>{{ $progress['current_task'] }}</p>
            @endif
            
            @if(isset($progress['percentage']))
                <p>完成度：{{ $progress['percentage'] }}%</p>
            @endif
            
            @if(isset($progress['estimated_time']))
                <p>预计剩余时间：{{ $progress['estimated_time'] }}</p>
            @endif
        </div>
        
        <h3>处理步骤</h3>
        <ol>
            <li>
                AI换脸处理 
                @if(isset($progress['face_swap_completed']) && $progress['face_swap_completed'])
                    <span class="status-badge status-completed">已完成</span>
                @elseif(isset($progress['face_swap_processing']) && $progress['face_swap_processing'])
                    <span class="status-badge status-processing">处理中</span>
                @else
                    <span class="status-badge status-pending">待处理</span>
                @endif
            </li>
            <li>
                图片合成和文字处理
                @if(isset($progress['image_processing_completed']) && $progress['image_processing_completed'])
                    <span class="status-badge status-completed">已完成</span>
                @elseif(isset($progress['image_processing_processing']) && $progress['image_processing_processing'])
                    <span class="status-badge status-processing">处理中</span>
                @else
                    <span class="status-badge status-pending">待处理</span>
                @endif
            </li>
            <li>
                质量检查和打包
                @if(isset($progress['quality_check_completed']) && $progress['quality_check_completed'])
                    <span class="status-badge status-completed">已完成</span>
                @elseif(isset($progress['quality_check_processing']) && $progress['quality_check_processing'])
                    <span class="status-badge status-processing">处理中</span>
                @else
                    <span class="status-badge status-pending">待处理</span>
                @endif
            </li>
            <li>
                发货
                <span class="status-badge status-pending">待处理</span>
            </li>
        </ol>
        
        <p>您可以在订单页面实时查看处理进度。</p>
        
        <p>如有任何问题，请联系我们的客服团队。</p>
        
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
            <p>&copy; 2024 Dreamazebook. All rights reserved.</p>
        </div>
    </div>
</body>
</html>