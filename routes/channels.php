<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::routes(['middleware' => ['auth:sanctum'], 'prefix' => 'api']);

// 用户私有频道 - 用于所有用户相关通知
Broadcast::channel('user.{userId}', function ($user, $userId) {
    $isAuthorized = (int) $user->id === (int) $userId;
    
    \Illuminate\Support\Facades\Log::info('用户私有频道授权请求', [
        'channel' => "user.{$userId}",
        'auth_user_id' => $user->id,
        'requested_user_id' => $userId,
        'is_authorized' => $isAuthorized
    ]);
    
    return $isAuthorized;
});

// 队列状态频道 - 用于队列状态通知
Broadcast::channel('queue.status.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// AI换脸进度频道 - 用户专属（兼容旧版本）
Broadcast::channel('face-swap.{userId}', function ($user, $userId) {
    $isAuthorized = (int) $user->id === (int) $userId;
    
    \Illuminate\Support\Facades\Log::info('AI换脸进度频道授权请求', [
        'channel' => "face-swap.{$userId}",
        'auth_user_id' => $user->id,
        'requested_user_id' => $userId,
        'is_authorized' => $isAuthorized
    ]);
    
    return $isAuthorized;
});

// AI换脸批次频道 - 根据用户ID和批次ID验证
Broadcast::channel('face-swap-batch.{batchId}', function ($user, $batchId) {
    // 首先尝试从缓存获取批次信息
    $batchInfo = \Illuminate\Support\Facades\Cache::get('face_swap:batch:auth:' . $batchId);
    
    $isAuthorized = false;
    if ($batchInfo && isset($batchInfo['user_id'])) {
        $isAuthorized = (int) $user->id === (int) $batchInfo['user_id'];
    } else {
        // 如果缓存中没有，从数据库查询
        $batchRecord = \App\Models\AiFaceTask::where('batch_id', $batchId)
            ->where('type', 'batch')
            ->first();
            
        if ($batchRecord) {
            $isAuthorized = (int) $user->id === (int) $batchRecord->user_id;
            
            // 缓存批次信息以提高性能
            \Illuminate\Support\Facades\Cache::put(
                'face_swap:batch:auth:' . $batchId,
                ['user_id' => $batchRecord->user_id],
                now()->addHours(1)
            );
        }
    }
    
    \Illuminate\Support\Facades\Log::info('AI换脸批次频道授权请求', [
        'channel' => "face-swap-batch.{$batchId}",
        'auth_user_id' => $user->id,
        'batch_id' => $batchId,
        'batch_user_id' => $batchInfo['user_id'] ?? null,
        'is_authorized' => $isAuthorized
    ]);
    
    return $isAuthorized;
});

// 订单频道 - 用于订单状态通知
Broadcast::channel('orders.{userId}', function ($user, $userId) {
    $isAuthorized = (int) $user->id === (int) $userId;
    
    \Illuminate\Support\Facades\Log::info('订单频道授权请求', [
        'channel' => "orders.{$userId}",
        'auth_user_id' => $user->id,
        'requested_user_id' => $userId,
        'is_authorized' => $isAuthorized
    ]);
    
    return $isAuthorized;
});

// 管理后台频道 - 仅允许管理员访问
Broadcast::channel('admin-dashboard', function ($user) {
    $isAdmin = $user instanceof \App\Models\Admin;
    
    \Illuminate\Support\Facades\Log::info('管理后台频道授权请求', [
        'channel' => 'admin-dashboard',
        'user_id' => $user->id,
        'user_type' => get_class($user),
        'is_authorized' => $isAdmin
    ]);
    
    return $isAdmin;
});

// 公共队列状态频道 - 所有认证用户都可以访问
Broadcast::channel('face-swap-queue-status', function ($user) {
    // 所有认证用户都可以监听队列状态
    return true;
}); 