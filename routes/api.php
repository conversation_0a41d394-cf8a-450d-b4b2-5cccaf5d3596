<?php

use App\Http\Controllers\Api\Admin\BatchProcessController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ImageController;
use App\Http\Controllers\Auth\UserAuthController;
use App\Http\Controllers\Auth\AdminAuthController;
use App\Http\Controllers\Api\LanguageController;
use App\Http\Controllers\Api\Admin\PicbookController as AdminPicbookController;
use App\Http\Controllers\Api\PicbookController;
use App\Http\Controllers\Api\Admin\PicbookVariantController;
use App\Http\Controllers\Api\Admin\PicbookTranslationController;
use App\Http\Controllers\Api\Admin\UserController as AdminUserController;
use App\Http\Controllers\Api\Admin\OrderController as AdminOrderController;
use App\Http\Controllers\Api\Admin\PicbookPageController;
use App\Http\Controllers\Api\Admin\PicbookPageVariantController;
use App\Http\Controllers\Api\FileController;
use App\Http\Controllers\ProtectedImageController;
use App\Http\Controllers\Api\PreviewController;
use App\Http\Controllers\Api\CartController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\SimpleFaceSwapController;
use App\Http\Controllers\Api\AddressController;
use App\Http\Controllers\Api\ExpDebugController;
use App\Http\Controllers\Api\QueueController;
use App\Http\Controllers\Api\EnhancedPicbookController;
use App\Services\ExpService;

// 兼容性健康检查端点（支持 /healthz 路径）
Route::get('/healthz', function () {
    return response()->json(['status' => 'ok']);
});

Route::redirect('/', '/healthz');

// 语言切换路由
Route::prefix('language')->group(function () {
    Route::post('switch', [LanguageController::class, 'switch']);
    Route::get('current', [LanguageController::class, 'current']);
});

/*
|--------------------------------------------------------------------------
| 前台 API Routes
|--------------------------------------------------------------------------
*/

// 前台认证路由
Route::prefix('auth')->group(function () {
    Route::post('register', [UserAuthController::class, 'register']);
    Route::post('login', [UserAuthController::class, 'login']);
    Route::post('forgot-password', [UserAuthController::class, 'sendResetLink']);
    Route::middleware(['auth:sanctum', 'check.user.type:user'])->group(function () {
        Route::post('logout', [UserAuthController::class, 'logout']);
        Route::get('me', [UserAuthController::class, 'me']);
    });
});
// 绘本相关路由
Route::prefix('picbooks')->group(function () {
    Route::get('/', [PicbookController::class, 'index']);
    Route::get('/{id}', [PicbookController::class, 'show']);
    Route::get('/{id}/options', [PicbookController::class, 'options']);
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::post('/{id}/preview', [PicbookController::class, 'preview']);
    });
    // Route::get('/{id}/variant', [PicbookController::class, 'getVariant']);
    // Route::get('/{id}/pages', [PicbookController::class, 'getPages']);
});

// 增强版绘本处理路由
Route::prefix('enhanced-picbook')->group(function () {
    Route::get('/{picbook_id}/config', [EnhancedPicbookController::class, 'getPicbookConfig']);
    Route::post('/process-page', [EnhancedPicbookController::class, 'processPage']);
    Route::post('/batch-process', [EnhancedPicbookController::class, 'batchProcessPages']);
});

// 前台用户管理路由
Route::middleware(['auth:sanctum', 'check.user.type:user'])->group(function () {
    Route::prefix('user')->group(function () {
        Route::put('profile', [UserAuthController::class, 'updateProfile']);
        Route::put('password', [UserAuthController::class, 'updatePassword']);
    });

    // 文件预签名URL
    Route::post('files/presigned-url', [FileController::class, 'getPresignedUrl']);
    Route::post('files/upload', [FileController::class, 'userUpload']);

    // AI换脸进度
    Route::get('picbooks/face-swap/{batchId}/progress', [PicbookController::class, 'getFaceSwapProgress']);
    // AI换脸通知
    Route::get('face-swap/notifications', [PicbookController::class, 'getFaceSwapNotifications']);
    Route::post('face-swap/notifications/mark-read', [PicbookController::class, 'markFaceSwapNotificationsAsRead']);
    // 问题回答
    Route::post('picbooks/{id}/pages/{pageId}/answers', [PicbookController::class, 'saveAnswer']);
    Route::get('picbooks/{id}/pages/{pageId}/answers', [PicbookController::class, 'getAnswer']);
    Route::get('{uid}/picbook-images/{filename}', [ProtectedImageController::class, 'getProcessedImage'])->whereIn('middleware', ['auth', 'cache.headers:public;max_age=86400;etag'])->name('picbook.image');

    // 预览相关路由
    Route::prefix('preview')->group(function () {
        Route::get('/progress', [PreviewController::class, 'progress']);
        Route::get('/list', [PreviewController::class, 'list']);
        Route::get('/detail/{id}', [PreviewController::class, 'detail']);
        Route::post('/update-options/{id}', [PreviewController::class, 'updateOptions']);
        Route::get('/options/{id}', [PreviewController::class, 'getOptions']);
        Route::post('/options-price', [PreviewController::class, 'getOptionsPrice']);
    });
    // 简化版换脸路由
    Route::prefix('simple-face-swap')->group(function () {
        // Route::post('/create', [SimpleFaceSwapController::class, 'create']);
        Route::post('/create-by-picbook', [SimpleFaceSwapController::class, 'createByPicbookId']);
        Route::post('/preview', [SimpleFaceSwapController::class, 'preview']);
        Route::get('/{batchId}/status', [SimpleFaceSwapController::class, 'status']);
        Route::get('/queue/status', [SimpleFaceSwapController::class, 'getQueueStatus']);
        Route::get('/preview-pages', [SimpleFaceSwapController::class, 'getPreviewPages']);
        Route::post('/create-full-book', [SimpleFaceSwapController::class, 'createFullBookBatch']);
    });

    // 购物车相关路由
    Route::prefix('cart')->group(function () {
        Route::post('/add', [CartController::class, 'add']);
        Route::post('/update/{id}', [CartController::class, 'update']);
        Route::delete('/remove/{id}', [CartController::class, 'remove']);
        Route::delete('/clear', [CartController::class, 'clear']);
        Route::get('/list', [CartController::class, 'list']);
        Route::get('/validate', [CartController::class, 'validate']);
    });

    // 增强版绘本订单处理路由
    Route::prefix('enhanced-picbook')->group(function () {
        Route::post('/process-order', [EnhancedPicbookController::class, 'processOrderImages']);
        Route::post('/update-dedication', [EnhancedPicbookController::class, 'updateOrderDedication']);
    });

    // 订单相关路由
    Route::prefix('order')->group(function () {
        Route::post('/create', [OrderController::class, 'create']);
        Route::get('/list', [OrderController::class, 'list']);
        Route::get('/detail/{id}', [OrderController::class, 'detail']);
        Route::post('/cancel/{id}', [OrderController::class, 'cancel']);
        Route::put('/update-address/{id}', [OrderController::class, 'updateAddress']);
        Route::put('/select-shipping/{id}', [OrderController::class, 'selectShippingMethod']);
        Route::put('/update-message/{id}', [OrderController::class, 'updateMessage']);
        Route::post('/confirm/{id}', [OrderController::class, 'confirm']);
        Route::post('/repay/{id}', [OrderController::class, 'repay']);
        Route::get('/processing-progress/{id}', [OrderController::class, 'getProcessingProgress']);
        Route::get('/tracking/{id}', [OrderController::class, 'getTrackingInfo']);
        Route::get('/available-addresses', [OrderController::class, 'getAvailableAddresses']);
        Route::post('/calculate-shipping', [OrderController::class, 'calculateShippingCost']);
        Route::get('/shipping-methods', [OrderController::class, 'getShippingMethods']);
        // Route::get('/physical-config', [OrderController::class, 'getPhysicalConfig']);
    });

    // Stripe支付相关路由
    Route::prefix('stripe')->group(function () {
        // PaymentIntent相关路由
        Route::post('/confirm-payment', [App\Http\Controllers\Api\StripePaymentController::class, 'confirmPayment']);
        Route::post('/check-payment-status', [App\Http\Controllers\Api\StripePaymentController::class, 'checkPaymentStatus']);
        Route::post('/cancel-payment', [App\Http\Controllers\Api\StripePaymentController::class, 'cancelPayment']);

        // 支付配置
        Route::get('/payment-config', [App\Http\Controllers\Api\StripePaymentController::class, 'getPaymentConfig']);
    });

    // 地址管理路由
    Route::prefix('addresses')->group(function () {
        Route::get('/', [AddressController::class, 'index']);
        Route::post('/', [AddressController::class, 'store']);
        Route::get('/default', [AddressController::class, 'getDefault']);
        Route::get('/{id}', [AddressController::class, 'show']);
        Route::put('/{id}', [AddressController::class, 'update']);
        Route::delete('/{id}', [AddressController::class, 'destroy']);
        Route::post('/{id}/set-default', [AddressController::class, 'setDefault']);
    });

    // 物流相关路由
    Route::prefix('shipping')->group(function () {
        Route::post('/prices', [App\Http\Controllers\Api\ShippingController::class, 'getPrices']);
        Route::get('/countries', [App\Http\Controllers\Api\ShippingController::class, 'getCountries']);
    });

    // 汇率相关路由
    Route::prefix('currency')->group(function () {
        Route::get('/rate', [App\Http\Controllers\Api\CurrencyController::class, 'getExchangeRate']);
        Route::post('/convert', [App\Http\Controllers\Api\CurrencyController::class, 'convertCurrency']);
        Route::post('/batch-rates', [App\Http\Controllers\Api\CurrencyController::class, 'getBatchRates']);
        Route::get('/supported', [App\Http\Controllers\Api\CurrencyController::class, 'getSupportedCurrencies']);
    });

    // 队列管理路由
    Route::prefix('queue')->group(function () {
        Route::get('/stats', [QueueController::class, 'getQueueStats']);
        Route::get('/tasks', [QueueController::class, 'getUserTasks']);
    });
});

/*
|--------------------------------------------------------------------------
| 后台 API Routes
|--------------------------------------------------------------------------
*/

// 后台认证路由（不带版本号）
Route::prefix('admin')->group(function () {
    Route::post('login', [AdminAuthController::class, 'login']);
    Route::middleware(['auth:sanctum', 'check.user.type:admin'])->group(function () {
        Route::post('logout', [AdminAuthController::class, 'logout']);
        Route::get('me', [AdminAuthController::class, 'me']);
        // 后台文件上传路由
        Route::prefix('files')->group(function () {
            Route::post('upload', [FileController::class, 'adminUpload']);
            Route::delete('delete', [FileController::class, 'destroy']);
            Route::post('presigned-url', [FileController::class, 'getPresignedUrl']);
        });
    });
});

// 后台API V1版本路由组
Route::prefix('admin')->middleware(['auth:sanctum', 'check.user.type:admin'])->group(function () {
    // 用户管理路由
    Route::prefix('users')->group(function () {
        Route::get('/', [AdminUserController::class, 'index']);
        Route::post('{id}/disable', [AdminUserController::class, 'disable']);
        Route::post('{id}/enable', [AdminUserController::class, 'enable']);
    });

    // 订单管理路由
    Route::prefix('orders')->group(function () {
        Route::get('/', [AdminOrderController::class, 'index']);
        Route::get('/{id}', [AdminOrderController::class, 'show']);
        Route::put('/{id}/status', [AdminOrderController::class, 'updateStatus']);
        Route::put('/{id}/payment-status', [AdminOrderController::class, 'updatePaymentStatus']);
        Route::put('/{id}/processing-status', [AdminOrderController::class, 'updateProcessingStatus']);
        Route::post('/{id}/process', [AdminOrderController::class, 'processOrder']);
        Route::post('/batch-update-status', [AdminOrderController::class, 'batchUpdateStatus']);
        Route::get('/statistics/overview', [AdminOrderController::class, 'getStatistics']);
        Route::post('/export', [AdminOrderController::class, 'export']);
    });

    // 绘本管理路由
    Route::apiResource('picbooks', AdminPicbookController::class);

    // 绘本变体管理路由
    Route::prefix('picbook_variants')->group(function () {
        Route::get('/', [PicbookVariantController::class, 'index']);
        Route::post('/', [PicbookVariantController::class, 'store']);
        Route::get('/{id}', [PicbookVariantController::class, 'show']);
        Route::put('/{id}', [PicbookVariantController::class, 'update']);
        Route::delete('/{id}', [PicbookVariantController::class, 'destroy']);
        Route::post('/batch', [PicbookVariantController::class, 'batchStore']);
    });

    // 绘本页面管理路由
    Route::prefix('picbook-pages')->group(function () {
        Route::get('/', [PicbookPageController::class, 'index']);
        Route::post('/', [PicbookPageController::class, 'store']);
        Route::get('/{id}', [PicbookPageController::class, 'show']);
        Route::put('/{id}', [PicbookPageController::class, 'update']);
        Route::delete('/{id}', [PicbookPageController::class, 'destroy']);
    });

    // 绘本页面变体管理路由
    Route::prefix('picbook-page-variants')->group(function () {
        Route::get('/', [PicbookPageVariantController::class, 'index']);
        Route::post('/', [PicbookPageVariantController::class, 'store']);
        Route::get('/{id}', [PicbookPageVariantController::class, 'show']);
        Route::put('/{id}', [PicbookPageVariantController::class, 'update']);
        Route::delete('/{id}', [PicbookPageVariantController::class, 'destroy']);
    });

    // 物流管理路由
    Route::prefix('logistics')->group(function () {
        Route::get('/eligible-orders', [App\Http\Controllers\Api\Admin\LogisticsController::class, 'getEligibleOrders']);
        Route::get('/orders/{orderId}/check', [App\Http\Controllers\Api\Admin\LogisticsController::class, 'checkOrderEligibility']);
        Route::post('/orders/{orderId}/create', [App\Http\Controllers\Api\Admin\LogisticsController::class, 'createLogisticsOrder']);
        Route::post('/orders/batch-create', [App\Http\Controllers\Api\Admin\LogisticsController::class, 'batchCreateLogisticsOrders']);
        Route::get('/orders', [App\Http\Controllers\Api\Admin\LogisticsController::class, 'getLogisticsOrders']);
    });

    // 汇率管理路由
    Route::prefix('currency')->group(function () {
        Route::post('/clear-cache', [App\Http\Controllers\Api\CurrencyController::class, 'clearCache']);
        Route::get('/rate', [App\Http\Controllers\Api\CurrencyController::class, 'getExchangeRate']);
        Route::post('/batch-rates', [App\Http\Controllers\Api\CurrencyController::class, 'getBatchRates']);
    });
    //批量处理相关路由
    Route::prefix('picbooks')->group(function () {
        //批量处理相关路由
        // Route::post('{id}/process-pages', [BatchProcessController::class, 'processPages']);
        //批量创建绘本变体
        Route::post('{id}/generate-variants', [BatchProcessController::class, 'generateVariants']);
        //批量创建绘本页面变体
        Route::post('{id}/generate-page-variants', [BatchProcessController::class, 'generatePageVariants']);
        //批量设置绘本页面变体蒙版
        Route::post('{id}/set-page-variants-masks', [BatchProcessController::class, 'setPageVariantsMasks']);
        //批量设置绘本页面变体内容
        Route::post('{id}/set-page-variants-content', [BatchProcessController::class, 'setPageVariantsContent']);
        //批量发布绘本封面
        Route::post('{id}/batch-publish-covers', [AdminPicbookController::class, 'batchPublishCovers']);
        //批量发布绘本页面
        Route::post('{id}/batch-publish-pages', [AdminPicbookController::class, 'batchPublishPages']);

        //查询批次处理进度
        Route::get('batch-progress/{batchId}', [BatchProcessController::class, 'getBatchProgress']);

        //批量处理相关日志
        Route::get('{id}/logs', [BatchProcessController::class, 'getLogs']);

        // 封面变体管理路由
        Route::post('{id}/cover-variants', [AdminPicbookController::class, 'storeCoverVariant']);
        Route::get('{id}/cover-variants', [AdminPicbookController::class, 'getCoverVariants']);
        Route::put('{id}/cover-variants/{variantId}', [AdminPicbookController::class, 'updateCoverVariant']);
        Route::delete('{id}/cover-variants/{variantId}', [AdminPicbookController::class, 'deleteCoverVariant']);
    });
});

// 支付回调路由（不需要身份验证）
Route::post('/payment/callback', [OrderController::class, 'paymentCallback']);

// Stripe Webhook路由（不需要身份验证）
Route::post('/stripe/webhook', [App\Http\Controllers\Api\StripePaymentController::class, 'handleWebhook']);

// 高级图像处理路由（不需要身份验证，用于测试）
Route::prefix('advanced-image')->group(function () {
    Route::post('/process', [App\Http\Controllers\Api\AdvancedImageController::class, 'processImage']);
    Route::post('/batch-process', [App\Http\Controllers\Api\AdvancedImageController::class, 'batchProcess']);
    Route::post('/process-page', [App\Http\Controllers\Api\AdvancedImageController::class, 'processPageFromConfig']);
    Route::post('/test-page', [App\Http\Controllers\Api\AdvancedImageController::class, 'processTestPage']);
    Route::get('/download/{filename}', [App\Http\Controllers\Api\AdvancedImageController::class, 'downloadResult']);
    Route::delete('/cleanup/{sessionId}', [App\Http\Controllers\Api\AdvancedImageController::class, 'cleanup']);
    Route::get('/health', [App\Http\Controllers\Api\AdvancedImageController::class, 'healthCheck']);
    Route::get('/sample-config', [App\Http\Controllers\Api\AdvancedImageController::class, 'getSampleConfig']);
});



// EXP物流调试路由（开发环境使用）
if (app()->environment(['local', 'testing', 'development'])) {
    Route::prefix('exp-debug')->group(function () {
        Route::post('/test-connection', [App\Http\Controllers\Api\ExpDebugController::class, 'testConnection']);
        Route::post('/create-test-order', [ExpDebugController::class, 'createTestOrder']);
        Route::post('/query-order', [App\Http\Controllers\Api\ExpDebugController::class, 'queryOrder']);
        Route::post('/query-freight', [App\Http\Controllers\Api\ExpDebugController::class, 'queryFreight']);
        Route::post('/batch-test', [ExpDebugController::class, 'testDropdownApis']);
        Route::post('/yfjs', [App\Http\Controllers\Api\ExpDebugController::class, 'testFreightCalculation']);
        Route::post('/update-weight', [App\Http\Controllers\Api\ExpDebugController::class, 'testUpdateWeight']);
        Route::post('/verify-signature', [App\Http\Controllers\Api\ExpDebugController::class, 'verifySignatureAlgorithm']);
        Route::post('/compare-signatures', [App\Http\Controllers\Api\ExpDebugController::class, 'compareSignatures']);
        Route::get('/debug-logs', [App\Http\Controllers\Api\ExpDebugController::class, 'getDebugLogs']);
        Route::delete('/clear-logs', [App\Http\Controllers\Api\ExpDebugController::class, 'clearDebugLogs']);
    });
}
