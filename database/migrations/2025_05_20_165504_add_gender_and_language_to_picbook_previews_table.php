<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('picbook_previews', function (Blueprint $table) {
            $table->string('gender')->nullable()->after('picbook_id')->comment('性别');
            $table->string('language')->nullable()->after('gender')->comment('语言');
            $table->json('skin_color')->nullable()->after('language')->comment('肤色');
            $table->json('options')->nullable()->after('skin_color')->comment('选项');
            $table->json('result_images')->nullable()->after('options')->comment('结果图片');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('picbook_previews', function (Blueprint $table) {
            $table->dropColumn(['gender', 'language', 'skin_color', 'options', 'result_images']);
        });
    }
}; 