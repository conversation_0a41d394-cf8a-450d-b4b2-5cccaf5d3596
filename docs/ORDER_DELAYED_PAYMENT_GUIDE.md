# 订单延迟支付功能指南 / Order Delayed Payment Guide

## 概述 / Overview

本系统已实现订单延迟支付功能，允许用户在订单创建后的任何时间进行支付。系统会自动为待支付的订单提供PaymentIntent信息，支持用户重新支付失败的订单。

This system implements delayed payment functionality for orders, allowing users to pay for their orders at any time after creation. The system automatically provides PaymentIntent information for pending orders and supports retry payments for failed orders.

## 核心功能 / Core Features

### 1. **自动PaymentIntent集成 / Automatic PaymentIntent Integration**
- 订单详情API自动包含PaymentIntent信息（仅限待支付订单）
- 智能检测现有PaymentIntent状态
- 自动创建新的PaymentIntent（如需要）

### 2. **重新支付支持 / Retry Payment Support**
- 专用的重新支付API端点
- 支持支付失败订单的重新支付
- 智能PaymentIntent复用机制

### 3. **状态权限管理 / Status Permission Management**
- 基于订单状态的支付权限控制
- 详细的操作权限信息
- 多语言状态文本支持

## API端点 / API Endpoints

### 1. 获取订单详情（含PaymentIntent）/ Get Order Details (with PaymentIntent)

```http
GET /api/order/detail/{id}
```

**请求头 / Request Headers:**
```http
Authorization: Bearer {token}
Accept-Language: zh|en
```

**响应示例 / Response Example:**
```json
{
    "success": true,
    "code": 200,
    "message": "订单详情获取成功",
    "data": {
        "order": {
            "id": 12345,
            "order_number": "ORD-20250129-001",
            "status": "pending",
            "status_text": "待付款",
            "payment_status": "pending",
            "payment_status_text": "待付款",
            "payment_method": "stripe",
            "total_amount": 29.99,
            "currency": "USD",
            "permissions": {
                "can_cancel": true,
                "can_update_message": true,
                "can_update_address": true,
                "can_confirm": false,
                "can_pay": true,
                "should_auto_confirm": false
            }
        },
        "payment_data": {
            "client_secret": "pi_1234567890_secret_abcdef",
            "payment_intent_id": "pi_1234567890",
            "publishable_key": "pk_test_1234567890",
            "amount": 2999,
            "currency": "usd",
            "status": "requires_payment_method",
            "is_existing": false
        }
    }
}
```

**PaymentIntent包含条件 / PaymentIntent Inclusion Conditions:**
- 订单状态为 `pending`
- 支付状态为 `pending`
- 支付方式为 `stripe`

### 2. 重新支付订单 / Retry Order Payment

```http
POST /api/order/repay/{id}
```

**请求头 / Request Headers:**
```http
Authorization: Bearer {token}
Accept-Language: zh|en
```

**响应示例 / Response Example:**
```json
{
    "success": true,
    "code": 200,
    "message": "支付意图创建成功",
    "data": {
        "order": {
            "id": 12345,
            "order_number": "ORD-20250129-001",
            "status": "pending",
            "status_text": "待付款",
            "payment_status": "pending",
            "payment_status_text": "待付款"
        },
        "payment_data": {
            "client_secret": "pi_1234567890_secret_abcdef",
            "payment_intent_id": "pi_1234567890",
            "publishable_key": "pk_test_1234567890",
            "amount": 2999,
            "currency": "usd",
            "status": "requires_payment_method",
            "is_existing": true
        }
    }
}
```

**重新支付条件 / Retry Payment Conditions:**
- 订单状态为 `pending`
- 支付状态为 `pending` 或 `failed`
- 支付方式为 `stripe` 或 `null`

## PaymentIntent字段说明 / PaymentIntent Field Description

| 字段 / Field | 类型 / Type | 说明 / Description |
|-------------|-------------|-------------------|
| `client_secret` | string | Stripe客户端密钥，用于前端支付确认 |
| `payment_intent_id` | string | PaymentIntent唯一标识符 |
| `publishable_key` | string | Stripe可发布密钥 |
| `amount` | integer | 支付金额（最小货币单位） |
| `currency` | string | 货币代码（小写） |
| `status` | string | PaymentIntent状态 |
| `is_existing` | boolean | 是否为现有PaymentIntent |

## PaymentIntent状态 / PaymentIntent Status

| 状态 / Status | 说明 / Description |
|--------------|-------------------|
| `requires_payment_method` | 需要支付方式 |
| `requires_confirmation` | 需要确认支付 |
| `requires_action` | 需要用户操作（如3D验证） |
| `processing` | 处理中 |
| `succeeded` | 支付成功 |
| `canceled` | 已取消 |

## 订单支付权限矩阵 / Order Payment Permission Matrix

| 订单状态 / Order Status | 支付状态 / Payment Status | 可支付 / Can Pay | 说明 / Description |
|------------------------|---------------------------|------------------|-------------------|
| `pending` | `pending` | ✅ | 待付款订单，可以支付 |
| `pending` | `failed` | ✅ | 支付失败，可以重新支付 |
| `processing` | `paid` | ❌ | 已支付，不能重新支付 |
| `confirmed` | `paid` | ❌ | 已确认，不能重新支付 |
| `completed` | `paid` | ❌ | 已完成，不能重新支付 |
| `cancelled` | `refunded` | ❌ | 已取消，不能重新支付 |

## 前端集成 / Frontend Integration

### 1. React/JavaScript集成示例 / React/JavaScript Integration Example

```javascript
import { loadStripe } from '@stripe/stripe-js';

class OrderPayment {
    constructor() {
        this.stripe = null;
    }

    async initStripe(publishableKey) {
        this.stripe = await loadStripe(publishableKey);
    }

    // 获取订单详情
    async getOrderDetail(orderId) {
        try {
            const response = await fetch(`/api/order/detail/${orderId}`, {
                headers: {
                    'Authorization': `Bearer ${this.getToken()}`,
                    'Accept-Language': this.getLocale()
                }
            });

            const data = await response.json();

            if (data.success) {
                // 检查是否需要支付
                if (data.data.payment_data) {
                    await this.initStripe(data.data.payment_data.publishable_key);
                    this.showPaymentUI(data.data);
                } else {
                    this.showOrderInfo(data.data.order);
                }
            }

            return data;
        } catch (error) {
            console.error('获取订单详情失败:', error);
            throw error;
        }
    }

    // 重新支付订单
    async repayOrder(orderId) {
        try {
            const response = await fetch(`/api/order/repay/${orderId}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.getToken()}`,
                    'Accept-Language': this.getLocale()
                }
            });

            const data = await response.json();

            if (data.success && data.data.payment_data) {
                await this.initStripe(data.data.payment_data.publishable_key);
                return this.processPayment(data.data.payment_data);
            }

            return data;
        } catch (error) {
            console.error('重新支付失败:', error);
            throw error;
        }
    }

    // 处理支付
    async processPayment(paymentData) {
        if (!this.stripe) {
            throw new Error('Stripe未初始化');
        }

        const { client_secret } = paymentData;

        // 确认支付
        const result = await this.stripe.confirmCardPayment(client_secret, {
            payment_method: {
                card: this.cardElement,
                billing_details: {
                    name: this.getBillingName()
                }
            }
        });

        if (result.error) {
            console.error('支付失败:', result.error);
            this.showPaymentError(result.error.message);
            return { success: false, error: result.error };
        } else {
            console.log('支付成功:', result.paymentIntent);
            this.showPaymentSuccess();
            // 刷新订单状态
            setTimeout(() => this.getOrderDetail(this.currentOrderId), 2000);
            return { success: true, paymentIntent: result.paymentIntent };
        }
    }

    // 显示支付UI
    showPaymentUI(orderData) {
        const { order, payment_data } = orderData;
        
        // 显示订单信息
        document.getElementById('order-info').innerHTML = `
            <h3>订单 ${order.order_number}</h3>
            <p>状态: ${order.status_text}</p>
            <p>金额: ${order.total_amount} ${order.currency}</p>
        `;

        // 显示支付按钮
        const payButton = document.getElementById('pay-button');
        payButton.style.display = 'block';
        payButton.onclick = () => this.processPayment(payment_data);

        // 如果是现有PaymentIntent，显示提示
        if (payment_data.is_existing) {
            document.getElementById('payment-notice').innerHTML = 
                '<p class="notice">检测到未完成的支付，您可以继续完成支付。</p>';
        }
    }

    // 工具方法
    getToken() {
        return localStorage.getItem('auth_token');
    }

    getLocale() {
        return localStorage.getItem('locale') || 'en';
    }

    getBillingName() {
        return localStorage.getItem('user_name') || 'Customer';
    }
}

// 使用示例
const orderPayment = new OrderPayment();

// 页面加载时获取订单详情
document.addEventListener('DOMContentLoaded', () => {
    const orderId = new URLSearchParams(window.location.search).get('order_id');
    if (orderId) {
        orderPayment.getOrderDetail(orderId);
    }
});
```

### 2. Vue.js集成示例 / Vue.js Integration Example

```vue
<template>
  <div class="order-payment">
    <div v-if="order" class="order-info">
      <h3>{{ $t('order.number') }}: {{ order.order_number }}</h3>
      <p>{{ $t('order.status') }}: {{ order.status_text }}</p>
      <p>{{ $t('order.amount') }}: {{ order.total_amount }} {{ order.currency }}</p>
    </div>

    <div v-if="paymentData" class="payment-section">
      <div v-if="paymentData.is_existing" class="payment-notice">
        <p>{{ $t('order.existing_payment_notice') }}</p>
      </div>
      
      <div id="card-element" class="card-element"></div>
      
      <button 
        @click="processPayment" 
        :disabled="processing"
        class="pay-button"
      >
        {{ processing ? $t('order.processing') : $t('order.pay_now') }}
      </button>
    </div>

    <div v-if="!paymentData && order && order.permissions.can_pay" class="repay-section">
      <button @click="repayOrder" class="repay-button">
        {{ $t('order.repay') }}
      </button>
    </div>
  </div>
</template>

<script>
import { loadStripe } from '@stripe/stripe-js';

export default {
  name: 'OrderPayment',
  data() {
    return {
      order: null,
      paymentData: null,
      stripe: null,
      cardElement: null,
      processing: false
    };
  },
  async mounted() {
    const orderId = this.$route.params.id;
    if (orderId) {
      await this.getOrderDetail(orderId);
    }
  },
  methods: {
    async getOrderDetail(orderId) {
      try {
        const response = await this.$http.get(`/api/order/detail/${orderId}`);
        
        if (response.data.success) {
          this.order = response.data.data.order;
          this.paymentData = response.data.data.payment_data;
          
          if (this.paymentData) {
            await this.initStripe();
          }
        }
      } catch (error) {
        this.$message.error(this.$t('order.get_detail_failed'));
      }
    },

    async repayOrder() {
      try {
        const response = await this.$http.post(`/api/order/repay/${this.order.id}`);
        
        if (response.data.success) {
          this.paymentData = response.data.data.payment_data;
          await this.initStripe();
        }
      } catch (error) {
        this.$message.error(this.$t('order.repay_failed'));
      }
    },

    async initStripe() {
      if (!this.paymentData) return;
      
      this.stripe = await loadStripe(this.paymentData.publishable_key);
      
      const elements = this.stripe.elements();
      this.cardElement = elements.create('card');
      this.cardElement.mount('#card-element');
    },

    async processPayment() {
      if (!this.stripe || !this.cardElement) return;
      
      this.processing = true;
      
      try {
        const result = await this.stripe.confirmCardPayment(
          this.paymentData.client_secret,
          {
            payment_method: {
              card: this.cardElement,
              billing_details: {
                name: this.$store.state.user.name
              }
            }
          }
        );

        if (result.error) {
          this.$message.error(result.error.message);
        } else {
          this.$message.success(this.$t('order.payment_success'));
          // 刷新订单状态
          setTimeout(() => this.getOrderDetail(this.order.id), 2000);
        }
      } catch (error) {
        this.$message.error(this.$t('order.payment_failed'));
      } finally {
        this.processing = false;
      }
    }
  }
};
</script>
```

## 错误处理 / Error Handling

### 常见错误及处理 / Common Errors and Handling

| 错误类型 / Error Type | 错误码 / Error Code | 处理方式 / Handling |
|----------------------|-------------------|-------------------|
| 订单不存在 | 404 | 重定向到订单列表 |
| 无权访问 | 403 | 显示权限错误信息 |
| 不能重新支付 | 400 | 显示订单状态说明 |
| PaymentIntent创建失败 | 500 | 显示支付系统错误 |
| 支付确认失败 | 400 | 显示具体支付错误 |

## 安全考虑 / Security Considerations

1. **权限验证 / Permission Verification**
   - 严格的用户权限检查
   - 订单所有权验证
   - 支付状态验证

2. **PaymentIntent安全 / PaymentIntent Security**
   - 客户端密钥安全传输
   - PaymentIntent状态验证
   - 重复支付防护

3. **数据完整性 / Data Integrity**
   - 订单金额一致性检查
   - 支付状态同步
   - Webhook验证

## 监控和日志 / Monitoring and Logging

系统会记录以下关键事件：
The system logs the following key events:

- PaymentIntent创建和检索
- 支付确认成功/失败
- 订单状态变更
- 权限检查失败
- 系统错误

## 测试 / Testing

运行延迟支付功能测试：
Run delayed payment functionality test:

```bash
php test_order_delayed_payment.php
```

这将显示不同场景下的API响应示例和前端集成代码。
This will show API response examples and frontend integration code for different scenarios.