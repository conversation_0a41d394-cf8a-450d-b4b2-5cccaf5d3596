<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\AiFaceTask;

class FaceSwapBatchCompleted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $batchRecord;
    public $userId;
    public $results;

    /**
     * Create a new event instance.
     */
    public function __construct(AiFaceTask $batchRecord, array $results = [])
    {
        $this->batchRecord = $batchRecord;
        $this->userId = $batchRecord->user_id;
        $this->results = $results;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->userId)
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'face-swap.batch.completed';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'type' => 'face_swap_batch_completed',
            'batch_id' => $this->batchRecord->batch_id,
            'status' => $this->batchRecord->status,
            'progress' => $this->batchRecord->progress,
            'total_tasks' => $this->batchRecord->total_tasks,
            'completed_tasks' => $this->batchRecord->completed_tasks,
            'is_priority' => $this->batchRecord->is_priority,
            'results' => $this->results,
            'timestamp' => now()->toISOString()
        ];
    }
}