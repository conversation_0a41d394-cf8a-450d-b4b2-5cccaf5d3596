<?php

namespace Database\Seeders;

use App\Models\ProductOption;
use Illuminate\Database\Seeder;

class ProductOptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清空现有数据
        ProductOption::truncate();
        
        // 封面选项
        $coverOptions = [
            [
                'option_type' => ProductOption::TYPE_COVER,
                'option_key' => 'default',
                'name' => '标准封面',
                'description' => '标准精装硬皮封面，适合大多数场景',
                'price' => 0,
                'currency_code' => 'USD',
                'is_default' => true,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 1
            ],
            [
                'option_type' => ProductOption::TYPE_COVER,
                'option_key' => 'premium',
                'name' => '高级封面',
                'description' => '高级皮质封面，触感更好，更加耐用',
                'price' => 20,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 2
            ],
            [
                'option_type' => ProductOption::TYPE_COVER,
                'option_key' => 'custom',
                'name' => '定制封面',
                'description' => '完全定制的封面，可以选择特殊材质和工艺',
                'price' => 50,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 3
            ]
        ];
        
        // 装帧选项
        $bindingOptions = [
            [
                'option_type' => ProductOption::TYPE_BINDING,
                'option_key' => 'standard',
                'name' => 'Premium Jumbo Hardcover',
                'description' => 'Premium Jumbo Hardcover',
                'price' => '19.90',
                'currency_code' => 'USD',
                'is_default' => true,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 1
            ],
            [
                'option_type' => ProductOption::TYPE_BINDING,
                'option_key' => 'hardcover',
                'name' => '精装装帧',
                'description' => '精装硬皮装帧，更加耐用',
                'price' => 30,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 2
            ],
            [
                'option_type' => ProductOption::TYPE_BINDING,
                'option_key' => 'special',
                'name' => '特殊装帧',
                'description' => '特殊工艺装帧，包括烫金、压纹等',
                'price' => 80,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 3
            ]
        ];
        
        // 礼盒选项
        $giftBoxOptions = [
            [
                'option_type' => ProductOption::TYPE_GIFT_BOX,
                'option_key' => 'standard',
                'name' => '标准礼盒',
                'description' => '精美礼盒包装，适合作为礼物赠送',
                'price' => 30,
                'currency_code' => 'USD',
                'is_default' => true,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 1
            ],
            [
                'option_type' => ProductOption::TYPE_GIFT_BOX,
                'option_key' => 'premium',
                'name' => '高级礼盒',
                'description' => '高级礼盒包装，包含贺卡和丝带',
                'price' => 60,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 2
            ]
        ];
        
        // 插入数据
        ProductOption::insert(array_merge($coverOptions, $bindingOptions, $giftBoxOptions));
        
        $this->command->info('产品选项数据已创建！');
    }
} 