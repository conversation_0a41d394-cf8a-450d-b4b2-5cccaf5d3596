<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * 注册应用程序的Artisan命令
     *
     * @var array
     */
    protected $commands = [
        \App\Console\Commands\ProcessPendingFaceSwapBatches::class,
        \App\Console\Commands\CleanupTimeoutFaceSwapTasks::class,
        \App\Console\Commands\FaceSwapQueueStats::class,
    ];

    /**
     * 定义应用程序的命令调度
     * 
     * 注意：在 Laravel 11 中，调度任务已迁移到 routes/console.php 文件中定义
     * 此方法保留用于向后兼容，但不再使用
     */
    protected function schedule(Schedule $schedule)
    {
        // Laravel 11 中的调度任务现在在 routes/console.php 中定义
        // 请参考 routes/console.php 文件查看当前的调度配置
    }

    /**
     * 注册命令
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');
        require base_path('routes/console.php');
    }
} 