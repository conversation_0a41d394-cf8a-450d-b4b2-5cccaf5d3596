<?php

namespace App\Services;

use App\Models\User;
use App\Mail\OrderConfirmationMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MailService
{
    /**
     * 发送重置密码邮件
     *
     * @param User $user
     * @return string 重置令牌
     */
    public function sendResetPasswordEmail(string $email): ?string
    {
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            return null;
        }

        // 生成重置令牌
        $token = Str::random(64);
        
        // 存储令牌到密码重置表
        DB::table('password_reset_tokens')->updateOrInsert(
            ['email' => $email],
            [
                'token' => $token,
                'created_at' => Carbon::now()
            ]
        );

        // 生成重置链接
        $resetLink = URL::to('/reset-password') . '?token=' . $token;

        Mail::send('emails.reset-password', [
            'name' => $user->name,
            'resetLink' => $resetLink
        ], function ($message) use ($user) {
            $message->to($user->email, $user->name)
                   ->subject('Reset Your Password - DreamAzeBook');
        });

        return $token;
    }

    /**
     * 发送欢迎邮件给新注册用户
     *
     * @param User $user
     * @return void
     */
    public function sendWelcomeEmail(User $user): void
    {
        Mail::send('emails.welcome', [
            'user' => $user,
            'name' => $user->name
        ], function ($message) use ($user) {
            $message->to($user->email, $user->name)
                   ->subject('Welcome to DreamAzeBook!');
        });
    }

    /**
     * 发送购物车订单确认邮件
     *
     * @param array|object $orderData 订单数据，包含：
     *                               - user (用户对象，包含email和name)
     *                               - order_number (订单号)
     *                               - items (订单商品数组)
     *                               - subtotal (小计金额)
     *                               - tax (税费)
     *                               - shipping_fee (运费)
     *                               - total (总金额)
     *                               - shipping_address (收货地址)
     *                               - created_at (创建时间)
     * @return bool 发送成功返回true，失败返回false
     */
    public function sendShoppingCartEmail($orderData): bool
    {
        try {
            // 如果传入的是数组，转换为对象
            $order = is_array($orderData) ? (object) $orderData : $orderData;
            
            // 确保用户数据可以作为对象访问
            if (is_array($order->user ?? null)) {
                $order->user = (object) $order->user;
            }
            
            // 使用新的Mailable类发送邮件
            Mail::to($order->user->email, $order->user->name ?? '')
                ->send(new OrderConfirmationMail($order));
            
            return true;
        } catch (\Exception $e) {
            // 记录错误日志
            \Log::error('发送订单确认邮件失败: ' . $e->getMessage());
            return false;
        }
    }
}