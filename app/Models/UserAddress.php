<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'first_name',
        'last_name',
        'company',
        'phone',
        'phone2',
        'email',
        'post_code',
        'country',
        'state',
        'city',
        'district',
        'street',
        'house_number',
        'second_name',
        'is_default',
    ];

    protected $casts = [
        'is_default' => 'boolean',
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 设置为默认地址
     */
    public function setAsDefault(): void
    {
        // 先将该用户的所有地址设为非默认
        static::where('user_id', $this->user_id)->update(['is_default' => false]);
        
        // 设置当前地址为默认
        $this->update(['is_default' => true]);
    }

    /**
     * 获取格式化的完整地址
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->house_number,
            $this->street,
            $this->district,
            $this->city,
            $this->state,
            $this->post_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * 获取收件人姓名
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }
}