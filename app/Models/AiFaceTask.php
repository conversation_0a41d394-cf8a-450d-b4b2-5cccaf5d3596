<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class AiFaceTask extends Model
{
    protected $fillable = [
        'task_id', 'status', 'input_image', 'mask_image',
        'face_image', 'result_image', 'error_message', 'user_id',
        'character_sequence', 'completed_at',
        'batch_id', 'task_index', 'type', 'is_priority',
        'target_image_url', 'face_image_url', 'result_image_url',
        'page_id', 'variant_id', 'config', 'result',
        'total_tasks', 'completed_tasks', 'progress'
    ];

    protected $casts = [
        'character_sequence' => 'array',
        'face_image' => 'array',
        'face_image_url' => 'array',
        'completed_at' => 'datetime',
        'config' => 'array',
        'result' => 'array',
        'is_priority' => 'boolean',
        'progress' => 'integer',
        'total_tasks' => 'integer',
        'completed_tasks' => 'integer'
    ];

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取关联的页面
     */
    public function page()
    {
        return $this->belongsTo(PicbookPage::class, 'page_id');
    }

    /**
     * 获取关联的页面变体
     */
    public function variant()
    {
        return $this->belongsTo(PicbookPageVariant::class, 'variant_id');
    }

    /**
     * 获取同一批次的所有任务
     */
    public function batchTasks()
    {
        return $this->hasMany(AiFaceTask::class, 'batch_id', 'batch_id')
            ->where('type', 'task')
            ->orderBy('task_index');
    }

    /**
     * 判断任务是否完成
     *
     * @return bool
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * 判断任务是否失败
     *
     * @return bool
     */
    public function isFailed()
    {
        return $this->status === 'failed';
    }

    /**
     * 判断任务是否处理中
     *
     * @return bool
     */
    public function isProcessing()
    {
        return $this->status === 'pending' || $this->status === 'processing';
    }

    /**
     * 获取结果图片的URL
     *
     * @return string|null
     */
    public function getResultImageUrl()
    {
        if ($this->result_image_url) {
            return $this->result_image_url;
        }

        if (!$this->result_image) {
            return null;
        }

        return url(Storage::disk('public')->url($this->result_image));
    }
}
