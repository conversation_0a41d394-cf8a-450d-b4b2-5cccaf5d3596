<?php

/**
 * 队列状态通知功能测试脚本
 * 
 * 使用方法：
 * php scripts/test-queue-notifications.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\SimpleFaceSwapService;
use App\Events\AiFaceQueueStatus;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== 队列状态通知功能测试 ===\n\n";

try {
    $faceSwapService = app(SimpleFaceSwapService::class);
    
    // 测试用户ID
    $testUserId = 1;
    
    echo "1. 测试获取队列状态...\n";
    $queueStatus = $faceSwapService->getQueueStatus($testUserId);
    
    if ($queueStatus['success']) {
        echo "✅ 队列状态获取成功\n";
        echo "   - 队列信息: " . json_encode($queueStatus['queue_info'], JSON_UNESCAPED_UNICODE) . "\n";
        echo "   - 用户任务数: " . count($queueStatus['user_tasks']) . "\n";
    } else {
        echo "❌ 队列状态获取失败: " . $queueStatus['error'] . "\n";
        exit(1);
    }
    
    echo "\n2. 测试发送队列状态更新通知...\n";
    $faceSwapService->sendQueueStatusUpdate($testUserId);
    echo "✅ 队列状态更新通知已发送\n";
    
    echo "\n3. 测试事件创建...\n";
    $event = new AiFaceQueueStatus(
        $testUserId,
        $queueStatus['queue_info']['regular_queue_length'] ?? 0,
        $queueStatus['queue_info']['priority_queue_length'] ?? 0,
        'regular',
        $queueStatus['queue_info']['queue_position'] ?? 0,
        $queueStatus['queue_info']['estimated_wait_time'] ?? 0
    );
    echo "✅ AiFaceQueueStatus 事件创建成功\n";
    echo "   - 用户ID: " . $event->userId . "\n";
    echo "   - 频道: " . $event->broadcastOn()[0]->name . "\n";
    echo "   - 事件名: " . $event->broadcastAs() . "\n";
    
    echo "\n=== 测试完成 ===\n";
    echo "✅ 所有测试通过！队列状态通知功能正常工作。\n\n";
    
    echo "前端监听示例:\n";
    echo "```javascript\n";
    echo "// 监听用户私有频道\n";
    echo "window.Echo.private('user.{$testUserId}')\n";
    echo "    .listen('face-swap.queue-status', (e) => {\n";
    echo "        console.log('队列状态更新:', e);\n";
    echo "        // 更新UI显示队列信息\n";
    echo "        updateQueueStatus(e);\n";
    echo "    });\n";
    echo "\n";
    echo "// 监听公共频道\n";
    echo "window.Echo.channel('face-swap-queue-status')\n";
    echo "    .listen('face-swap.queue-status', (e) => {\n";
    echo "        console.log('公共队列状态更新:', e);\n";
    echo "        // 更新UI显示队列信息\n";
    echo "        updateQueueStatus(e);\n";
    echo "    });\n";
    echo "```\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
    exit(1);
}