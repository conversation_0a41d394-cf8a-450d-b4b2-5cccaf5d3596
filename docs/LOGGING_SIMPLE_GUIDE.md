# Laravel 日志按天拆分 - 简化配置

## 配置说明

已将 Laravel 日志配置为按天拆分，无需迁移现有日志文件。

### 修改的配置文件

#### 1. `config/logging.php`
- 默认日志频道改为 `daily`
- 添加了专用日志频道

#### 2. `.env` 文件
```env
LOG_CHANNEL=daily
LOG_DAILY_DAYS=30
```

## 可用的日志频道

| 频道名 | 文件格式 | 用途 |
|--------|----------|------|
| `daily` | `laravel-YYYY-MM-DD.log` | 默认日志 |
| `face_swap` | `face_swap-YYYY-MM-DD.log` | AI换脸服务 |
| `queue` | `queue-YYYY-MM-DD.log` | 队列处理 |
| `api` | `api-YYYY-MM-DD.log` | API请求 |
| `order` | `order-YYYY-MM-DD.log` | 订单处理 |
| `websocket` | `websocket-YYYY-MM-DD.log` | WebSocket通知 |
| `error` | `error-YYYY-MM-DD.log` | 错误日志 |
| `performance` | `performance-YYYY-MM-DD.log` | 性能监控 |

## 使用方法

### 基本使用
```php
use Illuminate\Support\Facades\Log;

// 默认日志
Log::info('这是一条信息');

// 使用专用频道
Log::channel('face_swap')->info('换脸任务开始', ['batch_id' => $batchId]);
Log::channel('api')->info('API请求', ['endpoint' => '/api/test']);
Log::channel('error')->error('发生错误', ['error' => $e->getMessage()]);
```

### 在 SimpleFaceSwapService 中使用
```php
Log::channel('face_swap')->info('创建换脸批次', [
    'batch_id' => $batchId,
    'user_id' => $userId
]);
```

### 在 Controller 中使用
```php
Log::channel('api')->info('API请求', [
    'method' => $request->method(),
    'url' => $request->url(),
    'user_id' => $request->user()?->id
]);
```

## 日志文件管理

### 自动清理
Laravel 会根据配置的 `days` 参数自动清理过期的日志文件：
- 默认日志：保留 30 天
- 错误日志：保留 60 天  
- 性能日志：保留 7 天

### 手动查看日志
```bash
# 查看今天的默认日志
tail -f storage/logs/laravel-$(date +%Y-%m-%d).log

# 查看换脸服务日志
tail -f storage/logs/face_swap-$(date +%Y-%m-%d).log

# 查看错误日志
tail -f storage/logs/error-$(date +%Y-%m-%d).log
```

## 配置生效

配置修改后立即生效，新的日志将按天拆分存储。现有的 `laravel.log` 文件不会被影响，可以手动删除或保留。

## 环境变量

可以通过 `.env` 文件调整配置：

```env
# 日志频道
LOG_CHANNEL=daily

# 日志级别
LOG_LEVEL=debug

# 日志保留天数
LOG_DAILY_DAYS=30
```

这样配置后，系统将自动按天拆分日志，不同模块可以使用专门的日志频道，便于日志管理和问题排查。