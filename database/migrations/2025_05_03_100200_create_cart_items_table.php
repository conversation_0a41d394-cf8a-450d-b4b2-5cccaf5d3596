<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('preview_id')->constrained('picbook_previews')->onDelete('cascade');
            $table->integer('quantity')->default(1)->comment('数量');
            $table->decimal('price', 10, 2)->default(0)->comment('基础价格');
            $table->decimal('cover_price', 10, 2)->default(0)->comment('封面价格');
            $table->decimal('binding_price', 10, 2)->default(0)->comment('装帧价格');
            $table->decimal('gift_box_price', 10, 2)->default(0)->comment('礼盒价格');
            $table->decimal('total_price', 10, 2)->default(0)->comment('总价');
            $table->string('currency_code', 3)->default('USD')->comment('货币代码');
            $table->text('notes')->nullable()->comment('备注');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_items');
    }
}; 