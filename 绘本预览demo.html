<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="YOUR_CSRF_TOKEN_HERE">
    <title>绘本预览 WebSocket Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        #connection-status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
        }
        #message-container {
            border: 1px solid #ddd;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            background-color: #f1f1f1;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .preview-container {
            display: none;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
        }
        .preview-container.show {
            display: block;
        }
        .preview-image {
            max-width: 100%;
            margin-bottom: 10px;
        }
        .tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: white;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .info-box {
            background-color: #e2f0fd;
            border-left: 4px solid #1976d2;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .command {
            background-color: #333;
            color: #fff;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>绘本预览 WebSocket Demo</h1>
    
    <div class="info-box">
        <h3>使用说明</h3>
        <p>本demo需要后端启动以下服务才能正常运行：</p>
        <ol>
            <li>
                <strong>启动队列处理器</strong>（处理绘本预览和AI换脸任务）:
                <div class="command">php artisan horizon</div>
                或使用Laravel原生队列（如果不使用Horizon）:
                <div class="command">php artisan queue:work redis --sleep=3 --tries=3 --timeout=300 --queue=high_priority_face_swap,face_swap,polling</div>
            </li>
            <li>
                <strong>启动Laravel Reverb WebSocket服务</strong>（处理实时消息）:
                <div class="command">php artisan reverb:start</div>
                服务将按照.env文件中的配置启动，默认端口是8080
            </li>
        </ol>
        <p>如果需要调试队列问题，可以添加<code>--verbose</code>参数查看详细日志。</p>
    </div>
    
    <div class="form-group">
        <label for="picbook-id">绘本ID:</label>
        <input type="number" id="picbook-id" placeholder="输入绘本ID" value="1">
    </div>
    
    <div class="form-group">
        <label for="api-endpoint">API端点:</label>
        <input type="text" id="api-endpoint" value="http://huiben.com/api/picbooks">
    </div>
    
    <div id="connection-status" class="disconnected">未连接</div>
    
    <div class="controls">
        <button id="request-preview-btn">请求预览</button>
        <button id="connect-btn">连接 WebSocket</button>
        <button id="disconnect-btn" disabled>断开连接</button>
    </div>

    <div class="request-params">
        <h3>请求参数:</h3>
        <pre id="request-params">
{
  "characters": [
    {
      "full_name": "Chris",
      "language": "zh",
      "gender": 1,
      "skincolor": 2,
      "photo": "admin_uploads/picbook/3/ai_face/a2a4903e-7642-434f-8fa2-88d313f40709.jpeg"
    }
  ]
}
        </pre>
    </div>
    
    <div class="tabs">
        <div class="tab active" data-tab="messages">WebSocket消息</div>
        <div class="tab" data-tab="preview">预览内容</div>
    </div>
    
    <div class="tab-content active" data-tab="messages">
        <h3>接收消息:</h3>
        <div id="message-container"></div>
    </div>
    
    <div class="tab-content" data-tab="preview">
        <h3>预览内容:</h3>
        <div id="preview-container" class="preview-container">
            <h4>预览状态: <span id="preview-status">未开始</span></h4>
            <div id="preview-content"></div>
        </div>
    </div>
    
    <script>
        // 配置变量
        const authToken = "4|zUKSzVRbJm0KOo4R4nCjK9bnICShdGerngbNoPvT9699886d";
        
        // 页面元素
        const picbookIdInput = document.getElementById("picbook-id");
        const apiEndpointInput = document.getElementById("api-endpoint");
        const requestPreviewBtn = document.getElementById("request-preview-btn");
        const connectBtn = document.getElementById("connect-btn");
        const disconnectBtn = document.getElementById("disconnect-btn");
        const connectionStatus = document.getElementById("connection-status");
        const messageContainer = document.getElementById("message-container");
        const previewContainer = document.getElementById("preview-container");
        const previewStatus = document.getElementById("preview-status");
        const previewContent = document.getElementById("preview-content");
        
        // 存储变量
        let previewId = null;
        let socket = null;
        let heartbeatInterval = null;
        
        // 选项卡切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有选项卡的active类
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                // 为当前选项卡添加active类
                this.classList.add('active');
                
                // 隐藏所有内容区域
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // 显示对应的内容区域
                const tabName = this.getAttribute('data-tab');
                document.querySelector(`.tab-content[data-tab="${tabName}"]`).classList.add('active');
            });
        });
        
        // 请求预览API
        async function requestPreview() {
            const picbookId = picbookIdInput.value;
            const apiEndpoint = apiEndpointInput.value;
            
            if (!picbookId) {
                alert("请输入绘本ID");
                return;
            }
            
            // 显示预览状态
            previewContainer.classList.add("show");
            previewStatus.textContent = "请求中...";
            previewContent.innerHTML = "";
            
            try {
                // 构建请求参数
                const requestData = {
                    characters: [
                        {
                            full_name: "Chris",
                            language: "zh",
                            gender: 1,
                            skincolor: 2,
                            photo: "admin_uploads/picbook/3/ai_face/a2a4903e-7642-434f-8fa2-88d313f40709.jpeg"
                        }
                    ]
                };
                
                // 发起API请求
                const response = await fetch(`${apiEndpoint}/${picbookId}/preview`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                // 记录API响应
                logMessage("API响应", JSON.stringify(result, null, 2));
                
                // 检查响应状态
                if (result.code == 200) {
                    // 成功响应，显示预览ID
                    previewStatus.textContent = "预览创建成功，状态: " + (result.data.status || "pending");
                    
                    // 存储预览ID
                    if (result.data.preview_id) {
                        previewId = result.data.preview_id;
                        previewContent.innerHTML = `<p>预览ID: ${previewId}</p>`;
                        
                        // 如果WebSocket已连接，订阅预览更新频道
                        if (socket && socket.readyState === WebSocket.OPEN) {
                            // 检查是否有换脸批次ID
                            if (result.data.face_swap_batch && result.data.face_swap_batch.batch_id) {
                                // 提取换脸批次ID和用户ID
                                const batchId = result.data.face_swap_batch.batch_id;
                                // 使用批次ID和用户ID订阅频道
                                subscribeToChannels(previewId, batchId);
                            } else {
                                // 如果没有换脸批次ID，只订阅用户频道
                                subscribeToChannels(previewId);
                            }
                            
                            // 显示换脸批次信息（如果有）
                            if (result.data.face_swap_batch) {
                                const batchInfo = result.data.face_swap_batch;
                                let batchHtml = '<div class="batch-info">';
                                batchHtml += `<h4>换脸批次信息</h4>`;
                                batchHtml += `<p>批次ID: ${batchInfo.batch_id || ''}</p>`;
                                batchHtml += `<p>总页数: ${batchInfo.total_pages || '未知'}</p>`;
                                batchHtml += `<p>状态: ${batchInfo.status || 'pending'}</p>`;
                                batchHtml += `<p>队列位置: ${batchInfo.queue_position || ''}</p>`;
                                batchHtml += `<p>预计等待时间: ${batchInfo.estimated_wait_time || ''}秒</p>`;
                                batchHtml += `<p>队列类型: ${batchInfo.queue_type === 'priority' ? '优先' : '普通'}</p>`;
                                batchHtml += '</div>';
                                
                                // 创建批次信息容器并添加到页面
                                const batchContainer = document.createElement('div');
                                batchContainer.id = 'batch-info-container';
                                batchContainer.className = 'batch-info-container';
                                batchContainer.innerHTML = batchHtml;
                                previewContent.appendChild(batchContainer);
                            }
                        }
                    }
                } else {
                    // 错误响应
                    previewStatus.textContent = "预览创建失败";
                    previewContent.innerHTML = `<p>错误: ${result.message || "未知错误"}</p>`;
                }
            } catch (error) {
                logMessage("请求错误", error.message || "未知错误");
                previewStatus.textContent = "请求失败";
                previewContent.innerHTML = `<p>错误: ${error.message || "未知错误"}</p>`;
            }
        }
        
        // 连接 WebSocket
        function connectWebSocket() {
            if (socket !== null) {
                return;
            }
            
            updateConnectionStatus("connecting", "正在连接...");
            
            // 构造WebSocket URL - 使用.env中配置的端口号(默认8080)
            const wsUrl = "ws://huiben.com:8080/app/reverb_app_key";
            
            // 创建 WebSocket 连接
            socket = new WebSocket(wsUrl);
            
            // 连接建立时的处理
            socket.onopen = function(event) {
                updateConnectionStatus("connected", "已连接");
                
                // 启用/禁用按钮
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                
                logMessage("WebSocket连接", "连接已建立");
                
                // 启动心跳计时器（每25秒发送一次心跳）
                clearInterval(heartbeatInterval);
                heartbeatInterval = setInterval(function() {
                    if (socket && socket.readyState === WebSocket.OPEN) {
                        logMessage("发送心跳", "保持连接活跃");
                        socket.send(JSON.stringify({
                            event: "pusher:ping",
                            data: {}
                        }));
                    }
                }, 25000);
                
                // 如果已有预览ID，自动订阅频道
                if (previewId) {
                    subscribeToChannels(previewId);
                }
            };
            
            // 接收消息的处理
            socket.onmessage = function(event) {
                try {
                    // 记录原始消息
                    logMessage("WebSocket原始消息", event.data);
                    
                    const data = JSON.parse(event.data);
                    
                    // 处理ping消息 - 需要回复pong以保持连接
                    if (data.event === "pusher:ping") {
                        logMessage("收到Ping", "正在回复Pong...");
                        // 回复pong消息
                        socket.send(JSON.stringify({
                            event: "pusher:pong",
                            data: {}
                        }));
                        return;
                    }
                    
                    logMessage("收到消息", JSON.stringify(data, null, 2));
                    
                    // 分析消息的事件名称和频道
                    let channelEvent = data.event;
                    let channel = data.channel;
                    
                    // 检查是否是队列状态消息，并检测队列长度变化
                    if (data.event === 'face-swap.queue-status' && data.channel === 'face-swap-queue-status') {
                        try {
                            const queueData = JSON.parse(data.data);
                            // 如果发现队列长度变为0，可能表示任务已完成但没收到通知
                            if (queueData.regular_queue_length === 0 && queueData.priority_queue_length === 0) {
                                logMessage("队列已清空", "任务可能已完成，将在5秒后模拟接收完成消息");
                                
                                // 5秒后模拟完成消息
                                setTimeout(function() {
                                    // 检查是否已经收到完成事件（通过查看页面上是否有换脸更新区域）
                                    const hasCompletionEvent = document.getElementById('face-swap-updates');
                                    
                                    if (!hasCompletionEvent) {
                                        logMessage("模拟完成消息", "未检测到完成事件，生成模拟完成消息");
                                        
                                        // 构造一个模拟的完成消息
                                        const simulatedEvent = {
                                            event: "face-swap.completed",
                                            channel: `face-swap.${7}`, // 使用固定的用户ID 7
                                            data: JSON.stringify({
                                                batch_id: previewId,
                                                user_id: 7,
                                                status: "completed",
                                                result_images: [],
                                                timestamp: new Date().toISOString(),
                                                message: "任务已完成（模拟消息）"
                                            })
                                        };
                                        
                                        // 处理模拟消息
                                        handlePreviewUpdate(simulatedEvent);
                                    }
                                }, 5000);
                            }
                        } catch (e) {
                            logMessage("解析队列数据失败", e.message);
                        }
                    }
                    
                    // 输出更多详细信息以便调试
                    if (data.event && data.channel) {
                        logMessage("消息详情", `频道: ${data.channel}, 事件: ${data.event}`);
                    }
                    
                    // 处理预览更新消息
                    handlePreviewUpdate(data);
                } catch (e) {
                    logMessage("收到消息 (非JSON格式)", event.data);
                }
            };
            
            // 错误处理
            socket.onerror = function(error) {
                logMessage("发生错误", error.message || "未知错误");
                updateConnectionStatus("disconnected", "连接错误");
            };
            
            // 连接关闭的处理
            socket.onclose = function(event) {
                logMessage("连接关闭", `代码: ${event.code}, 原因: ${event.reason || "未提供"}`);
                updateConnectionStatus("disconnected", "已断开");
                
                // 清除心跳计时器
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
                
                // 重置按钮状态
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                
                socket = null;
                
                // 连接意外断开时自动重连（排除正常关闭和主动断开的情况）
                if (event.code !== 1000) {
                    logMessage("重新连接", "3秒后尝试重新连接...");
                    setTimeout(connectWebSocket, 3000);
                }
            };
        }
        
        // 订阅频道（合并了原先的两个函数）
        function subscribeToChannels(previewId, batchId = null) {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                logMessage("无法订阅", "WebSocket未连接");
                return;
            }
            
            // 使用用户ID（这里写死为7）
            const userId = 7; // 写死为7用于调试
            
            // 订阅用户私有频道
            const userChannelMessage = {
                event: "pusher:subscribe",
                data: {
                    channel: `face-swap.${userId}`,
                    auth: authToken // 直接使用authToken作为认证
                }
            };
            
            socket.send(JSON.stringify(userChannelMessage));
            logMessage("订阅频道", `已订阅用户ID: ${userId} 的换脸频道`);
            
            // 如果提供了批次ID，则订阅批次频道
            if (batchId) {
                const batchChannelMessage = {
                    event: "pusher:subscribe",
                    data: {
                        channel: `face-swap-batch.${batchId}`,
                        auth: authToken // 直接使用authToken作为认证
                    }
                };
                
                socket.send(JSON.stringify(batchChannelMessage));
                logMessage("订阅频道", `已订阅批次ID: ${batchId} 的换脸频道`);
            }
            
            // 订阅全局换脸队列状态频道（公共频道，不需要认证）
            const queueStatusSubscribeMessage = {
                event: "pusher:subscribe",
                data: {
                    channel: `face-swap-queue-status`
                }
            };
            
            socket.send(JSON.stringify(queueStatusSubscribeMessage));
            logMessage("订阅频道", `已订阅换脸队列状态频道`);
        }
        
        // 处理预览更新
        function handlePreviewUpdate(data) {
            // 提取事件名和频道（如果有）
            const eventName = data.event || '';
            const channelName = data.channel || '';
            
            // 处理频道名称 - 不再需要移除private-前缀
            const normalizedChannelName = channelName;
            
            // 更详细的日志记录，帮助诊断问题
            logMessage("消息分析", `
                事件名: ${eventName}
                频道名: ${channelName}
                是否与face-swap相关: ${normalizedChannelName.includes('face-swap') || eventName.includes('face-swap')}
                是否为完成事件: ${eventName.includes('completed') || eventName.includes('complete')}
                是否为进度事件: ${eventName.includes('progress')}
            `);
            
            // 检查是否是预览更新消息
            if (eventName.startsWith('preview.') || normalizedChannelName.startsWith('preview.')) {
                try {
                    const previewData = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
                    
                    // 记录详细信息
                    logMessage("预览更新", `状态: ${previewData.status || "未知"}, 数据: ${JSON.stringify(previewData)}`);
                    
                    // 更新预览状态
                    previewStatus.textContent = `预览状态: ${previewData.status || "处理中"}`;
                    
                    // 切换到预览选项卡
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelector('.tab[data-tab="preview"]').classList.add('active');
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    document.querySelector('.tab-content[data-tab="preview"]').classList.add('active');
                    
                    // 确保预览容器可见
                    previewContainer.classList.add("show");
                    
                    // 处理不同类型的更新
                    if (previewData.status === 'completed' && previewData.pages && previewData.pages.length > 0) {
                        // 完成状态：显示预览页面
                        displayPreviewPages(previewData.pages);
                    } else if (previewData.status === 'failed') {
                        // 失败状态：显示错误信息
                        previewContent.innerHTML = `
                            <p>预览生成失败</p>
                            <p>错误信息: ${previewData.error || "未知错误"}</p>
                        `;
                    } else if (previewData.status === 'processing') {
                        // 处理中状态：显示进度
                        let processingHtml = '';
                        
                        if (previewData.progress !== undefined) {
                            processingHtml += `<p>处理进度: ${previewData.progress}%</p>`;
                            
                            // 添加进度条
                            processingHtml += `
                                <div style="width:100%; background-color:#f1f1f1; border-radius:4px; margin:10px 0;">
                                    <div style="width:${previewData.progress}%; background-color:#4CAF50; height:20px; border-radius:4px;">
                                        ${previewData.progress}%
                                    </div>
                                </div>
                            `;
                        }
                        
                        if (previewData.current_task) {
                            processingHtml += `<p>当前任务: ${previewData.current_task}</p>`;
                        }
                        
                        previewContent.innerHTML = processingHtml;
                    } else if (previewData.status === 'queued') {
                        // 排队中状态：显示队列信息
                        let queueHtml = '<p>预览任务正在排队中</p>';
                        
                        if (previewData.queue_position !== undefined) {
                            queueHtml += `<p>队列位置: ${previewData.queue_position}</p>`;
                        }
                        
                        if (previewData.estimated_wait_time !== undefined) {
                            queueHtml += `<p>预计等待时间: ${previewData.estimated_wait_time}秒</p>`;
                        }
                        
                        previewContent.innerHTML = queueHtml;
                    }
                } catch (e) {
                    logMessage("处理预览更新失败", e.message || "未知错误");
                }
            }
            // 更广泛地匹配换脸完成或进度消息
            // 增强匹配逻辑，捕获更多可能的事件格式
            else if ((eventName.includes('face-swap') && (eventName.includes('complet') || eventName.includes('progress'))) || 
                     (normalizedChannelName.includes('face-swap') && (eventName.includes('complet') || eventName.includes('progress'))) ||
                     eventName === 'app.face-swap.completed' || 
                     eventName === 'app.face-swap.batch-completed' ||
                     eventName === 'face-swap.completed') {
                try {
                    const faceSwapData = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
                    
                    // 记录详细信息
                    logMessage("换脸更新（捕获到事件）", `事件: ${eventName}, 频道: ${channelName}, 数据: ${JSON.stringify(faceSwapData)}`);
                    
                    // 进一步检查频道类型
                    const isBatchChannel = normalizedChannelName.includes('face-swap-batch');
                    const channelType = isBatchChannel ? '批次频道' : '用户频道';
                    
                    // 将换脸进度/结果信息添加到预览内容中
                    let faceSwapHtml = '<div class="face-swap-update">';
                    faceSwapHtml += `<h4>换脸任务更新 (${channelType})</h4>`;
                    
                    // 添加频道和事件信息
                    faceSwapHtml += `<p>频道: ${normalizedChannelName}</p>`;
                    faceSwapHtml += `<p>事件: ${eventName}</p>`;
                    
                    if (eventName.includes('complet')) {
                        faceSwapHtml += `<h4>换脸任务完成</h4>`;
                        faceSwapHtml += `<p>批次ID: ${faceSwapData.batch_id || ''}</p>`;
                        faceSwapHtml += `<p>状态: ${faceSwapData.status || 'completed'}</p>`;
                        
                        // 显示结果图像
                        if (faceSwapData.result_images && faceSwapData.result_images.length > 0) {
                            faceSwapHtml += `<p>结果图像:</p>`;
                            faceSwapData.result_images.forEach((imgUrl, index) => {
                                faceSwapHtml += `
                                    <div>
                                        <img src="${imgUrl}" style="max-width:100%; margin:10px 0;" alt="换脸结果 ${index + 1}">
                                    </div>
                                `;
                            });
                        } else {
                            // 如果没有结果图像（可能是模拟事件），添加消息
                            faceSwapHtml += `
                                <p>结果图像未返回，但任务已标记为完成。您可以在系统中查看结果。</p>
                                <p>如果这是自动模拟的消息，说明后端没有成功发送完成事件。</p>
                            `;
                        }
                    } else if (eventName.includes('progress')) {
                        faceSwapHtml += `<h4>换脸任务进度</h4>`;
                        faceSwapHtml += `<p>批次ID: ${faceSwapData.batch_id || ''}</p>`;
                        
                        // 显示进度
                        if (faceSwapData.progress !== undefined) {
                            faceSwapHtml += `<p>处理进度: ${faceSwapData.progress}%</p>`;
                            
                            // 添加进度条
                            faceSwapHtml += `
                                <div style="width:100%; background-color:#f1f1f1; border-radius:4px; margin:10px 0;">
                                    <div style="width:${faceSwapData.progress}%; background-color:#4CAF50; height:20px; border-radius:4px;">
                                        ${faceSwapData.progress}%
                                    </div>
                                </div>
                            `;
                        }
                        
                        // 显示已完成/总任务数
                        if (faceSwapData.completed !== undefined && faceSwapData.total !== undefined) {
                            faceSwapHtml += `<p>已完成: ${faceSwapData.completed}/${faceSwapData.total}</p>`;
                        }
                        
                        // 显示当前处理的页面
                        if (faceSwapData.current_page) {
                            faceSwapHtml += `<p>正在处理第 ${faceSwapData.current_page} 页</p>`;
                        }
                    }
                    
                    faceSwapHtml += `<p>时间戳: ${faceSwapData.timestamp || new Date().toLocaleTimeString()}</p>`;
                    
                    // 添加消息内容（如果有）
                    if (faceSwapData.message) {
                        faceSwapHtml += `<p>消息: ${faceSwapData.message}</p>`;
                    }
                    
                    faceSwapHtml += '</div>';
                    
                    // 创建换脸更新容器（如果不存在）
                    let faceSwapContainer = document.getElementById('face-swap-updates');
                    if (!faceSwapContainer) {
                        // 在预览选项卡添加换脸更新标题和容器
                        const previewTab = document.querySelector('.tab-content[data-tab="preview"]');
                        if (previewTab) {
                            const faceSwapTitle = document.createElement('h3');
                            faceSwapTitle.textContent = '换脸更新';
                            
                            faceSwapContainer = document.createElement('div');
                            faceSwapContainer.id = 'face-swap-updates';
                            faceSwapContainer.className = 'face-swap-updates';
                            
                            previewTab.appendChild(faceSwapTitle);
                            previewTab.appendChild(faceSwapContainer);
                        }
                    }
                    
                    // 添加更新（如果容器存在）
                    if (faceSwapContainer) {
                        faceSwapContainer.innerHTML = faceSwapHtml + faceSwapContainer.innerHTML;
                    }
                    
                } catch (e) {
                    logMessage("处理换脸更新失败", `错误: ${e.message || "未知错误"}, 原始数据: ${JSON.stringify(data)}`);
                }
            }
            // 处理队列状态消息
            else if (eventName.includes('queue-status') || normalizedChannelName.includes('queue-status')) {
                try {
                    const queueData = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
                    
                    // 记录详细信息
                    logMessage("队列状态更新", `事件: ${eventName}, 频道: ${normalizedChannelName}, 数据: ${JSON.stringify(queueData)}`);
                    
                    // 构建队列状态HTML
                    let queueStatusHtml = '<div class="queue-status-update">';
                    queueStatusHtml += `<h4>换脸队列状态</h4>`;
                    
                    // 显示队列信息
                    if (queueData.regular_queue_length !== undefined) {
                        queueStatusHtml += `<p>普通队列长度: ${queueData.regular_queue_length}</p>`;
                    }
                    
                    if (queueData.priority_queue_length !== undefined) {
                        queueStatusHtml += `<p>优先队列长度: ${queueData.priority_queue_length}</p>`;
                    }
                    
                    if (queueData.total_queue_length !== undefined) {
                        queueStatusHtml += `<p>总队列长度: ${queueData.total_queue_length}</p>`;
                    }
                    
                    // 显示当前任务状态
                    if (queueData.queue_position !== undefined) {
                        queueStatusHtml += `<p>队列位置: ${queueData.queue_position}</p>`;
                    }
                    
                    if (queueData.estimated_wait_time !== undefined) {
                        queueStatusHtml += `<p>预计等待时间: ${queueData.estimated_wait_time}秒</p>`;
                    }
                    
                    if (queueData.queue_type) {
                        queueStatusHtml += `<p>队列类型: ${queueData.queue_type === 'priority' ? '优先' : '普通'}</p>`;
                    }
                    
                    queueStatusHtml += `<p>更新时间: ${queueData.timestamp || new Date().toLocaleTimeString()}</p>`;
                    queueStatusHtml += '</div>';
                    
                    // 添加到预览内容
                    let queueContainer = document.getElementById('queue-status-container');
                    if (!queueContainer) {
                        queueContainer = document.createElement('div');
                        queueContainer.id = 'queue-status-container';
                        queueContainer.className = 'queue-status-container';
                        
                        // 在页面上添加队列状态容器
                        const tabContent = document.querySelector('.tab-content[data-tab="preview"]');
                        if (tabContent) {
                            // 创建队列状态标题
                            const queueTitle = document.createElement('h3');
                            queueTitle.textContent = '队列状态';
                            tabContent.appendChild(queueTitle);
                            tabContent.appendChild(queueContainer);
                        }
                    }
                    
                    // 更新队列状态内容
                    queueContainer.innerHTML = queueStatusHtml;
                    
                } catch (e) {
                    logMessage("处理队列状态更新失败", e.message || "未知错误");
                }
            } else {
                // 添加额外的日志以捕获无法识别的消息类型
                logMessage("未识别的消息类型", `
                    事件名: ${eventName}
                    频道名: ${channelName} 
                    完整消息: ${JSON.stringify(data)}
                `);
            }
        }
        
        // 显示预览页面
        function displayPreviewPages(pages) {
            let pagesHtml = '<div class="preview-pages">';
            
            pages.forEach((page, index) => {
                pagesHtml += `
                    <div class="preview-page">
                        <h4>第 ${index + 1} 页</h4>
                        ${page.image_url ? `<img src="${page.image_url}" class="preview-image" alt="预览图片 ${index + 1}">` : ''}
                        ${page.text ? `<p>${page.text}</p>` : ''}
                    </div>
                `;
            });
            
            pagesHtml += '</div>';
            previewContent.innerHTML = pagesHtml;
        }
        
        // 断开 WebSocket 连接
        function disconnectWebSocket() {
            if (socket !== null) {
                // 清除心跳计时器
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
                
                socket.close();
            }
        }
        
        // 更新连接状态显示
        function updateConnectionStatus(status, message) {
            connectionStatus.className = status;
            connectionStatus.textContent = message;
        }
        
        // 日志消息
        function logMessage(title, content) {
            const messageDiv = document.createElement("div");
            messageDiv.className = "message";
            
            const timestamp = new Date().toLocaleTimeString();
            
            messageDiv.innerHTML = `
                <strong>${timestamp} - ${title}</strong>
                <pre>${content}</pre>
            `;
            
            messageContainer.appendChild(messageDiv);
            messageContainer.scrollTop = messageContainer.scrollHeight;
        }
        
        // 按钮事件监听
        requestPreviewBtn.addEventListener("click", requestPreview);
        connectBtn.addEventListener("click", connectWebSocket);
        disconnectBtn.addEventListener("click", disconnectWebSocket);
    </script>
</body>
</html> 