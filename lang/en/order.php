<?php

return [
    // 订单创建
    'created' => 'Order created successfully',
    'create_failed' => 'Failed to create order',
    'create_error' => 'Order creation error',
    
    // 订单状态
    'status' => [
        'pending' => 'Pending Payment',
        'processing' => 'Book Preparation',
        'preparing' => 'Book Production',
        'confirmed' => 'Confirmed',
        'printed' => 'Printed',
        'packed' => 'Packed',
        'shipped' => 'Shipped',
        'delivered' => 'Delivered',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
        'refunded' => 'Refunded'
    ],
    
    // 支付状态
    'payment_status' => [
        'pending' => 'Pending Payment',
        'paid' => 'Paid',
        'failed' => 'Payment Failed',
        'refunded' => 'Refunded',
        'partially_refunded' => 'Partially Refunded'
    ],
    
    // 订单操作
    'confirmed' => 'Order confirmed successfully',
    'confirm_failed' => 'Failed to confirm order',
    'cancelled' => 'Order cancelled successfully',
    'cancel_failed' => 'Failed to cancel order',
    'updated' => 'Order updated successfully',
    'update_failed' => 'Failed to update order',
    'message_updated' => 'Order message updated successfully',
    'address_updated' => 'Order address updated successfully',
    
    // 订单查询
    'not_found' => 'Order not found',
    'access_denied' => 'You do not have permission to access this order',
    'invalid' => 'Invalid order',
    'invalid_status' => 'Invalid order status',
    'list_empty' => 'No orders found',
    'detail_success' => 'Order details retrieved successfully',
    'list_success' => 'Order list retrieved successfully',
    
    // 支付相关
    'payment_created' => 'Payment created successfully',
    'payment_create_failed' => 'Failed to create payment',
    'payment_success' => 'Payment successful',
    'payment_failed' => 'Payment failed',
    'payment_timeout' => 'Payment timeout',
    'payment_cancelled' => 'Payment cancelled',
    'payment_error' => 'Payment error',
    
    // 物流相关
    'shipping_methods_success' => 'Shipping methods retrieved successfully',
    'shipping_cost_calculated' => 'Shipping cost calculated successfully',
    'shipping_cost_validation_failed' => 'Shipping cost validation failed, please recalculate shipping cost',
    'shipping_address_required' => 'Shipping address is required',
    'invalid_shipping_method' => 'Invalid shipping method',
    'shipping_method_updated' => 'Shipping method updated successfully',
    'shipping_service_unavailable' => 'Shipping service is temporarily unavailable. Please try again later or contact support.',
    'shipping_calculation_error' => 'Unable to calculate shipping cost at this time. Please try again later.',
    
    // 购物车相关
    'cart_items_required' => 'Cart items are required',
    'invalid_cart_items' => 'Invalid cart items found, please remove them and try again',
    'cart_empty' => 'Cart is empty, cannot create order',
    
    // 地址相关
    'address_required' => 'Address information is required',
    'invalid_address' => 'Invalid address information',
    'address_not_found' => 'Address not found or access denied',
    
    // 时间限制
    'message_update_expired' => 'Order created more than 4 hours ago, cannot modify message',
    'auto_confirm_pending' => 'Order will be automatically confirmed after 48 hours',
    'confirm_period_expired' => 'Confirmation period has expired',
    
    // 处理进度
    'processing_progress' => 'Order processing progress retrieved successfully',
    'tracking_info' => 'Tracking information retrieved successfully',
    'no_tracking_number' => 'No tracking number available for this order',
    
    // 配置相关
    'physical_config_success' => 'Physical configuration retrieved successfully',
    
    // 错误消息
    'processing_error' => 'Order processing error',
    'system_error' => 'System error',
    'permission_denied' => 'Permission denied',
    'validation_failed' => 'Validation failed',
    'operation_failed' => 'Operation failed',
    'data_error' => 'Data error',
    'access_denied' => 'Access denied',
    'invalid_status' => 'Invalid status',
    'payment_requires_action' => 'Payment requires further action',

    
    // 通用消息
    'success' => 'Success',
    'failed' => 'Failed',
    'processing' => 'Processing...',
    'please_wait' => 'Please wait...',
    
    // 新增翻译键
    'cancel_reason' => 'Cancel reason',
    'invalid_status_transition' => 'Cannot change order status from :current to :new',
    'address_field_required' => 'Address field :field is required',
    'invalid_country_code' => 'Country code must be 2 characters',
    'invalid_phone_format' => 'Invalid phone number format',
    'cannot_repay' => 'This order cannot be repaid',
    'payment_intent_created' => 'Payment intent created successfully',
    
    // 物流方式
    'shipping_methods' => [
        'standard' => [
            'name' => 'Standard Shipping',
            'description' => 'Standard international shipping'
        ],
        'express' => [
            'name' => 'Express Shipping',
            'description' => 'Express international shipping'
        ]
    ]
];
