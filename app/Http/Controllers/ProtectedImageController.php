<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;

class ProtectedImageController extends Controller
{
    /**
     * 获取处理后的图片
     */
    public function getProcessedImage($uid, string $filename)
    {
        if($uid != Auth::id()){
            return response()->json([
                'success' => false,
                'message' => __('messages.user_not_match')
            ], 403); // 使用403而不是404，因为这是权限问题
        }
        
        // 构建完整路径
        $path = $this->getImagePath($uid, $filename);
        if (!$path) {
            return response()->json([
                'success' => false,
                'message' => __('messages.invalid_image_path')
            ], 400); // 使用400表示请求参数错误
        }
        
        // 检查文件是否存在
        if (!Storage::disk('public')->exists($path)) {
            return response()->json([
                'success' => false,
                'message' => __('messages.image_not_found')
            ], 404);
        }
        
        // 获取文件完整路径
        $fullPath = Storage::disk('public')->path($path);
        
        // 获取文件MIME类型
        $mimeType = mime_content_type($fullPath);
        
        // 返回文件响应
        return Response::file($fullPath, [
            'Content-Type' => $mimeType,
            'Cache-Control' => 'public, max-age=86400' // 24小时缓存
        ]);
    }
    
    /**
     * 获取图片路径
     */
    private function getImagePath($uid, $filename)
    {
        if (strpos($filename, 'processed_') === 0) {
            return 'processed/' . $uid . '/' . $filename;
        }
        
        if (strpos($filename, 'merged_') === 0) {
            return 'merged/' . $uid . '/' . $filename;
        }
        
        if (strpos($filename, 'face_') === 0) {
            return 'faces/' . $uid . '/' . $filename;
        }
        
        return null;
    }
}