<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\SimpleFaceSwapService;

class ProcessHighPriorityFaceSwapBatch implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 批次ID
     */
    protected $batchId;

    /**
     * 任务尝试次数
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 600; // 高优先级任务给更长的超时时间

    /**
     * 创建任务实例
     * 
     * @param string $batchId 批次ID
     */
    public function __construct(string $batchId)
    {
        $this->batchId = $batchId;
        
        // 设置为高优先级队列
        $this->onQueue('high_priority_face_swap');
    }

    /**
     * 执行任务
     * 
     * @param SimpleFaceSwapService $faceSwapService 换脸服务
     */
    public function handle(SimpleFaceSwapService $faceSwapService)
    {
        try {
            Log::info('开始处理高优先级AI换脸批次', [
                'batch_id' => $this->batchId,
                'queue' => 'high_priority_face_swap'
            ]);

            // 处理批次中的任务
            $result = $faceSwapService->processBatch($this->batchId);

            Log::info('处理高优先级批次任务完成', [
                'batch_id' => $this->batchId,
                'success' => $result['success']
            ]);
            
        } catch (\Exception $e) {
            Log::error('处理高优先级AI换脸批次失败', [
                'batch_id' => $this->batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     * 
     * @param \Throwable $exception
     */
    public function failed(\Throwable $exception)
    {
        Log::error('高优先级AI换脸批次任务最终失败', [
            'batch_id' => $this->batchId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // 更新批次状态为失败
        try {
            \App\Models\AiFaceTask::where('batch_id', $this->batchId)
                ->where('type', 'batch')
                ->update([
                    'status' => 'failed',
                    'error_message' => $exception->getMessage()
                ]);
        } catch (\Exception $e) {
            Log::error('更新失败批次状态时出错', [
                'batch_id' => $this->batchId,
                'error' => $e->getMessage()
            ]);
        }
    }
}