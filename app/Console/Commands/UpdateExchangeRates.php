<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CurrencyService;

class UpdateExchangeRates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'currency:update-rates {--clear-cache : Clear existing cache before updating}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update exchange rates from API and cache them';

    protected $currencyService;

    public function __construct(CurrencyService $currencyService)
    {
        parent::__construct();
        $this->currencyService = $currencyService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始更新汇率...');

        if ($this->option('clear-cache')) {
            $this->info('清除现有汇率缓存...');
            $this->currencyService->clearCache();
        }

        // 定义需要更新的汇率对
        $baseCurrency = 'CNY';
        $targetCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'];

        $this->info("从 {$baseCurrency} 更新到以下币种的汇率:");
        
        $successCount = 0;
        $failCount = 0;

        foreach ($targetCurrencies as $currency) {
            try {
                $rate = $this->currencyService->getExchangeRate($baseCurrency, $currency);
                $this->line("✅ {$baseCurrency} -> {$currency}: {$rate}");
                $successCount++;
            } catch (\Exception $e) {
                $this->error("❌ {$baseCurrency} -> {$currency}: {$e->getMessage()}");
                $failCount++;
            }
        }

        // 反向汇率（从其他币种到CNY）
        $this->info("\n更新反向汇率:");
        foreach ($targetCurrencies as $currency) {
            try {
                $rate = $this->currencyService->getExchangeRate($currency, $baseCurrency);
                $this->line("✅ {$currency} -> {$baseCurrency}: {$rate}");
                $successCount++;
            } catch (\Exception $e) {
                $this->error("❌ {$currency} -> {$baseCurrency}: {$e->getMessage()}");
                $failCount++;
            }
        }

        // 主要币种之间的汇率
        $this->info("\n更新主要币种间汇率:");
        $majorCurrencies = ['USD', 'EUR', 'GBP'];
        
        foreach ($majorCurrencies as $from) {
            foreach ($majorCurrencies as $to) {
                if ($from !== $to) {
                    try {
                        $rate = $this->currencyService->getExchangeRate($from, $to);
                        $this->line("✅ {$from} -> {$to}: {$rate}");
                        $successCount++;
                    } catch (\Exception $e) {
                        $this->error("❌ {$from} -> {$to}: {$e->getMessage()}");
                        $failCount++;
                    }
                }
            }
        }

        $this->info("\n汇率更新完成!");
        $this->info("成功: {$successCount} 个汇率对");
        if ($failCount > 0) {
            $this->warn("失败: {$failCount} 个汇率对");
        }

        return $failCount > 0 ? 1 : 0;
    }
}
