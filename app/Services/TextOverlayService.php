<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class TextOverlayService
{
    /**
     * 在寄语页面底图上添加文字
     *
     * @param int $previewId 预览ID
     * @param string $text 要添加的文字
     * @param array $config 文字配置
     * @return array 处理结果
     */
    public function addTextToDedicationPage(int $previewId, string $text, array $config = []): array
    {
        try {
            Log::info('开始处理寄语页面文字添加', [
                'preview_id' => $previewId,
                'text' => $text,
                'config' => $config
            ]);
            
            // 获取预览记录
            $preview = \App\Models\PicbookPreview::find($previewId);
            if (!$preview) {
                return [
                    'success' => false,
                    'message' => '预览记录不存在'
                ];
            }
            
            // 获取底图路径
            $baseImagePath = $this->getDedicationBaseImage($preview);
            if (!$baseImagePath) {
                return [
                    'success' => false,
                    'message' => '无法获取底图'
                ];
            }
            
            // 从page变体获取文字配置
            $pageVariantConfig = $this->getPageVariantTextConfig($preview);
            
            // 合并用户配置和page变体配置
            $textConfig = $this->mergeTextConfig(array_merge($pageVariantConfig, $config));
            $textConfig['text'] = $text; // 确保文字内容被正确设置
            
            // 在底图上添加文字
            $resultImagePath = $this->addTextToImage($baseImagePath, $text, $textConfig);
            
            if (!$resultImagePath) {
                return [
                    'success' => false,
                    'message' => '文字添加失败'
                ];
            }
            
            // 生成结果图片URL
            $resultImageUrl = Storage::disk('s3_faceswap')->url($resultImagePath);
            
            Log::info('寄语页面文字添加完成', [
                'preview_id' => $previewId,
                'result_image_path' => $resultImagePath,
                'result_image_url' => $resultImageUrl,
                'text_config' => $textConfig
            ]);
            
            return [
                'success' => true,
                'message' => '文字添加成功',
                'result_images' => [$resultImageUrl],
                'processed_at' => now()->toISOString()
            ];
            
        } catch (\Exception $e) {
            Log::error('处理寄语页面文字添加失败', [
                'preview_id' => $previewId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '文字添加异常: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 从page变体获取文字配置
     *
     * @param \App\Models\PicbookPreview $preview 预览记录
     * @return array 文字配置
     */
    private function getPageVariantTextConfig(\App\Models\PicbookPreview $preview): array
    {
        try {
            // 获取寄语页面的page变体
            $dedicationPage = \App\Models\PicbookPage::where('picbook_id', $preview->picbook_id)
                ->where('has_replaceable_text', 2)
                ->first();
            
            if (!$dedicationPage) {
                Log::warning('未找到寄语页面', [
                    'preview_id' => $preview->id,
                    'picbook_id' => $preview->picbook_id
                ]);
                return [];
            }
            
            // 获取匹配的变体
            $variant = $dedicationPage->variants()
                ->where('language', $preview->language)
                ->where('gender', $preview->gender)
                ->whereJsonContains('character_skincolors', $preview->skin_color)
                ->first();
            
            if (!$variant) {
                // 如果没有找到匹配的变体，使用默认变体
                $variant = $dedicationPage->variants()->first();
            }
            
            if (!$variant) {
                Log::warning('未找到寄语页面变体', [
                    'preview_id' => $preview->id,
                    'page_id' => $dedicationPage->id
                ]);
                return [];
            }
            
            // 从变体中获取文字配置
            $textConfig = $variant->text_config ?? [];
            
            Log::info('从page变体获取文字配置', [
                'preview_id' => $preview->id,
                'page_id' => $dedicationPage->id,
                'variant_id' => $variant->id,
                'text_config' => $textConfig
            ]);
            
            return $textConfig;
            
        } catch (\Exception $e) {
            Log::error('获取page变体文字配置失败', [
                'preview_id' => $preview->id,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * 获取寄语页面底图
     *
     * @param \App\Models\PicbookPreview $preview 预览记录
     * @return string|null 底图路径
     */
    private function getDedicationBaseImage(\App\Models\PicbookPreview $preview): ?string
    {
        try {
            // 从预览配置中获取底图路径
            $baseImage = $preview->base_image ?? null;
            
            if ($baseImage) {
                // 如果是完整URL，转换为相对路径
                if (filter_var($baseImage, FILTER_VALIDATE_URL)) {
                    $baseImage = parse_url($baseImage, PHP_URL_PATH);
                    $baseImage = ltrim($baseImage, '/');
                }
                
                // 检查文件是否存在（先检查s3_faceswap，再检查默认存储）
                if (Storage::disk('s3_faceswap')->exists($baseImage) || Storage::exists($baseImage)) {
                    return $baseImage;
                }
            }
            
            // 如果没有配置底图，尝试从page变体获取
            $dedicationPage = \App\Models\PicbookPage::where('picbook_id', $preview->picbook_id)
                ->where('has_replaceable_text', 2)
                ->first();
            
            if ($dedicationPage) {
                // 获取匹配的变体
                $variant = $dedicationPage->variants()
                    ->where('language', $preview->language)
                    ->where('gender', $preview->gender)
                    ->whereJsonContains('character_skincolors', $preview->skin_color)
                    ->first();
                
                if (!$variant) {
                    $variant = $dedicationPage->variants()->first();
                }
                
                if ($variant && !empty($variant->image_url)) {
                    $variantImage = $variant->image_url;
                    
                    // 如果是完整URL，转换为相对路径
                    if (filter_var($variantImage, FILTER_VALIDATE_URL)) {
                        $variantImage = parse_url($variantImage, PHP_URL_PATH);
                        $variantImage = ltrim($variantImage, '/');
                    }
                    
                    if (Storage::disk('s3_faceswap')->exists($variantImage) || Storage::exists($variantImage)) {
                        return $variantImage;
                    }
                }
            }
            
            // 如果还是没有找到，使用默认底图
            $defaultBaseImage = 'dedication_pages/default_base.png';
            if (Storage::disk('s3_faceswap')->exists($defaultBaseImage) || Storage::exists($defaultBaseImage)) {
                return $defaultBaseImage;
            }
            
            Log::warning('无法找到寄语页面底图', [
                'preview_id' => $preview->id,
                'configured_base_image' => $baseImage,
                'default_base_image' => $defaultBaseImage
            ]);
            
            return null;
            
        } catch (\Exception $e) {
            Log::error('获取寄语页面底图失败', [
                'preview_id' => $preview->id,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * 合并文字配置
     *
     * @param array $config 用户配置
     * @return array 合并后的配置
     */
    private function mergeTextConfig(array $config): array
    {
        // 默认文字配置（如果page变体中没有配置，则使用这些默认值）
        $defaultConfig = [
            'font_size' => 24,
            'font_color' => '#000000',
            'font_family' => 'Arial',
            'position_x' => 50,
            'position_y' => 50,
            'max_width' => 400,
            'line_height' => 1.5,
            'alignment' => 'center',
            'background_color' => null,
            'background_opacity' => 0,
            'padding' => 10,
            'border_radius' => 0,
        ];
        
        // 限制文本长度
        if (isset($config['text']) && strlen($config['text']) > 500) {
            $config['text'] = substr($config['text'], 0, 500);
        }
        
        // 限制字体大小
        if (isset($config['font_size'])) {
            $config['font_size'] = max(12, min(48, $config['font_size']));
        }
        
        // 合并配置
        return array_merge($defaultConfig, $config);
    }
    
    /**
     * 在图片上添加文字
     *
     * @param string $imagePath 图片路径
     * @param string $text 文字内容
     * @param array $config 文字配置
     * @return string|null 处理后的图片路径
     */
    private function addTextToImage(string $imagePath, string $text, array $config): ?string
    {
        try {
            // 创建图片管理器
            $manager = new ImageManager(new Driver());
            
            // 直接加载图片（imagePath已经是有效的文件路径）
            $image = $manager->read($imagePath);
            
            // 处理长文本换行
            $lines = $this->wrapText($text, $config['max_width'], $config['font_size']);
            
            // 计算文字总高度
            $lineHeight = $config['font_size'] * $config['line_height'];
            $totalHeight = count($lines) * $lineHeight;
            
            // 计算起始位置
            $startY = $config['position_y'] - ($totalHeight / 2);
            
            // 逐行添加文字
            foreach ($lines as $index => $line) {
                $y = $startY + ($index * $lineHeight) + ($config['font_size'] / 2);
                
                // 如果需要背景
                if ($config['background_color']) {
                    $this->addTextBackground($image, $line, $config['position_x'], $y, $config);
                }
                
                // 添加文字
                $image->text($line, $config['position_x'], $y, function ($font) use ($config) {
                    $font->size($config['font_size']);
                    $font->color($config['font_color']);
                    $font->align($config['alignment']);
                    $font->valign('middle');
                    
                    // 设置字体（如果支持）
                    if ($config['font_family'] && function_exists('imagettftext')) {
                        $font->file($this->getFontPath($config['font_family']));
                    }
                });
            }
            
            // 生成保存路径 - 保存到processed目录，与其他换脸处理结果一致
            $filename = 'processed/dedication_' . uniqid() . '_' . time() . '.png';
            $savePath = $filename;
            
            // 保存图片到 S3
            Storage::disk('s3_faceswap')->put($savePath, $image->toPng());
            
            Log::info('文字添加成功', [
                'original_path' => $imagePath,
                'save_path' => $savePath,
                'text_lines' => count($lines),
                'storage_disk' => 's3_faceswap'
            ]);
            
            return $savePath;
            
        } catch (\Exception $e) {
            Log::error('图片文字添加失败', [
                'image_path' => $imagePath,
                'text' => $text,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }
    
    /**
     * 文本换行处理
     *
     * @param string $text 文本内容
     * @param int $maxWidth 最大宽度
     * @param int $fontSize 字体大小
     * @return array 换行后的文本数组
     */
    private function wrapText(string $text, int $maxWidth, int $fontSize): array
    {
        // 简单的换行逻辑，按字符数分割
        $maxChars = floor($maxWidth / ($fontSize * 0.6)); // 估算每个字符的宽度
        
        if (mb_strlen($text) <= $maxChars) {
            return [$text];
        }
        
        $lines = [];
        $words = explode(' ', $text);
        $currentLine = '';
        
        foreach ($words as $word) {
            $testLine = $currentLine ? $currentLine . ' ' . $word : $word;
            
            if (mb_strlen($testLine) <= $maxChars) {
                $currentLine = $testLine;
            } else {
                if ($currentLine) {
                    $lines[] = $currentLine;
                }
                $currentLine = $word;
            }
        }
        
        if ($currentLine) {
            $lines[] = $currentLine;
        }
        
        return $lines;
    }
    
    /**
     * 添加文字背景
     *
     * @param \Intervention\Image\Image $image 图片对象
     * @param string $text 文字内容
     * @param int $x X坐标
     * @param int $y Y坐标
     * @param array $config 配置
     */
    private function addTextBackground($image, string $text, int $x, int $y, array $config): void
    {
        // 简单的背景添加逻辑
        // 这里可以根据需要扩展更复杂的背景处理
        
        // 注意：Intervention Image v3 的API可能有所不同
        // 这里只是一个示例实现
    }
    
    /**
     * 获取字体文件路径
     *
     * @param string $fontFamily 字体名称
     * @return string 字体文件路径
     */
    private function getFontPath(string $fontFamily): string
    {
        // 映射字体名称到实际字体文件
        $fontMap = [
            'Arial' => public_path('fonts/Arial-Regular.ttf'),
            'Arial Bold' => public_path('fonts/Arial-Bold.ttf'),
            'SimHei' => public_path('fonts/SimHei.ttf'),
            // 可以添加更多字体映射
        ];
        
        return $fontMap[$fontFamily] ?? public_path('fonts/Arial-Regular.ttf');
    }
}