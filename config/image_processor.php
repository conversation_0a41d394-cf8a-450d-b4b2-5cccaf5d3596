<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Advanced Image Processor Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the Node.js-based advanced image processing service
    |
    */

    'service' => [
        'url' => env('IMAGE_PROCESSOR_URL', 'http://localhost:3001'),
        'timeout' => env('IMAGE_PROCESSOR_TIMEOUT', 120),
        'max_retries' => env('IMAGE_PROCESSOR_MAX_RETRIES', 3),
    ],

    'canvas' => [
        'default_width' => 1200,
        'default_height' => 1600,
        'max_width' => 4000,
        'max_height' => 6000,
        'default_background' => '#ffffff',
    ],

    'text' => [
        'default_font' => 'Arial',
        'default_size' => 16,
        'default_color' => '#000000',
        'max_size' => 200,
        'supported_fonts' => [
            'Arial',
            'Helvetica',
            'Times New Roman',
            'Georgia',
            'Verdana',
            'Courier New',
        ],
        'supported_weights' => [
            'normal',
            'bold',
            'lighter',
            'bolder',
            '100', '200', '300', '400', '500', '600', '700', '800', '900'
        ],
        'supported_alignments' => [
            'left',
            'center',
            'right'
        ]
    ],

    'filters' => [
        'brightness' => [
            'min' => -100,
            'max' => 100,
            'default' => 0
        ],
        'contrast' => [
            'min' => -100,
            'max' => 100,
            'default' => 0
        ],
        'saturation' => [
            'min' => -100,
            'max' => 100,
            'default' => 0
        ],
        'hue' => [
            'min' => -180,
            'max' => 180,
            'default' => 0
        ]
    ],

    'files' => [
        'max_size' => 50 * 1024 * 1024, // 50MB
        'max_count' => 20,
        'allowed_types' => [
            'image/png',
            'image/jpeg',
            'image/jpg',
            'image/webp'
        ],
        'output_format' => 'png',
        'output_quality' => 90
    ],

    'storage' => [
        'temp_dir' => storage_path('app/temp/image_processor'),
        'output_dir' => storage_path('app/processed_images'),
        'cleanup_after' => 24 * 60 * 60, // 24 hours in seconds
    ],

    'presets' => [
        'skin_tones' => [
            'light' => [
                'brightness' => 10,
                'saturation' => 5,
                'hue' => 0
            ],
            'medium' => [
                'brightness' => 0,
                'saturation' => 0,
                'hue' => 0
            ],
            'brown' => [
                'brightness' => -31,
                'saturation' => 2,
                'hue' => -4
            ],
            'dark' => [
                'brightness' => -115,
                'contrast' => -48,
                'saturation' => 0,
                'hue' => 0
            ]
        ],
        'hair_colors' => [
            'blonde' => [
                'brightness' => 20,
                'saturation' => 10,
                'hue' => 15
            ],
            'brown' => [
                'brightness' => -31,
                'saturation' => 2,
                'hue' => -4
            ],
            'black' => [
                'brightness' => -115,
                'contrast' => -48,
                'saturation' => 0,
                'hue' => 0
            ],
            'red' => [
                'brightness' => -10,
                'saturation' => 20,
                'hue' => -15
            ]
        ]
    ],

    'performance' => [
        'enable_caching' => true,
        'cache_ttl' => 3600, // 1 hour
        'max_concurrent_requests' => 5,
        'queue_processing' => true
    ],

    'logging' => [
        'enabled' => true,
        'level' => env('IMAGE_PROCESSOR_LOG_LEVEL', 'info'),
        'log_requests' => true,
        'log_responses' => false, // Set to true for debugging
        'log_performance' => true
    ]
];