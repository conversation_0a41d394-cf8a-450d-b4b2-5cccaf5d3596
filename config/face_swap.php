<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Face Swap Configuration
    |--------------------------------------------------------------------------
    |
    | 换脸功能相关配置
    |
    */

    // 队列配置
    'queues' => [
        'normal' => 'face_swap',
        'high_priority' => 'high_priority_face_swap',
    ],

    // 处理配置
    'processing' => [
        'max_retries' => 3,
        'timeout' => 300, // 5分钟
        'delay_between_tasks' => 2, // 任务间延迟秒数
        'average_processing_time' => 120, // 平均处理时间（秒）
    ],

    // 限制配置
    'limits' => [
        'max_preview_per_day' => 50,
        'max_concurrent_tasks' => 5,
        'max_batch_size' => 20,
    ],

    // 缓存配置
    'cache' => [
        'prefix' => 'simple_face_swap:',
        'ttl' => 3600, // 1小时
    ],

    // API配置
    'api' => [
        'base_url' => env('FACE_SWAP_API_URL', 'https://www.runninghub.cn'),
        'workflow_id' => env('FACE_SWAP_WORKFLOW_ID', ''),
        'upload_endpoint' => '/task/openapi/upload',
        'create_endpoint' => '/task/openapi/create',
        'status_endpoint' => '/task/openapi/outputs',
        'max_poll_attempts' => 45,
        'poll_interval' => 10, // 轮询间隔秒数
    ],

    // 图片处理配置
    'image' => [
        'max_file_size' => 10 * 1024 * 1024, // 10MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif'],
        'resolutions' => [
            'high_res' => ['width' => 2048, 'height' => 2048],
            'standard' => ['width' => 1024, 'height' => 1024],
            'low_res' => ['width' => 512, 'height' => 512],
        ],
    ],

    // WebSocket配置
    'websocket' => [
        'enabled' => env('WEBSOCKET_ENABLED', true),
        'channels' => [
            'user_prefix' => 'user.',
        ],
        'events' => [
            'task_completed' => 'face-swap.task.completed',
            'task_failed' => 'face-swap.task.failed',
            'batch_completed' => 'face-swap.batch.completed',
        ],
    ],

    // 通知配置
    'notifications' => [
        'enabled' => true,
        'channels' => ['websocket', 'database'],
    ],
];