<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PicbookCoverVariant extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'picbook_id',
        'language',
        'gender',
        'skincolor',
        'image_url',
        'skin_mask_url',
        'has_text',
        'text_config',
        'has_face',
        'face_config',
        'is_published',
        'sort_order',
        'price',
        'pricesymbol',
        'currencycode',
        'is_default'
    ];

    protected $casts = [
        'gender' => 'integer',
        'skincolor' => 'integer',
        'text_config' => 'array',
        'face_config' => 'array',
        'has_text' => 'boolean',
        'has_face' => 'boolean',
        'is_published' => 'boolean',
        'sort_order' => 'integer',
        'price' => 'float',
        'is_default' => 'boolean'
    ];

    /**
     * 获取关联的绘本
     */
    public function picbook()
    {
        return $this->belongsTo(Picbook::class, 'picbook_id');
    }
    
    
    /**
     * 获取无肤色基础图URL
     */
    public function getNoneSkinUrlAttribute()
    {
        return null;
    }

    /**
     * 本地化范围查询
     */
    public function scopeLocalized($query, $language = null, $gender = null, $skincolor = null)
    {
        $language = $language ?? app()->getLocale();
        $gender = $gender ?? 1;
        $skincolor = $skincolor ?? 1;

        return $query->where([
            'language' => $language,
            'gender' => $gender,
            'skincolor' => $skincolor
        ]);
    }
    
    /**
     * 已发布的封面变体范围
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }
} 