<?php

/**
 * Stripe集成测试脚本
 * 
 * 这个脚本用于测试Stripe支付集成的各个组件
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Models\Order;
use App\Services\StripePaymentService;

echo "🧪 开始Stripe集成测试...\n\n";

// 测试1: Order模型的Stripe方法
echo "1️⃣ 测试Order模型的Stripe方法\n";
$order = new Order();
$order->user_id = 1;
$order->order_number = 'TEST' . time();
$order->total_amount = 29.99;
$order->currency_code = 'USD';
$order->payment_method = 'stripe';

echo "   ✅ isStripePayment(): " . ($order->isStripePayment() ? 'true' : 'false') . "\n";

$order->setStripeSession('cs_test_123', 'pi_test_456', ['test' => 'data']);
echo "   ✅ setStripeSession() 成功\n";
echo "   ✅ Stripe session ID: " . $order->stripe_session_id . "\n";
echo "   ✅ Stripe payment intent ID: " . $order->stripe_payment_intent_id . "\n";

// 测试2: 配置检查
echo "\n2️⃣ 测试配置\n";
$stripeKey = config('stripe.key');
$stripeSecret = config('stripe.secret');

echo "   ✅ Stripe公钥配置: " . ($stripeKey ? '已配置' : '未配置') . "\n";
echo "   ✅ Stripe密钥配置: " . ($stripeSecret ? '已配置' : '未配置') . "\n";

// 测试3: 数据库字段
echo "\n3️⃣ 测试数据库字段\n";
try {
    $columns = \Illuminate\Support\Facades\Schema::getColumnListing('orders');
    $stripeFields = [
        'stripe_session_id',
        'stripe_payment_intent_id', 
        'stripe_customer_id',
        'stripe_metadata',
        'stripe_webhook_received_at'
    ];
    
    foreach ($stripeFields as $field) {
        $exists = in_array($field, $columns);
        echo "   " . ($exists ? '✅' : '❌') . " {$field}: " . ($exists ? '存在' : '不存在') . "\n";
    }
} catch (Exception $e) {
    echo "   ❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 测试4: 路由检查
echo "\n4️⃣ 测试API路由\n";
$routes = [
    '/api/stripe/public-key' => 'GET',
    '/api/stripe/webhook' => 'POST',
];

foreach ($routes as $route => $method) {
    $url = 'http://huiben.com' . $route;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $status = ($httpCode >= 200 && $httpCode < 400) ? '✅' : '❌';
    echo "   {$status} {$method} {$route}: HTTP {$httpCode}\n";
}

echo "\n🎉 Stripe集成测试完成！\n";
echo "\n📋 下一步操作:\n";
echo "   1. 在.env文件中配置真实的Stripe密钥\n";
echo "   2. 在Stripe控制台配置Webhook端点\n";
echo "   3. 测试完整的支付流程\n";