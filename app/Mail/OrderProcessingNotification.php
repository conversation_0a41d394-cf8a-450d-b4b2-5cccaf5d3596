<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\Order;

class OrderProcessingNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $order;
    public $user;
    public $progress;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order, array $progress = [])
    {
        $this->order = $order;
        $this->user = $order->user;
        $this->progress = $progress;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('订单处理进度更新 - ' . $this->order->order_number)
            ->view('emails.orders.processing')
            ->with([
                'order' => $this->order,
                'user' => $this->user,
                'progress' => $this->progress,
            ]);
    }
}