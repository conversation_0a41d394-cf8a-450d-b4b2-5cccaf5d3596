# 简化的Stripe PaymentIntent支付流程

## 🎯 核心理念

订单创建时自动生成PaymentIntent，用户可以随时支付，无需额外的创建步骤。

## 📋 支付流程

### 1. 订单创建（自动生成PaymentIntent）
用户创建订单时，系统自动为待支付订单创建PaymentIntent。

### 2. 获取支付信息
通过订单详情API获取PaymentIntent信息。

### 3. 前端支付处理
使用Stripe.js处理支付。

### 4. 确认支付
后端验证并更新订单状态。

## 🔗 API端点

### 核心端点

| 端点 | 方法 | 说明 |
|------|------|------|
| `/api/order/detail/{id}` | GET | 获取订单详情（包含PaymentIntent） |
| `/api/stripe/confirm-payment` | POST | 确认支付成功 |
| `/api/stripe/check-payment-status` | POST | 检查支付状态 |
| `/api/stripe/cancel-payment` | POST | 取消支付 |
| `/api/stripe/payment-config` | GET | 获取支付配置 |

### 订单详情响应示例

```json
{
  "success": true,
  "message": "订单详情获取成功",
  "data": {
    "order": {
      "id": 123,
      "order_number": "ORD-20250129-001",
      "status": "pending",
      "payment_status": "pending",
      "total_amount": 29.99,
      "currency": "USD",
      "permissions": {
        "can_pay": true
      }
    },
    "payment_data": {
      "client_secret": "pi_1234567890_secret_abcdef",
      "payment_intent_id": "pi_1234567890",
      "publishable_key": "pk_test_xxx",
      "amount": 2999,
      "currency": "usd",
      "is_existing": false
    }
  }
}
```

## 🌐 前端集成

### 简化的JavaScript实现

```javascript
class SimpleStripePayment {
  constructor() {
    this.stripe = null;
    this.cardElement = null;
  }

  // 初始化支付表单
  async initPaymentForm(orderId) {
    try {
      // 1. 获取订单详情和支付配置
      const [orderResponse, configResponse] = await Promise.all([
        fetch(`/api/order/detail/${orderId}`, {
          headers: { 'Authorization': `Bearer ${this.getToken()}` }
        }),
        fetch('/api/stripe/payment-config')
      ]);

      const orderData = await orderResponse.json();
      const configData = await configResponse.json();

      if (!orderData.success || !configData.success) {
        throw new Error('获取支付信息失败');
      }

      // 检查是否需要支付
      if (!orderData.data.payment_data) {
        console.log('订单无需支付或已支付');
        return false;
      }

      // 2. 初始化Stripe
      this.stripe = Stripe(configData.data.publishable_key);
      const elements = this.stripe.elements({
        appearance: configData.data.appearance
      });

      // 3. 创建卡片元素
      this.cardElement = elements.create('card');
      this.cardElement.mount('#card-element');

      // 4. 存储支付数据
      this.paymentData = orderData.data.payment_data;
      this.orderData = orderData.data.order;

      return true;
    } catch (error) {
      console.error('初始化支付表单失败:', error);
      return false;
    }
  }

  // 处理支付
  async processPayment(billingDetails = {}) {
    if (!this.stripe || !this.cardElement || !this.paymentData) {
      throw new Error('支付未初始化');
    }

    try {
      // 1. 确认支付
      const result = await this.stripe.confirmCardPayment(
        this.paymentData.client_secret,
        {
          payment_method: {
            card: this.cardElement,
            billing_details: {
              name: billingDetails.name || 'Customer',
              email: billingDetails.email || ''
            }
          }
        }
      );

      if (result.error) {
        throw new Error(result.error.message);
      }

      // 2. 通知后端支付成功
      const confirmResponse = await fetch('/api/stripe/confirm-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getToken()}`
        },
        body: JSON.stringify({
          payment_intent_id: result.paymentIntent.id,
          order_id: this.orderData.id
        })
      });

      const confirmResult = await confirmResponse.json();

      if (!confirmResult.success) {
        throw new Error(confirmResult.message || '支付确认失败');
      }

      return {
        success: true,
        paymentIntent: result.paymentIntent,
        order: confirmResult.data.order
      };

    } catch (error) {
      console.error('支付处理失败:', error);
      throw error;
    }
  }

  // 获取认证令牌
  getToken() {
    return localStorage.getItem('auth_token') || '';
  }
}

// 使用示例
const payment = new SimpleStripePayment();

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', async () => {
  const orderId = new URLSearchParams(window.location.search).get('order_id');
  
  if (orderId) {
    const initialized = await payment.initPaymentForm(orderId);
    
    if (initialized) {
      // 绑定支付按钮
      document.getElementById('pay-button').addEventListener('click', async () => {
        try {
          const billingDetails = {
            name: document.getElementById('cardholder-name').value,
            email: document.getElementById('email').value
          };
          
          const result = await payment.processPayment(billingDetails);
          
          if (result.success) {
            alert('支付成功！');
            // 跳转到成功页面
            window.location.href = `/order-success?order_id=${result.order.id}`;
          }
        } catch (error) {
          alert('支付失败: ' + error.message);
        }
      });
    }
  }
});
```

### React Hook示例

```javascript
import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';

export const useStripePayment = (orderId) => {
  const [stripe, setStripe] = useState(null);
  const [cardElement, setCardElement] = useState(null);
  const [paymentData, setPaymentData] = useState(null);
  const [orderData, setOrderData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 初始化支付
  useEffect(() => {
    if (!orderId) return;

    const initPayment = async () => {
      try {
        setLoading(true);
        
        // 获取订单详情和支付配置
        const [orderResponse, configResponse] = await Promise.all([
          fetch(`/api/order/detail/${orderId}`, {
            headers: { 'Authorization': `Bearer ${getAuthToken()}` }
          }),
          fetch('/api/stripe/payment-config')
        ]);

        const orderResult = await orderResponse.json();
        const configResult = await configResponse.json();

        if (!orderResult.success || !configResult.success) {
          throw new Error('获取支付信息失败');
        }

        // 检查是否需要支付
        if (!orderResult.data.payment_data) {
          setError('订单无需支付或已支付');
          return;
        }

        // 初始化Stripe
        const stripeInstance = await loadStripe(configResult.data.publishable_key);
        const elements = stripeInstance.elements({
          appearance: configResult.data.appearance
        });

        const card = elements.create('card');

        setStripe(stripeInstance);
        setCardElement(card);
        setPaymentData(orderResult.data.payment_data);
        setOrderData(orderResult.data.order);

      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    initPayment();
  }, [orderId]);

  // 处理支付
  const processPayment = async (billingDetails = {}) => {
    if (!stripe || !cardElement || !paymentData) {
      throw new Error('支付未初始化');
    }

    try {
      // 确认支付
      const result = await stripe.confirmCardPayment(
        paymentData.client_secret,
        {
          payment_method: {
            card: cardElement,
            billing_details
          }
        }
      );

      if (result.error) {
        throw new Error(result.error.message);
      }

      // 通知后端
      const confirmResponse = await fetch('/api/stripe/confirm-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          payment_intent_id: result.paymentIntent.id,
          order_id: orderData.id
        })
      });

      const confirmResult = await confirmResponse.json();

      if (!confirmResult.success) {
        throw new Error(confirmResult.message || '支付确认失败');
      }

      return {
        success: true,
        paymentIntent: result.paymentIntent,
        order: confirmResult.data.order
      };

    } catch (error) {
      throw error;
    }
  };

  return {
    stripe,
    cardElement,
    paymentData,
    orderData,
    loading,
    error,
    processPayment
  };
};

// 获取认证令牌
const getAuthToken = () => {
  return localStorage.getItem('auth_token') || '';
};
```

## 🔄 支付状态处理

### 自动状态检查

```javascript
// 定期检查支付状态
const checkPaymentStatus = async (paymentIntentId) => {
  try {
    const response = await fetch('/api/stripe/check-payment-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: JSON.stringify({
        payment_intent_id: paymentIntentId
      })
    });

    const result = await response.json();
    
    if (result.success) {
      return result.data.status;
    }
    
    throw new Error(result.message);
  } catch (error) {
    console.error('检查支付状态失败:', error);
    return null;
  }
};

// 轮询支付状态
const pollPaymentStatus = (paymentIntentId, callback, maxAttempts = 10) => {
  let attempts = 0;
  
  const poll = async () => {
    if (attempts >= maxAttempts) {
      callback({ status: 'timeout', error: '支付状态检查超时' });
      return;
    }
    
    const status = await checkPaymentStatus(paymentIntentId);
    
    if (status === 'succeeded') {
      callback({ status: 'succeeded' });
    } else if (status === 'canceled' || status === 'failed') {
      callback({ status, error: '支付失败或已取消' });
    } else {
      attempts++;
      setTimeout(poll, 2000); // 2秒后重试
    }
  };
  
  poll();
};
```

## 🧪 测试流程

### 1. 创建测试订单
```bash
curl -X POST http://localhost:8000/api/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "cart_item_ids": [1, 2],
    "shipping_address": {...},
    "payment_method": "stripe"
  }'
```

### 2. 获取订单详情
```bash
curl -X GET http://localhost:8000/api/order/detail/123 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 前端支付测试
使用测试卡号：`4242424242424242`

### 4. 确认支付
```bash
curl -X POST http://localhost:8000/api/stripe/confirm-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "payment_intent_id": "pi_xxx",
    "order_id": 123
  }'
```

## ✅ 优势

1. **简化流程** - 订单创建时自动生成PaymentIntent
2. **减少API调用** - 无需单独创建PaymentIntent
3. **更好的用户体验** - 支付信息随订单详情一起获取
4. **延迟支付支持** - 用户可以随时回来完成支付
5. **状态同步** - 自动处理支付状态更新

这个简化的流程更符合实际的电商支付场景，减少了不必要的API调用，提供了更流畅的用户体验。