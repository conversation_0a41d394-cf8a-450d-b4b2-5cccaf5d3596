<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ExpService;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ExpDebugController extends Controller
{
    use ApiResponse;

    protected $expService;

    public function __construct(ExpService $expService)
    {
        $this->expService = $expService;
    }

    /**
     * 测试4PX API连接
     */
    public function testConnection()
    {
        try {
            $config = [
                'base_url' => config('services.4px.base_url'),
                'app_key' => config('services.4px.app_key'),
                'app_secret' => config('services.4px.app_secret') ? '已配置' : '未配置',
                'environment' => str_contains(config('services.4px.base_url'), 'test') ? 'test' : 'production'
            ];

            return $this->success($config, '4PX配置信息获取成功');
        } catch (\Exception $e) {
            return $this->error('配置获取失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 测试签名生成
     */
    public function testSignature(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'method' => 'required|string',
                'data' => 'array'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', $validator->errors(), 422);
            }

            $method = $request->input('method');
            $data = $request->input('data', []);

            // 生成测试签名
            $timestamp = time() * 1000;
            $requestData = [
                'app_key' => config('services.4px.app_key'),
                'timestamp' => $timestamp,
                'method' => $method,
                'format' => 'json',
                'v' => '1.1.0',
            ];

            // 使用反射调用私有方法进行测试
            $reflection = new \ReflectionClass($this->expService);
            $generateSignMethod = $reflection->getMethod('generateSign');
            $generateSignMethod->setAccessible(true);

            $sign = $generateSignMethod->invoke($this->expService, $requestData, $data);

            return $this->success([
                'request_data' => $requestData,
                'data' => $data,
                'signature' => $sign,
                'sign_string' => $this->buildSignString($requestData, $data),
                'timestamp' => $timestamp
            ], '签名生成成功');
        } catch (\Exception $e) {
            return $this->error('签名生成失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 创建测试物流订单
     */
    public function createTestOrder(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_data' => 'required|array'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', $validator->errors(), 422);
            }

            $testOrderData = $request->input('order_data');

            Log::info('创建测试订单', ['order_data' => $testOrderData]);

            $result = $this->expService->createLogisticsOrder($testOrderData, true);

            return $this->success([
                'request_data' => $testOrderData,
                'response' => $result
            ], '测试订单创建成功');
        } catch (\Exception $e) {
            Log::error('创建测试订单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->input('order_data')
            ]);

            return $this->error('创建测试订单失败: ' . $e->getMessage(), [
                'request_data' => $request->input('order_data')
            ], 500);
        }
    }

    /**
     * 查询物流订单
     */
    public function queryOrder(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'request_no' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', $validator->errors(), 422);
            }

            $requestNo = $request->input('request_no');

            Log::info('查询物流订单', ['request_no' => $requestNo]);

            $result = $this->expService->getLogisticsOrderInfo($requestNo, true);

            return $this->success([
                'request_no' => $requestNo,
                'response' => $result
            ], '订单查询成功');
        } catch (\Exception $e) {
            Log::error('查询订单失败', [
                'error' => $e->getMessage(),
                'request_no' => $request->input('request_no')
            ]);

            return $this->error('查询订单失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 查询运费
     */
    public function queryFreight(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'request_no' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', $validator->errors(), 422);
            }

            $requestNo = $request->input('request_no');

            Log::info('查询运费', ['request_no' => $requestNo]);

            $result = $this->expService->getFreight($requestNo);

            return $this->success([
                'request_no' => $requestNo,
                'response' => $result
            ], '运费查询成功');
        } catch (\Exception $e) {
            Log::error('查询运费失败', [
                'error' => $e->getMessage(),
                'request_no' => $request->input('request_no')
            ]);

            return $this->error('查询运费失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 批量测试API接口
     */
    public function batchTest()
    {
        $results = [];
        $testCases = [
            'connection' => '连接测试',
            'signature' => '签名测试',
            'create_order' => '创建订单测试'
        ];

        foreach ($testCases as $test => $description) {
            try {
                switch ($test) {
                    case 'connection':
                        $result = $this->testConnection();
                        break;
                    case 'signature':
                        $request = new Request(['method' => 'ds.xms.order.create', 'data' => []]);
                        $result = $this->testSignature($request);
                        break;
                    case 'create_order':
                        $result = $this->createTestOrder(new Request());
                        break;
                }

                $results[$test] = [
                    'description' => $description,
                    'status' => 'success',
                    'result' => $result->getData()
                ];
            } catch (\Exception $e) {
                $results[$test] = [
                    'description' => $description,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }

        return $this->success($results, '批量测试完成');
    }

    /**
     * 构建签名字符串（用于调试）
     */
    private function buildSignString($requestData, $data)
    {
        ksort($requestData);
        $signStr = '';
        foreach ($requestData as $key => $value) {
            $signStr .= $key . $value;
        }
        $signStr .= json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . config('services.4px.app_secret');

        return $signStr;
    }

    /**
     * 清理调试日志
     */
    public function clearDebugLogs()
    {
        try {
            $logFile = storage_path('logs/laravel.log');
            if (file_exists($logFile)) {
                file_put_contents($logFile, '');
            }

            return $this->success(null, '调试日志已清理');
        } catch (\Exception $e) {
            return $this->error('清理日志失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 获取最近的调试日志
     */
    public function getDebugLogs(Request $request)
    {
        try {
            $lines = $request->input('lines', 50);
            $logFile = storage_path('logs/laravel.log');

            if (!file_exists($logFile)) {
                return $this->success([], '日志文件不存在');
            }

            $logs = [];
            $handle = fopen($logFile, 'r');
            if ($handle) {
                $logLines = [];
                while (($line = fgets($handle)) !== false) {
                    $logLines[] = $line;
                }
                fclose($handle);

                // 获取最后N行
                $logs = array_slice($logLines, -$lines);
            }

            return $this->success([
                'logs' => $logs,
                'total_lines' => count($logs)
            ], '调试日志获取成功');
        } catch (\Exception $e) {
            return $this->error('获取日志失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 测试下拉接口
     */
    public function testDropdownApis(Request $request)
    {
        try {
            $results = [];

            // 测试各种下拉接口
            $dropdownApis = [
                // 'countries' => '获取国家列表',
                'logistics_products' => '获取物流产品列表'
            ];

            foreach ($dropdownApis as $api => $description) {
                try {
                    switch ($api) {
                        case 'countries':
                            $result = $this->expService->getCountryList(true);
                            break;
                        case 'logistics_products':
                            $result = $this->expService->getLogisticsProducts(['transport_mode' => 1], true);
                            break;
                    }

                    $results[$api] = [
                        'description' => $description,
                        'status' => 'success',
                        'data' => $result
                    ];
                } catch (\Exception $e) {
                    $results[$api] = [
                        'description' => $description,
                        'status' => 'error',
                        'error' => $e->getMessage()
                    ];
                }
            }

            return $this->success($results, '下拉接口测试完成');
        } catch (\Exception $e) {
            return $this->error('下拉接口测试失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 测试运费计算
     */
    public function testFreightCalculation(Request $request)
    {
        try {
            // $validator = Validator::make($request->all(), [
            //     'data' => 'required|array',
            //     'data.country_code' => 'required|string|size:2',
            //     'data.weight' => 'required|string',
            //     'data.length' => 'nullable|string',
            //     'data.width' => 'nullable|string',
            //     'data.height' => 'nullable|string',
            //     'data.cargocode' => 'nullable|string',
            //     'data.logistics_product_code' => 'nullable|array'
            // ]);

            // if ($validator->fails()) {
            //     return $this->error('参数验证失败', $validator->errors(), 422);
            // }

            // $params = $request->input('data');

            // 设置默认值
            // $params = array_merge([
            //     'request_no' => '',
            //     'country_code' => 'US',
            //     'weight' => '1',
            //     'length' => '100',
            //     'width' => '100',
            //     'height' => '100',
            //     'cargocode' => 'P',
            //     'logistics_product_code' => []
            // ], $params);

            // Log::info('测试运费计算', ['params' => $params]);
            $params = $request->input('data');
            $result = $this->expService->calculateFreight($params, true);

            return $this->success([
                'params' => $params,
                'result' => json_decode($result)
            ], '运费计算测试成功');
        } catch (\Exception $e) {
            Log::error('运费计算测试失败', [
                'error' => $e->getMessage(),
                'params' => $request->input('data')
            ]);

            return $this->error('运费计算测试失败: ' . $e->getMessage(), [
                'params' => $request->input('data')
            ], 500);
        }
    }

    /**
     * 测试更新预报重量
     */
    public function testUpdateWeight(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'request_no' => 'required|string',
                'weight' => 'required|numeric|min:1'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', $validator->errors(), 422);
            }

            $result = $this->expService->updateWeight(
                $request->request_no,
                $request->weight,
                true
            );

            return $this->success([
                'request_no' => $request->request_no,
                'weight' => $request->weight,
                'result' => $result
            ], '更新预报重量测试成功');
        } catch (\Exception $e) {
            return $this->error('更新预报重量测试失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 验证签名算法
     */
    public function verifySignatureAlgorithm(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'method' => 'required|string',
                'data' => 'required|array',
                'expected_sign' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', $validator->errors(), 422);
            }

            $method = $request->input('method');
            $data = $request->input('data');
            $expectedSign = $request->input('expected_sign');

            // 构建请求参数
            $timestamp = time() * 1000;
            $requestData = [
                'app_key' => config('services.4px.app_key'),
                'timestamp' => $timestamp,
                'method' => $method,
                'format' => 'json',
                'v' => '1.1.0',
            ];

            // 验证签名
            $signatureInfo = $this->expService->verifySignature($requestData, $data, $expectedSign);

            return $this->success([
                'signature_info' => $signatureInfo,
                'config' => [
                    'app_key' => config('services.4px.app_key'),
                    'app_secret' => config('services.4px.app_secret') ? '已配置' : '未配置',
                    'base_url' => config('services.4px.base_url')
                ]
            ], '签名验证完成');
        } catch (\Exception $e) {
            return $this->error('签名验证失败: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * 对比create-test-order和yfjs的签名差异
     */
    public function compareSignatures(Request $request)
    {
        try {
            $testData = [
                'request_no' => 'TEST' . time(),
                'country_code' => 'US',
                'weight' => '500'
            ];

            $results = [];

            // 测试create-test-order方法的签名
            $createOrderData = [
                'request_no' => $testData['request_no'],
                'consignee_name' => 'Test User',
                'consignee_phone' => '1234567890',
                'consignee_address' => [
                    'country_code' => $testData['country_code'],
                    'city' => 'New York',
                    'street' => '123 Test St'
                ]
            ];

            $results['create_order'] = $this->expService->verifySignature(
                [
                    'app_key' => config('services.4px.app_key'),
                    'timestamp' => time() * 1000,
                    'method' => 'ds.xms.order.create',
                    'format' => 'json',
                    'v' => '1.1.0',
                ],
                $createOrderData
            );

            // 测试yfjs方法的签名（运费计算）
            $freightData = [
                'request_no' => '',
                'country_code' => $testData['country_code'],
                'weight' => $testData['weight'],
                'length' => '100',
                'width' => '100',
                'height' => '100',
                'cargocode' => 'P',
                'logistics_product_code' => []
            ];

            $results['freight_calculation'] = $this->expService->verifySignature(
                [
                    'app_key' => config('services.4px.app_key'),
                    'timestamp' => time() * 1000,
                    'method' => 'ds.xms.estimated_cost.get',
                    'format' => 'json',
                    'v' => '1.1.0',
                ],
                $freightData
            );

            return $this->success([
                'comparison' => $results,
                'analysis' => [
                    'create_order_sign_length' => strlen($results['create_order']['calculated_sign']),
                    'freight_calc_sign_length' => strlen($results['freight_calculation']['calculated_sign']),
                    'signs_match' => $results['create_order']['calculated_sign'] === $results['freight_calculation']['calculated_sign'],
                    'data_size_difference' => strlen(json_encode($createOrderData)) - strlen(json_encode($freightData))
                ]
            ], '签名对比完成');
        } catch (\Exception $e) {
            return $this->error('签名对比失败: ' . $e->getMessage(), null, 500);
        }
    }
}
